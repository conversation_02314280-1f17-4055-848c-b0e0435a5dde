/*
* Copyright (c) 2017.7，南京华乘电气科技有限公司
* All rights reserved.
*
* syncserver.h
*
* 初始版本：1.0
* 作者：wujun
* 创建日期：2017年07月24日
* 摘要：包含同步业务服务端的处理流程

* 当前版本：1.0
*/
#ifndef __SYNCSERVER_H_
#define __SYNCSERVER_H_

#include "processdata.h"
#include "protocoldefine.h"
#include <QThread>
#include <QFile>


class SyncManager;

class SyncServer: public QObject
{
    Q_OBJECT

public:
    /*************************************************
    函数名： SyncServer
    输入参数： pMain--同步管理器
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    SyncServer(SyncManager *pMain);

    /*************************************************
    函数名： ~SyncServer
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 析构函数
    *************************************************************/
    ~SyncServer();

signals:
    void sigProcDbData();

    void sigSendFile();

private slots:
    void onProcDbData();

    /*************************************************
    函数名： sendDbFile
    输入参数： NULL
    输出参数： NULL
    返回值： 结果状态--true表示文件发送成功
    功能： 发送数据库文件
    *************************************************************/
    bool sendDbFile();

public:

    //StationOutlineInfo getStationInfo();

    /*************************************************
    函数名： getCmdType
    输入参数： data--数据内容
    输出参数： NULL
    返回值： 命令字
    功能： 获取命令字类型
    *************************************************************/
    CommandType getCmdType(const QByteArray &data);

    /************************************************
     * 函数名:  procConnectRequest
     * 输入参数:  data--连接请求内容
     * 输出参数:  NULL
     * 返回值:  处理结果--true表示成功
     * 功能:   处理连接请求
     ************************************************/
    bool procConnectRequest(const QByteArray &data);

    /************************************************
     * 函数名:  procConfigRequest
     * 输入参数:  data--配置请求内容
     * 输出参数:  NULL
     * 返回值:  处理结果--true表示成功
     * 功能:   处理配置请求
     ************************************************/
    bool procConfigRequest(const QByteArray &data);

    /************************************************
     * 函数名:  procDbInfoRequest
     * 输入参数:  data--请求内容
     * 输出参数:  NULL
     * 返回值:  处理结果--true表示成功
     * 功能:   处理数据信息请求
     ************************************************/
    bool procDbInfoRequest(const QByteArray &data);

    /************************************************
     * 函数名:  procDbDataRequest
     * 输入参数:  data--请求内容
     * 输出参数:  NULL
     * 返回值:  处理结果--true表示成功
     * 功能:   处理取数据请求
     ************************************************/
    bool procDbDataRequest(const QByteArray &data);

    /************************************************
     * 函数名:  procDbFileHeadRequest
     * 输入参数:  data--请求内容
     * 输出参数:  NULL
     * 返回值:  处理结果--true表示成功
     * 功能:   处理数据文件头请求
     ************************************************/
    bool procDbFileHeadRequest(const QByteArray &data);

    bool procDbFileContentRequest(const QByteArray &data);

    void addToBuffer(const QByteArray &data);

    /************************************************
     * 函数名:  stopSync
     * 输入参数:  NULL
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:   设置停止标识为true（同步接口）
     ************************************************/
    void stopSync();

    /************************************************
     * 函数名:  procCustSyncDataRequest
     * 输入参数:  data--请求内容
     * 输出参数:  NULL
     * 返回值:  处理结果--true表示成功
     * 功能:   处理取数据请求
     ************************************************/
    bool procCustSyncDataRequest(const QByteArray &data);

    /************************************************
     * 函数名:  getPrtcParam
     * 输入参数:  NULL
     * 输出参数:  NULL
     * 返回值:  通讯协议参数
     * 功能: 获取通讯协议参数
     ************************************************/
    ProtocolParam getPrtcParam();

    /************************************************
     * 函数名:  closeFile
     * 输入参数:  NULL
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能: 关闭当前已打开的文件
     ************************************************/
    void closeFile();

    // 是否通过密码验证
    bool m_bConnected;

private:
    SyncManager *m_pMain;           // 同步管理器指针
    DataProcesser m_dataProcesser;  // 数据组包、解析器
    ProtocolParam m_ptParam;        // 通讯协议参数
    QThread m_thread;
    StationOutlineInfo m_stationInfo;   // 站点概要信息

    uint m_syncTotal;               // for test
    uint m_failNum;

    volatile bool m_bStopSync;      // true表示停止同步

    QList<QByteArray> m_dataBuff;   // 数据缓冲

    QFile *m_pLocalFile;
    quint64 m_nTotalSize;


};

#endif

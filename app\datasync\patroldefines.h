/*
* Copyright (c) 2017.7，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：patroldefines.h
*
* 初始版本：1.0
* 创建日期：2017年07月05日
* 摘要：智能巡检业务相关结构定义
*/
#ifndef __PATROLDEFINES_H_
#define __PATROLDEFINES_H_

#include <QObject>
#include <QDateTime>
#include <QVector>
/*************常用数据结构定义*******************/
namespace PatrolServiceNS{
//通信的远端目标类型
enum RemoteDestnationType
{
    NONE_DSTTYPE = -1,      // 初始化项
    HOST_DSTTYPE = 1,       //主机
    CLOUD_DSTTYPE,          //云服务
    PDST_DSTTYPE,           //PDST
    UDISK_DSTTYPE,          //U盘
};

enum DataDestnationType        // 同步数据的数据源
{
    DST_TYPE_HOST = 1,      // 主机
    DST_TYPE_SENSOR,        // 传感器
};

//测试数据类型
enum DataType
{
    UHF_TYPE = 1,       //UHF
    HFCT_TYPE,          //HFCT
    TEV_TYPE,           //TEV
    AE_TYPE,            //AE
    LIGHTING_ARRESTER,  //避雷器
    VIBRATOR_TYPE,      //振动
    MECHANICAL_PROPERTY,//机械特性
    ENVIRONMENT_TYPE,   //环境
};

enum ProgressTrigger
{
    TRIGGER_DOWNLOAD_CONFIG,                // 下载配置
    TRIGGER_SYNC_DATA,                      // 同步数据
    TRIGGER_UPLOAD_DATA,                    // 上传数据
};

//后台工作状态
enum BGWorkingState
{
    BG_IDLE = 0,        //后台处于空闲状态
    BG_DOWNLOAD_CFG,    //后台正在下载配置
    BG_SYNC_DATA,       //后台正在同步数据
    BG_UPLOAD_DATA,     //后台正在上传数据
};

enum StatusSource
{
    STATUS_SOURCE_UDISK,                    // U盘插入状态
    STATUS_SOURCE_WIFI,                     // wifi打开状态
    STATUS_SOURCE_4G,                       // 4G网络打开状态
    STATUS_SOURCE_BLUETOOTH,                // 蓝牙打开状态
};

enum Status
{
    STATUS_ON,                              // 打开（插入）状态
    STATUS_OFF,                             // 关闭（拔出）状态
};

//业务处理状态信息
enum ProgressStateCode
{
    PROGRESS_MIN = 0,       //正常进度的最小值
    PROGRESS_MAX = 100,     //正常进度的最大值
    CONNECTION_ERROR = -1,  //链路连接错误
    DST_REPLY_ERROR = -2,   //目的应答错误
};

enum SyncDataType                  // 数据同步类型
{
    SYNC_TYPE_ALL,                          // 完全同步
    SYNC_TYPE_CUSTOM,                       // 自定义同步
};

enum TestType                      // 测试类型
{
    AE_AMP,                        // AE幅值
    AE_WAVE,                       // AE波形
    TEV_AMP,                       // TEV幅值
    UHF_PRPS,
    HFCT_PRPS,
};
/*********************************用于数据展示的结构体*******************************************/
// 测点信息 用于展示
struct TestPointInfo
{
    QString s_testPointName;    //测点名称
    DataType eDataType;         //通道类型
    QString s_testPointID;      //测点ID
    DataType s_eDataType;       //测试类型
};

//一次设备信息
struct PrimaryDeviceInfo
{
    QString s_deviceName;       //设备名称
    QString s_deviceID;         //设备标识
    QList<TestPointInfo> s_frontEndList; //设备下的前端列表
};

//测试数据信息
struct TestDataInfo
{
    QString s_testDataName;     //测试数据名称
    unsigned int s_uiID;        //测试数据ID  TODO
};
//定位到指定通道的信息 用于筛选
struct DataLocateInfo
{
    QString s_stationName;      //通道所属站点名
    QString s_deviceName;       //通道所属设备名
    QString s_testPointName;    //通道所属测点名
    QString s_testPointID;      //测点ID
};

/************************站点相关信息********************************/
//站点概要信息 只包括站点自身信息，不包括下级信息
struct StationOutlineInfo
{
    QString s_stationName;      //站点名称
    QString s_stationID;        //站点外部编号
};

//站点信息
struct StationInfo
{
    StationOutlineInfo s_outlineInfo;      //站点概要信息
    QString s_strVoltage;    //站点电压等级
    QString s_strID;         //站点内部编号
    QList<PrimaryDeviceInfo> s_deviceList;  //一次设备列表
};

/************************参数相关**********************************/

struct SyncDataParam               // 数据同步操作参数
{
    DataDestnationType eDstType;            // 数据源
    SyncDataType eSyncType;                 // 同步类型(如果取值SYNC_TYPE_ALL，则忽略后面参数)
    QVector<PatrolServiceNS::DataType> vecDataType;          // 同步的数据类型
    StationInfo stationInfo;                //被选中的设备、前端信息
    QDateTime timeBegin;                    // 数据起始时间
    QDateTime timeEnd;                      // 数据截止时间
};

struct UploadDataparam             // 数据上送参数
{
    RemoteDestnationType eDstType;          // 数据上送目标
    StationOutlineInfo stDataInfo;          // 数据信息
};
}
#endif // __PATROLDEFINES_H_

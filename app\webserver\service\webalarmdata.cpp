#include "webalarmdata.h"
#include "dbserver.h"

WebAlarmData::WebAlarmData()
{

}


/************************************************
* 函数名:  reFreshDeviceData
* 输入参数:  NULL
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  刷新svg图
************************************************/
void WebAlarmData::reFreshStationSVGData(void)
{
    ConfigService &configService = ConfigService::instance();
    const StationNode &deviceTree =  configService.stationNode();
    if (deviceTree.strName != "")
    {
        QJsonArray jsonDevices;
        for (int i = 0; i < deviceTree.devices.size(); i++)
        {
            const DeviceNode &device = deviceTree.devices.at(i);
            int alarmCount = getDeviceAlarmCount(device);
            QJsonObject jsonDevice;
            jsonDevice.insert(STR_DEVICE_NAME, device.strName);
            jsonDevice.insert(STR_ALARM_COUNT, alarmCount);
            jsonDevices.insert(i,jsonDevice);
        }
        if (jsonDevices.size() > 0)
        {
            insert(STR_DEVICE_ITEMS,jsonDevices);
        }
    }

}

/************************************************
* 函数名:  getDeviceAlarmCount
* 输入参数:  NULL
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取设备的报警条数
************************************************/
int WebAlarmData::getDeviceAlarmCount(const DeviceNode &staDevice)
{
    Q_UNUSED(staDevice);
    return 0;
//    DBServer &dbSever = DBServer::instance();

//    int alarmCount = 0;
//    for (int i = 0; i < staDevice.adus.count(); i++)
//    {
//        const ADUUnitInfo &adu = staDevice.adus.at(i);
//        for (int j = 0; j < adu.Channels.count(); j++)
//        {
//            const ADUChannelInfo &channel = adu.Channels.at(j);

//            alarmCount += dbSever.channelAlarmRecordsCount(dbSever.getChannelName(adu.strID, channel.unID));
//        }
//    }
//    return alarmCount;
}



/************************************************
* 函数名:  reFreshDeviceSVGData
* 输入参数:  NULL
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  刷新设备svg图
************************************************/
void WebAlarmData::reFreshDeviceSVGData(quint8 ucDeviceID)
{
    Q_UNUSED(ucDeviceID);

//    ConfigService &configService = ConfigService::instance();
//    const StationNode &deviceTree =  configService.stationNode();
//    if (deviceTree.strName != "")
//    {
//        for (int i = 0; i < deviceTree.devices.size(); i++)
//        {
//            const DeviceNode &device = deviceTree.devices.at(i);
//            QJsonArray jsonChannels;
//            if (device.unID == ucDeviceID)
//            {
//                for (int j = 0; j < device.adus.size(); j++)
//                {

//                    const ADUUnitInfo &adu = device.adus.at(j);
//                    for(int k = 0; k < adu.Channels.count(); k++)
//                    {
//                        const ADUChannelInfo &channel = adu.Channels.at(k);
//                        QJsonObject jsonChannel;
//                        jsonChannel.insert(STR_POINT_NAME,channel.strName);
//                        jsonChannel.insert(STR_POINT_STATE,getstrChannelState(channel.estate));
//                        jsonChannel.insert(STR_POINT_TYPE,getstrChannelType(channel.etype));

//                        jsonChannels.append(jsonChannel);
//                    }
//                }
//                insert(STR_POINT_ITEMS,jsonChannels);
//            }
//        }
//    }
}


/************************************************
* 函数名:  reFreshStationCountData
* 输入参数:  NULL
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  刷新站点统计信息
************************************************/
void WebAlarmData::reFreshStationCountData(const QDate &startDate, const QDate &endDate)
{
    Q_UNUSED(startDate);
    Q_UNUSED(endDate);

//    Q_UNUSED(startDate);
//    Q_UNUSED(endDate);

//    QJsonArray jsonDataArray;
//    QJsonObject jsonDataObject;
//    QJsonObject jsonDeviceSummary;
//    QJsonObject jsonStateSummary;
//    QJsonObject jsonDeviceItem;
//    QJsonObject jsonStateTrend;

//    QJsonArray jsonDeviceID;
//    QJsonArray jsonAcked;
//    QJsonArray jsonunAcked;
//    QJsonArray jsonDateValue;
//    QJsonArray jsonWarning;
//    QJsonArray jsonAlarm;

//    int iErrorChannelCount = 0;
//    int iNormalChannelCount = 0;
//    int iErrorDeviceCount = 0;
//    int iNormalDeviceCount = 0;
//    ConfigService &configService = ConfigService::instance();
//    DBServer &dbSever = DBServer::instance();
//    const StationNode &deviceTree =  configService.stationNode();
//    for (int i = 0; i < deviceTree.devices.size(); i++)
//    {
//        const DeviceNode &device = deviceTree.devices.at(i);
//        jsonDeviceID.append(device.unID);
//        quint64 ullDeviceAckedAlarmcount = 0;
//        quint64 ullDeviceUnackedAlarmcount = 0;
//        if (device.state() == STATE_NORMAL)
//        {
//            iNormalDeviceCount++;
//        }
//        else
//        {
//            iErrorDeviceCount++;
//        }
//        for (int j = 0; j < device.adus.size(); j++)
//        {

//            const ADUUnitInfo &adu = device.adus.at(j);
//            for(int k = 0; k < adu.Channels.count(); k++)
//            {
//                const ADUChannelInfo &channel = adu.Channels.at(k);
//                ullDeviceAckedAlarmcount += dbSever.channelAlarmRecordsCount(dbSever.getChannelName(adu.strID, channel.unID));
//                if (channel.estate == STATE_NORMAL)
//                {
//                    iNormalChannelCount++;
//                }
//                else
//                {
//                    iErrorChannelCount++;
//                }

//            }
//        }
//        jsonAcked.append(QString::number(ullDeviceAckedAlarmcount));
//        jsonunAcked.append(QString::number(ullDeviceUnackedAlarmcount));
//    }
//    jsonDeviceItem.insert(STR_X_VALUE, jsonDeviceID);
//    jsonDeviceItem.insert(STR_ACKED, jsonAcked);
//    jsonDeviceItem.insert(STR_UNACKED, jsonunAcked);


//    jsonStateTrend.insert(STR_X_VALUE, jsonDateValue);
//    jsonStateTrend.insert(STR_PARA_ALARM, jsonAlarm);
//    jsonStateTrend.insert(STR_PARA_WARNING, jsonWarning);

//    jsonStateSummary.insert(STR_EXCEPTION_RATE,iNormalChannelCount);

//    jsonDeviceSummary.insert("normal",iNormalDeviceCount);
//    jsonDeviceSummary.insert(STR_EXCEPTION,iErrorDeviceCount);
//    jsonDeviceSummary.insert(STR_COUNT,iNormalDeviceCount + iErrorDeviceCount);
//    jsonDataObject.insert(STR_DEVICE_SUMMARY,jsonDeviceSummary);
//    jsonDataObject.insert(STR_STATE_SUMMARY,jsonStateSummary);
//    jsonDataObject.insert(STR_DEVICE_ITEMS,jsonDeviceItem);
//    jsonDataObject.insert(STR_STATE_TREND,jsonStateTrend);
//    jsonDataArray.append(jsonDataObject);
//    insert(STR_STATION_STATISTIC,jsonDataArray);
    return;
}


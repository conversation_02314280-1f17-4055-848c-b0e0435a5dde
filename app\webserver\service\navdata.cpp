#include "navdata.h"

/************************************************
 * 函数名:  NavData
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  构造函数
 ************************************************/
NavData::NavData()
{

}

/************************************************
 * 函数名:  reFresh
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  刷新数据
 ************************************************/
void NavData::reFresh()
{
//    ConfigService &configService = ConfigService::instance();
//    StationNode deviceTree =  configService.stationNode();
//    if (deviceTree.strName != "")
//    {
//        insert("stationName",deviceTree.strName);
//        QJsonArray jsonDevices;
//        for (int i = 0; i < deviceTree.devices.size(); i++)
//        {
//            const DeviceNode &device =   deviceTree.devices.at(i);
//            QJsonObject jsonDevice;
//            jsonDevice.insert("deviceName", device.strName);
//            QJsonArray jsonChannels;
//            for (int j = 0; j < device.adus.size(); j++)
//            {
//                const ADUUnitInfo &adu = device.adus.at(j);
//                for(int k = 0; k < adu.Channels.count(); k++)
//                {
//                    const ADUChannelInfo &channel = adu.Channels.at(k);
//                    QJsonObject jsonChannel;
//                    jsonChannel.insert("channelName", channel.strName);
//                    jsonChannels.append(jsonChannel);
//                }
//            }
//            if (jsonChannels.size() > 0)
//            {
//                jsonDevice.insert("Channel",jsonChannels);
//            }
//            jsonDevices.append(jsonDevice);
//        }
//        if (jsonDevices.size() > 0)
//        {
//            insert("device",jsonDevices);
//        }
//    }
}

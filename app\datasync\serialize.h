/*
* Copyright (c) 2019.8，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：serialize.h
*
* 初始版本：1.0.0
* 作者：wujun
* 创建日期：2018年9月25日
* 摘要：序列化、反序列化结构体
*/

#ifndef SERIALIZE_H
#define SERIALIZE_H

#include <QDataStream>
#include <QDebug>

#ifndef CONFIG_TOOL
#include "syncdefine.h"
#include "testdatastruct.h"
#include "dbrecord.h"
#include "patroldefines.h"
using namespace DataSync;
#include "cmdefine.h"
#endif

#ifdef CONFIG_TOOL
#include "module/configtoolservice/cmdefine.h"
#endif

// 序列化PointConnectionInfo
inline QDataStream& operator <<(QDataStream &out, const PointConnectionInfo &data)
{
    int iType = (int)data.etype;
    out << data.strID << data.unID << iType;
    return out;
}

// 反序列化PointConnectionInfo
inline QDataStream& operator >>(QDataStream &in, PointConnectionInfo &data)
{
    int itype;
    in >> data.strID >> data.unID >> itype;
    data.etype = (ADUChannelType)itype;
    return in;
}

// 序列化TestPointInfo
inline QDataStream& operator <<(QDataStream &out, const TestPointInfo &data)
{
    out << data.strName << data.strOutName << data.strPointGUID << data.ConnectionInfo;
    return out;
}

// 反序列化TestPointInfo
inline QDataStream& operator >>(QDataStream &in, TestPointInfo &data)
{
    in >> data.strName >> data.strOutName >> data.strPointGUID >> data.ConnectionInfo;
    return in;
}

// 序列化
inline QDataStream& operator <<(QDataStream &out, const DeviceAlarmPara &data)
{
    out << data.iDeviceAlarm << data.iDeviceAlarmMax;
    return out;
}

// 反序列化
inline QDataStream& operator >>(QDataStream &in, DeviceAlarmPara &data)
{
    in >> data.iDeviceAlarm >> data.iDeviceAlarmMax;
    return in;
}

// 序列化
inline QDataStream& operator <<(QDataStream &out, const DeviceNode &data)
{
    int iVoltage = data.eVoltage;
    int iDeviceType = data.eDeviceType;
    out << data.strName << data.strDeviceGUID << data.strPMS
        << iDeviceType << iVoltage << data.testPoints;
    return out;
}
// 反序列化
inline QDataStream& operator >>(QDataStream &in, DeviceNode &data)
{
    int iDeviceType, ieVoltage;
    in >> data.strName >> data.strDeviceGUID >> data.strPMS
       >> iDeviceType >> ieVoltage >> data.testPoints;
    data.eDeviceType = (DeviceType)iDeviceType;
    data.eVoltage = (VoltageLevel)ieVoltage;
    return in;
}

// 序列化 FloodChannelPara
inline QDataStream& operator <<(QDataStream &out, const FloodChannelPara &data)
{
    out << data.cFlood;
    return out;
}

// 反序列化 FloodChannelPara
inline QDataStream& operator >>(QDataStream &in, FloodChannelPara &data)
{
    in >> data.cFlood;
    return in;
}

// 序列化 AEChannelPara
inline QDataStream& operator <<(QDataStream &out, const AEChannelPara &data)
{
    out << (int)data.eChannelPhase << data.cGain << (int)data.eChannelGainType << data.ucSampleCycles
        << (ushort)data.usSampelCount;
    return out;
}

// 反序列化 AEChannelPara
inline QDataStream& operator >>(QDataStream &in, AEChannelPara &data)
{
    ushort usSampelCount;
    int iChannelPhase, iChannelGainType;
    in >> iChannelPhase >> data.cGain >> iChannelGainType >> data.ucSampleCycles
       >> usSampelCount;
    data.eChannelPhase = (ChannelPhase)iChannelPhase;
    data.eChannelGainType = (ChannelGainType)iChannelGainType;
    data.usSampelCount = usSampelCount;

    return in;
}

// 序列化
inline QDataStream& operator <<(QDataStream &out, const TEVChannelPara &data)
{
    out << data.cBackGroundNum <<data.eChannelPhase;
    return out;
}

// 反序列化
inline QDataStream& operator >>(QDataStream &in, TEVChannelPara &data)
{
    int iChannelPhase;
    in >> data.cBackGroundNum >> iChannelPhase;
    data.eChannelPhase = (ChannelPhase)iChannelPhase;
    return in;
}

// 序列化
inline QDataStream& operator <<(QDataStream &out, const UHFChannelPara &data)
{
    out << data.cGain << (int)data.eChannelGainType << data.ucSampleCycles
        << (ushort)data.usSampelCount<< (int)data.cBandWidth << (int)data.eChannelPhase;
    return out;
}

// 反序列化
inline QDataStream& operator >>(QDataStream &in, UHFChannelPara &data)
{
    int iChannelGainType;
    int iChannelPhase;
    int cBandWidth;
    ushort usSampelCount;

    in >> data.cGain >> iChannelGainType >> data.ucSampleCycles
        >> usSampelCount >> cBandWidth >> iChannelPhase;
    data.eChannelGainType = (ChannelGainType)iChannelGainType;
    data.cBandWidth = (UHFDataFilter)cBandWidth;
    data.eChannelPhase = (ChannelPhase)iChannelPhase;
    data.usSampelCount = usSampelCount;
    return in;
}

// 序列化
inline QDataStream& operator <<(QDataStream &out, const HFCTChannelPara &data)
{
    out << data.cGain << data.eChannelGainType << data.ucSampleCycles
        << data.usSampelCount << (int)data.eChannelPhase;
    return out;
}

// 反序列化
inline QDataStream& operator >>(QDataStream &in, HFCTChannelPara &data)
{
    int iChannelGainType;
    int eChannelPhase;
    quint16 usSampelCount;
    in >> data.cGain >> iChannelGainType >> data.ucSampleCycles
        >> usSampelCount >> eChannelPhase;
    data.eChannelGainType = (ChannelGainType)iChannelGainType;
    data.eChannelPhase = (ChannelPhase)eChannelPhase;
    data.usSampelCount = usSampelCount;
    return in;
}

// 序列化
inline QDataStream& operator <<(QDataStream &out, const ArresterIChannelPara &data)
{
    out << (int)data.eChannelPhase;
    return out;
}
// 反序列化
inline QDataStream& operator >>(QDataStream &in, ArresterIChannelPara &data)
{
    int eChannelPhase;
    in >> eChannelPhase ;
    data.eChannelPhase = (ChannelPhase)eChannelPhase;
    return in;
}

// 序列化
inline QDataStream& operator <<(QDataStream &out, const ArresterUChannelPara &data)
{
    out << (int)data.eChannelPhase << data.fTransformationRatio;
    return out;
}
// 反序列化
inline QDataStream& operator >>(QDataStream &in, ArresterUChannelPara &data)
{
    int eChannelPhase;
    float fTransformationRatio;
    in >> eChannelPhase >> fTransformationRatio;
    data.eChannelPhase = (ChannelPhase)eChannelPhase;
    data.fTransformationRatio = fTransformationRatio;
    return in;
}

// 序列化
inline QDataStream& operator <<(QDataStream &out, const VibrationChannelParam &data)
{
    out << (int)data.ucSampleCycles << (int)data.usSampelCount;
    return out;
}
// 反序列化
inline QDataStream& operator >>(QDataStream &in, VibrationChannelParam &data)
{
    int ucSampleCycles;
    int usSampelCount;
    in >> ucSampleCycles >> usSampelCount;
    data.ucSampleCycles = ucSampleCycles;
    data.usSampelCount = usSampelCount;
    return in;
}

// 序列化 Channelpara
inline QDataStream& operator <<(QDataStream &out, const Channelpara &data)
{
    out << data.staAEPara << data.staTEVPara << data.staUHFPara << data.staHFCTPara; //<< data.staTempParam << data.staHumParam;
    return out;
}
// 反序列化 Channelpara
inline QDataStream& operator >>(QDataStream &in, Channelpara &data)
{
    in >> data.staAEPara >> data.staTEVPara >> data.staUHFPara >> data.staHFCTPara;// >> data.staTempParam >> data.staHumParam;

    return in;
}

// 序列化 ADUChannelType
inline QDataStream& operator <<(QDataStream &out, const ADUChannelType &data)
{
    int iData = (int)data;
    out << iData;
    return out;
}
// 反序列化 ADUChannelType
inline QDataStream& operator >>(QDataStream &in, ADUChannelType &data)
{
    int iData;
    in >> iData;
    data = (ADUChannelType)iData;
    return in;
}


//机械特性通道序列化
inline QDataStream& operator <<(QDataStream &out, const MechChannelPara &data)
{
    out << data.usLoopCurrentThred << data.usMotorCurrentThred << (quint8)data.bSwitchState << (quint8)data.bBreakerType;
    return out;
}

//机械特性通道反序列化
inline QDataStream& operator >>(QDataStream &in,  MechChannelPara &data)
{
   quint16 loopCurrentThred, motorCurrentThred;
   quint8 switchState, breakerType;
   in >> loopCurrentThred >> motorCurrentThred >> switchState >> breakerType;
   data.usLoopCurrentThred = loopCurrentThred;
   data.usMotorCurrentThred = motorCurrentThred;
   data.bSwitchState = switchState;
   data.bBreakerType = breakerType;

   return in;
}


//温度通道序列化
inline QDataStream& operator <<(QDataStream &out, const TempChannelPara &data)
{
    out << data.fUpperThreshold << data.fLowerThreshold << data.fChangedThreshold;
    return out;
}

//温度通道反序列化
inline QDataStream& operator >>(QDataStream &in,  TempChannelPara &data)
{
    float upper,lower,change;
    in >> upper >> lower >> change;
    data.fUpperThreshold = upper;
    data.fLowerThreshold = lower;
    data.fChangedThreshold = change;
    return in;
}

//湿度通道序列化
inline QDataStream& operator <<(QDataStream &out, const HumChannelPara &data)
{
    out << data.fUpperThreshold << data.fLowerThreshold << data.fChangedThreshold;
    return out;
}

//湿度通道反序列化
inline QDataStream& operator >>(QDataStream &in,  HumChannelPara &data)
{
    float upper,lower,change;
    in >> upper >> lower >> change;
    data.fUpperThreshold = upper;
    data.fLowerThreshold = lower;
    data.fChangedThreshold = change;
    return in;
}

//// 序列化 ADUChannelInfo
//inline QDataStream& operator <<(QDataStream &out, const ADUChannelInfo &data)
//{
//    out << data.strName << data.unID << data.etype << data.stachpara
//        << data.estate;
//    return out;
//}
//// 反序列化ADUChannelInfo
//inline QDataStream& operator >>(QDataStream &in, ADUChannelInfo &data)
//{
//    int itype, istate;
//    in >> data.strName >> data.unID >> itype >> data.stachpara
//       >> istate;
//    data.etype = (ADUChannelType)itype;
//    data.estate = (ADUChannelState)istate;
//    return in;
//}

// 序列化 ADUChannelInfo
inline QDataStream& operator <<(QDataStream &out, const ADUChannelInfo &data)
{
    out << data.strName << data.unID << (int)data.etype;
    if (CHANNEL_AE == data.etype)
    {
         out << data.stachpara.staAEPara;
    }
    if (CHANNEL_TEV == data.etype)
    {
        out << data.stachpara.staTEVPara;
    }
    if(CHANNEL_MECH == data.etype)
    {
       out << data.stachpara.staMechPara;
    }
    if (CHANNEL_UHF == data.etype)
    {
        out << data.stachpara.staUHFPara;
    }
    if (CHANNEL_TEVPRPS == data.etype)
    {
        //out << data.stachpara.staTEVPRPSPara;
    }
    if (CHANNEL_HFCT == data.etype)
    {
        out << data.stachpara.staHFCTPara;
    }
    if (CHANNEL_ARRESTER_I == data.etype)
    {
        out << data.stachpara.staArresterIPara;
    }
    if (CHANNEL_ARRESTER_U == data.etype)
    {
        out << data.stachpara.staArresterUPara;
    }
    if (CHANNEL_GROUNDDINGCURRENT == data.etype)
    {
        out << data.stachpara.staArresterUPara;
    }
    if (CHANNEL_LEAKAGECURRENT == data.etype)
    {
        out << data.stachpara.staArresterUPara;
    }
    if (CHANNEL_VIBRATION == data.etype)
    {
        out << data.stachpara.staVibrationParam;
    }
    if(CHANNEL_TEMPERATURE == data.etype)
    {
        out << data.stachpara.staTempParam;
    }
    if(CHANNEL_FLOOD == data.etype)
    {
        out << data.stachpara.stFloodChannelPara;
    }
    if(CHANNEL_HUMIDITY == data.etype)
    {
        out << data.stachpara.staHumParam;
    }

    return out;
}
// 反序列化ADUChannelInfo
inline QDataStream& operator >>(QDataStream &in, ADUChannelInfo &data)
{
    int nType;
    in >> data.strName >> data.unID >> nType;
    data.etype = (ADUChannelType)nType;

    if (CHANNEL_AE == data.etype)
    {
        in >> data.stachpara.staAEPara;
    }
    if (CHANNEL_TEV == data.etype)
    {
        in >> data.stachpara.staTEVPara;
    }
    if(CHANNEL_MECH == data.etype)
    {
       in >> data.stachpara.staMechPara;
    }
    if(CHANNEL_TEVPRPS == data.etype)
    {
       //in >> data.stachpara.staTEVPRPSPara;
    }
    if (CHANNEL_UHF == data.etype)
    {
        in >> data.stachpara.staUHFPara;
    }
    if (CHANNEL_HFCT == data.etype)
    {
        in >> data.stachpara.staHFCTPara;
    }
    if (CHANNEL_ARRESTER_I == data.etype)
    {
        in >> data.stachpara.staArresterIPara;
    }
    if (CHANNEL_ARRESTER_U == data.etype)
    {
        in >> data.stachpara.staArresterUPara;
    }
    if (CHANNEL_GROUNDDINGCURRENT == data.etype)
    {
        in >> data.stachpara.staArresterIPara;
    }
    if (CHANNEL_LEAKAGECURRENT == data.etype)
    {
        in >> data.stachpara.staArresterIPara;
    }
    if (CHANNEL_VIBRATION == data.etype)
    {
        in >> data.stachpara.staVibrationParam;
    }
    if(CHANNEL_TEMPERATURE == data.etype)
    {
        in >> data.stachpara.staTempParam;
    }
    if(CHANNEL_HUMIDITY == data.etype)
    {
        in >> data.stachpara.staHumParam;
    }
    if(CHANNEL_FLOOD == data.etype)
    {
        in >> data.stachpara.stFloodChannelPara;
    }

    return in;
}

// 序列化 ADUParam
inline QDataStream& operator <<(QDataStream &out, const ADUParam &data)
{
    out << data.ucAutoUpdate << data.ucFrequency << data.uiSampleSpace
        << data.ucWorkGroup << data.usNumInGroup << data.ucConnectionSpeed
        << data.ucConnectionLoad << data.uiSleepTime << data.usStartSampleTime
        << (int)data.eADUWorkModel << data.ucStartArtificialTime << data.ucEndArtificialTime
       <<data.usArtificialWakeUpInterval<<data.usNotArtificialWalkUpInterval<<data.usTutelageSampleSpace
      << (int)data.eLinkGroup << data.bAutoChangeMode << data.uiAutoGivingSpace;
    return out;
}
// 反序列化 ADUParam
inline QDataStream& operator >>(QDataStream &in, ADUParam &data)
{
    int iLinkGroup = 0;
    int iType = 0;
    in >> data.ucAutoUpdate >> data.ucFrequency >> data.uiSampleSpace
       >> data.ucWorkGroup >> data.usNumInGroup >> data.ucConnectionSpeed
            >> data.ucConnectionLoad >> data.uiSleepTime >> data.usStartSampleTime
            >> iType >> data.ucStartArtificialTime >> data.ucEndArtificialTime
           >> data.usArtificialWakeUpInterval >> data.usNotArtificialWalkUpInterval
            >> data.usTutelageSampleSpace >> iLinkGroup >> data.bAutoChangeMode >> data.uiAutoGivingSpace;
    data.eADUWorkModel = (Monitor::ADUWorkMode)iType;
    data.eLinkGroup = (Monitor::LinkGroup)iLinkGroup;
    return in;
}

// 序列化 ADUUnitInfo
inline QDataStream& operator <<(QDataStream &out, const ADUUnitInfo &data)
{

    int iLinkGroup = (int)data.eLinkGroup;
    int iType = (int)data.eType;
    QDateTime dateTime(QDateTime::currentDateTime());
    bool online = true;
    out << data.strName << data.strID << online
        << dateTime << iLinkGroup
        << data.stADUParam << data.iSampleItvl
        << iType << data.strVersion << data.strRS485ComPort << data.iTaskGroup <<  data.Channels;
    return out;
}
// 反序列化 ADUUnitInfo
inline QDataStream& operator >>(QDataStream &in, ADUUnitInfo &data)
{
    int iLinkGroup, iType;
    QDateTime dateTime(QDateTime::currentDateTime());
    bool online = true;
    in >> data.strName >> data.strID >> online
       >> dateTime >> iLinkGroup
       >> data.stADUParam >> data.iSampleItvl
       >> iType >> data.strVersion >> data.strRS485ComPort
            >> data.iTaskGroup >> data.Channels;
    data.eLinkGroup = (Monitor::LinkGroup)iLinkGroup;
    data.eType = (ADUType)iType;
    return in;
}

// 序列化 StationNode
inline QDataStream& operator <<(QDataStream &out, const StationNode &data)
{
//    int placeSize = 300;

//    placeSize -= data.strName.size();
//    placeSize -= sizeof(int);
//    placeSize -= data.strGUID.size();
//    placeSize -= data.strPMS.size();
//    placeSize -=  data.strCompany.size();

  //  char* placeHolder = new char(placeSize);
  //  memset(placeHolder, 'c', sizeof(char) * placeSize);
   // QString strPlaceHolder = QString(QLatin1String(placeHolder));

    int iVoltage = data.eVoltage;
    out << data.strName << iVoltage << data.strSiteGUID
        << data.strPMS << data.strCompany   << data.devices ;

   // delete[] placeHolder;
  //  placeHolder = NULL;
    return out;
}
// 反序列化 StationNode
inline QDataStream& operator >>(QDataStream &in, StationNode &data)
{
    QString strPlaceHolder;
    int iVoltage;
    in >> data.strName >> iVoltage >> data.strSiteGUID
            >> data.strPMS >> data.strCompany  >> data.devices;

    data.eVoltage = (VoltageLevel)iVoltage;
    return in;
}

#ifndef CONFIG_TOOL
// 序列化 UHFData
inline QDataStream& operator <<(QDataStream &out, const UHFData &data)
{
    out << data.battery << data.recordID << data.freq
        << data.gain << data.bandWidth << data.syncType
        << data.syncFlag << data.period << data.phase
        << data.maxQ << data.minQ << data.avgQ
        << data.pdCount << data.isPd << data.pdPeriod
        << data.pdType << data.array;
    return out;
}
#endif

#ifndef CONFIG_TOOL
// 反序列化 UHFData
inline QDataStream& operator >>(QDataStream &in, UHFData &data)
{
    in >> data.battery >> data.recordID >> data.freq
       >> data.gain >> data.bandWidth >> data.syncType
       >> data.syncFlag >> data.period >> data.phase
       >> data.maxQ >> data.minQ >> data.avgQ
       >> data.pdCount >> data.isPd >> data.pdPeriod
       >> data.pdType >> data.array;
    return in;
}
#endif

#ifndef CONFIG_TOOL
// 序列化 AEData
inline QDataStream& operator <<(QDataStream &out, const AEData &data)
{
    out << data.freq << data.gain << data.syncType
        << data.syncFlag << data.maxV << data.rmsV
        << data.freq1 << data.freq2;
    return out;
}
#endif

#ifndef CONFIG_TOOL
// 反序列化 AEData
inline QDataStream& operator >>(QDataStream &in, AEData &data)
{
    in >> data.freq >> data.gain >> data.syncType
       >> data.syncFlag >> data.maxV >> data.rmsV
       >> data.freq1 >> data.freq2;
    return in;
}
#endif

#ifndef CONFIG_TOOL
// 序列化 PointArchiveInfo
inline QDataStream& operator <<(QDataStream &out, const PointArchiveInfo &data)
{
    out << data.strStationPMS << data.strDevicePMS << data.strPointGUID
        << data.strStationGUID << data.strDeviceGUID;
    return out;
}
#endif

#ifndef CONFIG_TOOL
// 反序列化 PointArchiveInfo
inline QDataStream& operator >>(QDataStream &in, PointArchiveInfo &data)
{
    in >> data.strStationPMS >> data.strDevicePMS >> data.strPointGUID
       >> data.strStationGUID >> data.strDeviceGUID;;
    return in;
}
#endif

#ifndef CONFIG_TOOL
// 序列化 AERecord
inline QDataStream& operator <<(QDataStream &out, const AERecord &data)
{
    out << data.recordTime << data.channelId << data.aduId
        << data.autoId << data.batteryInfo << data.globalId
        << data.recordId << data.recordStringId << data.pointArchiveInfo
        << data.isUpdate << data.ucPwrFre << data.ucGain << data.ucSyncType
        << data.ucSyncFlag << data.fMax << data.fRms << data.fFre1
        << data.fFre2 << data.ucSampleCycleCount << data.usSampleCountCycle;
//        << data.fArray;
        return out;
}
#endif

#ifndef CONFIG_TOOL
// 反序列化 AERecord
inline QDataStream& operator >>(QDataStream &in, AERecord &data)
{
    in >> data.recordTime >> data.channelId >> data.aduId
       >> data.autoId >> data.batteryInfo >> data.globalId
       >> data.recordId >> data.recordStringId >> data.pointArchiveInfo
       >> data.isUpdate >> data.ucPwrFre >> data.ucGain >> data.ucSyncType
       >> data.ucSyncFlag >> data.fMax >> data.fRms >> data.fFre1
       >> data.fFre2 >> data.ucSampleCycleCount >> data.usSampleCountCycle;
//       >> data.fArray;
    return in;
}
#endif

#ifndef CONFIG_TOOL
// 序列化 TEVRecord
inline QDataStream& operator <<(QDataStream &out, const TEVRecord &data)
{
    out << data.recordTime << data.channelId << data.aduId
        << data.autoId << data.globalId << data.recordId
        << data.batteryInfo << data.recordStringId << data.pointArchiveInfo
        << data.isUpdate << data.cMax;

    return out;
}
#endif

#ifndef CONFIG_TOOL
// 反序列化 TEVRecord
inline QDataStream& operator >>(QDataStream &in, TEVRecord &data)
{
    in >> data.recordTime >> data.channelId >> data.aduId
       >> data.autoId >> data.globalId >> data.recordId
       >> data.batteryInfo >> data.recordStringId >> data.pointArchiveInfo
       >> data.isUpdate >> data.cMax;
    return in;
}
#endif

#ifndef CONFIG_TOOL
// 序列化 PRPSRecord
inline QDataStream& operator <<(QDataStream &out, const PRPSRecord &data)
{
    out << data.recordTime << data.channelId << data.aduId
        << data.globalId << data.autoId << data.recordId
        << data.recordStringId << data.pointArchiveInfo << data.isUpdate
        << data.batteryInfo << data.ucPwrFre << data.ucGain << data.ucBandwidth
        << data.ucSyncType << data.ucSyncFlag << data.ucPeriod
        << data.ucPhase << data.fMaxq << data.fMinq << data.fAvgq
        << data.iPdCount << data.fPdPhase << data.iIsPd << data.iPdType
        << data.fArray;

    return out;
}
#endif

#ifndef CONFIG_TOOL
// 反序列化 PRPSRecord
inline QDataStream& operator >>(QDataStream &in, PRPSRecord &data)
{
    in >> data.recordTime >> data.channelId >> data.aduId
       >> data.globalId >> data.autoId >> data.recordId
       >> data.recordStringId >> data.pointArchiveInfo >> data.isUpdate
       >> data.batteryInfo >> data.ucPwrFre >> data.ucGain >> data.ucBandwidth
       >> data.ucSyncType >> data.ucSyncFlag >> data.ucPeriod
       >> data.ucPhase >> data.fMaxq >> data.fMinq >> data.fAvgq
       >> data.iPdCount >> data.fPdPhase >> data.iIsPd >> data.iPdType
       >> data.fArray;

    return in;
}
#endif

#ifndef CONFIG_TOOL
// 序列化 TbData
inline QDataStream& operator <<(QDataStream &out, const TbData &data)
{
    out << data.channelType << data.aeData << data.tevData
        << data.uhfData << data.hfctData;
    return out;
}
#endif

#ifndef CONFIG_TOOL
// 反序列化 TbData
inline QDataStream& operator >>(QDataStream &in, TbData &data)
{
    in >> data.channelType >> data.aeData >> data.tevData
    >> data.uhfData >> data.hfctData;
    return in;
}
#endif

#ifndef CONFIG_TOOL
// 序列化
inline QDataStream& operator <<(QDataStream &out, const ConnReply &data)
{
    out << data.bAgree << data.hostCode << data.stationCode << data.stationName;
    return out;
}
#endif

#ifndef CONFIG_TOOL
// 反序列化
inline QDataStream& operator >>(QDataStream &in, ConnReply &data)
{
    in >> data.bAgree >> data.hostCode >> data.stationCode >> data.stationName;
    return in;
}
#endif

#ifndef CONFIG_TOOL
// 序列化
inline QDataStream& operator <<(QDataStream &out, const CustmSyncInfoReqst &data)
{
    out << data.channelType << data.devCode << data.tpCode << data.dtStart << data.dtEnd;
    return out;
}
#endif

#ifndef CONFIG_TOOL
// 反序列化
inline QDataStream& operator >>(QDataStream &in, CustmSyncInfoReqst &data)
{
    int iChannelType;
    in >> iChannelType >> data.devCode >> data.tpCode >> data.dtStart >> data.dtEnd;
    data.channelType = (ADUChannelType)iChannelType;
    return in;
}
#endif

#ifndef CONFIG_TOOL
// 序列化 SyncDataReqst
inline QDataStream& operator <<(QDataStream &out, const SyncDataReqst &data)
{
    out << data.dbName << data.globalID;
    return out;
}
#endif

#ifndef CONFIG_TOOL
// 反序列化 SyncDataReqst
inline QDataStream& operator >>(QDataStream &in, SyncDataReqst &data)
{
    in >> data.dbName >> data.globalID;
    return in;
}
#endif

#ifndef CONFIG_TOOL
// 序列化 SyncInfoReply
inline QDataStream& operator <<(QDataStream &out, const SyncInfoReply &data)
{
    out << data.dbName << data.startID << data.endID;
    return out;
}
#endif

#ifndef CONFIG_TOOL
// 反序列化 SyncInfoReply
inline QDataStream& operator >>(QDataStream &in, SyncInfoReply &data)
{
    in >> data.dbName >> data.startID >> data.endID;
    return in;
}
#endif

#ifndef CONFIG_TOOL
// 序列化
inline QDataStream& operator <<(QDataStream &out, const CustmSyncDataReqst &data)
{
    out << data.dbName << data.tbName << data.index;
    return out;
}
#endif

#ifndef CONFIG_TOOL
// 反序列化
inline QDataStream& operator >>(QDataStream &in, CustmSyncDataReqst &data)
{
    in >> data.dbName >> data.tbName >> data.index;
    return in;
}
#endif


//---------patroldefines.h---------
#ifndef CONFIG_TOOL
// 序列化 TestPointInfo
inline QDataStream& operator <<(QDataStream &out, const PatrolServiceNS::TestPointInfo &data)
{
    out << data.s_testPointName << data.eDataType << data.s_testPointID
        << data.s_eDataType;
    return out;
}
#endif

#ifndef CONFIG_TOOL
// 反序列化 TestPointInfo
inline QDataStream& operator >>(QDataStream &in, PatrolServiceNS::TestPointInfo &data)
{
    int iDataType, s_iDataType;
    in >> data.s_testPointName >> iDataType >> data.s_testPointID >> s_iDataType;
    data.eDataType = (PatrolServiceNS::DataType)iDataType;
    data.s_eDataType = (PatrolServiceNS::DataType)s_iDataType;
    return in;
}
#endif

#ifndef CONFIG_TOOL
// 序列化 PrimaryDeviceInfo
inline QDataStream& operator <<(QDataStream &out, const PatrolServiceNS::PrimaryDeviceInfo &data)
{
    out << data.s_deviceName << data.s_deviceID << data.s_frontEndList;
    return out;
}
#endif

#ifndef CONFIG_TOOL
// 反序列化 PrimaryDeviceInfo
inline QDataStream& operator >>(QDataStream &in, PatrolServiceNS::PrimaryDeviceInfo &data)
{
    in >> data.s_deviceName >> data.s_deviceID >> data.s_frontEndList;
    return in;
}
#endif

#ifndef CONFIG_TOOL
// 序列化 StationOutlineInfo
inline QDataStream& operator <<(QDataStream &out, const PatrolServiceNS::StationOutlineInfo &data)
{
    out << data.s_stationName << data.s_stationID;
    return out;
}
#endif

#ifndef CONFIG_TOOL
// 反序列化 StationOutlineInfo
inline QDataStream& operator >>(QDataStream &in, PatrolServiceNS::StationOutlineInfo &data)
{
    in >> data.s_stationName >> data.s_stationID;
    return in;
}
#endif

#ifndef CONFIG_TOOL
// 序列化
inline QDataStream& operator <<(QDataStream &out, const PatrolServiceNS::StationInfo &data)
{
    out << data.s_outlineInfo << data.s_strVoltage << data.s_strID
        << data.s_deviceList;
    return out;
}
#endif

#ifndef CONFIG_TOOL
// 反序列化
inline QDataStream& operator >>(QDataStream &in, PatrolServiceNS::StationInfo &data)
{
    in >> data.s_outlineInfo >> data.s_strVoltage >> data.s_strID
       >> data.s_deviceList;
    return in;
}
#endif

#ifndef CONFIG_TOOL
// 序列化 SyncDataParam
inline QDataStream& operator <<(QDataStream &out, const PatrolServiceNS::SyncDataParam &data)
{
    out << data.eSyncType << data.vecDataType << data.stationInfo
        << data.timeBegin << data.timeEnd;
    return out;
}
#endif

#ifndef CONFIG_TOOL
// 反序列化 SyncDataParam
inline QDataStream& operator >>(QDataStream &in, PatrolServiceNS::SyncDataParam &data)
{
    int nSyncType;
    QVector<int> iDataType;
    in >> nSyncType >> iDataType >> data.stationInfo
       >> data.timeBegin >> data.timeEnd;
    data.eSyncType = (PatrolServiceNS::SyncDataType)nSyncType;

    data.vecDataType.clear();
    for (int i=0; i<iDataType.size(); i++)
    {
        data.vecDataType.append((PatrolServiceNS::DataType)iDataType.at(i));
    }
    return in;
}
#endif

//---------patroldefines.h
#ifndef CONFIG_TOOL
// 序列化 ConfigNode
inline QDataStream& operator <<(QDataStream &out, const ConfigNode &data)
{
    int nState = (int)data.state;
    out << nState << data.strCode << data.nGroup << data.stationNode << data.adus;
    //out << data.adus;
    return out;
}
// 反序列化 ConfigNode
inline QDataStream& operator >>(QDataStream &in, ConfigNode &data)
{
    int nState;
    in >> nState >> data.strCode >> data.nGroup >> data.stationNode >> data.adus;
    data.state = (ConfigTransState)nState;
    //in >> data.adus;
    return in;
}

// 序列化 DbIndex
inline QDataStream& operator <<(QDataStream &out, const DbIndex &data)
{
    out << data.channelType << data.audId << data.channelId << data.autoId;
    return out;
}
// 反序列化 DbIndex
inline QDataStream& operator >>(QDataStream &in, DbIndex &data)
{
    int iChannelType;
    in >> iChannelType >> data.audId >> data.channelId >> data.audId;
    data.channelType = (ADUChannelType)iChannelType;
    return in;
}

// 序列化 CustmSyncInfoReply
inline QDataStream& operator <<(QDataStream &out, const CustmSyncInfoReply &data)
{
    out << data.dbName << data.tbName << data.startIndex << data.endIndex;
    return out;
}
// 反序列化 CustmSyncInfoReply
inline QDataStream& operator >>(QDataStream &in, CustmSyncInfoReply &data)
{
    in >> data.dbName >> data.tbName >> data.startIndex >> data.endIndex;
    return in;
}
#endif

//--------------配置工具相关--------------

inline QDataStream& operator <<(QDataStream &out, const SystemSetting &data)
{
    int iMonitorType = data.eMonitorHostType;
    out << data.stsystemTime << data.iFreq << data.strLanguage << data.iUploadInterval
        << data.strMonitorID << data.uiMonitorSleepSpace << data.uiMonitorAwakeTime << data.uiMonitorAwakeStartTime
        << data.iMonitorSampleStartTime << data.iMonitorSampleSpace << data.uiSampleinterval
        << data.uiPdSampleInterval << data.uiMechSyncDataInterval << data.ucMonitorConnectionGroup
        << data.ucAutoChangeLowPower << iMonitorType << data.strMonitorName;
    return out;
}



inline QDataStream& operator >>(QDataStream &in, SystemSetting &data)
{
    int iMonitorType;
    in >> data.stsystemTime >> data.iFreq >> data.strLanguage >> data.iUploadInterval
            >> data.strMonitorID >> data.uiMonitorSleepSpace >> data.uiMonitorAwakeTime >> data.uiMonitorAwakeStartTime
            >> data.iMonitorSampleStartTime >> data.iMonitorSampleSpace >> data.uiSampleinterval
            >> data.uiPdSampleInterval >> data.uiMechSyncDataInterval >> data.ucMonitorConnectionGroup
            >> data.ucAutoChangeLowPower >> iMonitorType >> data.strMonitorName;
    data.eMonitorHostType = (MonitorHostType)iMonitorType;

    return in;
}

inline QDataStream& operator <<(QDataStream &out, const ModbusSetting &data)
{
    qint8 iParity = data.ucParity;
    out << data.ucModbusAddress << data.strDevice << data.iBaud << iParity
        << data.strParity << data.iDatabit << data.iStopbit << data.iRts << data.strRts;
    return out;
}

inline QDataStream& operator >>(QDataStream &in, ModbusSetting &data)
{
    qint8 iParity;
    in >> data.ucModbusAddress >> data.strDevice >> data.iBaud >> iParity >> data.strParity
            >> data.iDatabit >> data.iStopbit >> data.iRts >> data.strRts;
    data.ucParity = (char)iParity;
    return in;
}

inline QDataStream& operator <<(QDataStream &out, const WebSetting &data)
{
    out << data.eWebType << data.strIP << data.strSubnetMask << data.strGateWay << data.strServerAddress
        << data.iServerPort << data.strWorkAPN << data.strNetworkUserName << data.strNetworkPassword << data.bStrategy4G;
    return out;
}

inline QDataStream& operator >>(QDataStream &in, WebSetting &data)
{
    int nWebType;
    in >> nWebType >> data.strIP >> data.strSubnetMask >> data.strGateWay
            >> data.strServerAddress >> data.iServerPort >>  data.strWorkAPN
            >> data.strNetworkUserName >> data.strNetworkPassword >> data.bStrategy4G;
    data.eWebType = (WebType)nWebType;
    return in;
}

inline QDataStream& operator <<(QDataStream &out, const CM::ServerConfig &data)
{
    out << data.stStationNode << data.stSystemSetting << data.stModbusSetting
        << data.stWebSetting << data.listADUs;
    return out;
}

inline QDataStream& operator >>(QDataStream &in, CM::ServerConfig &data)
{
    QList<ADUUnitInfo> listADUs;
    in >> data.stStationNode >> data.stSystemSetting >> data.stModbusSetting
            >> data.stWebSetting >> listADUs;

    data.listADUs.clear();
    for ( int i=0; i<listADUs.size(); i++ )
    {
        data.listADUs.append(listADUs.at(i));
    }
    return in;
}

inline QDataStream& operator <<(QDataStream &out, const CM::AduParaID &data)
{
    out << (int)data;
    return out;
}

inline QDataStream& operator >>(QDataStream &in, CM::AduParaID &data)
{
    int value;
    in >> value;
    data = (CM::AduParaID)value;
    return in;
}

inline QDataStream& operator <<(QDataStream &out, const CM::I_ConfigUpload &data)
{
    out << data.stServerConfig << data.dtSystemTime;
    return out;
}

inline QDataStream& operator >>(QDataStream &in, CM::I_ConfigUpload &data)
{
    in >> data.stServerConfig >> data.dtSystemTime;
    return in;
}

inline QDataStream& operator <<(QDataStream &out, const CM::I_ConfigDownload &data)
{
    out << data.bRet << data.stServerConfig;
    return out;
}

inline QDataStream& operator >>(QDataStream &in, CM::I_ConfigDownload &data)
{
    in >> data.bRet >> data.stServerConfig;
    return in;
}

inline QDataStream& operator <<(QDataStream &out, const CM::I_SetAduPara &data)
{
    out << data.bRet << data.adus << data.aduParas;
    return out;
}

inline QDataStream& operator >>(QDataStream &in, CM::I_SetAduPara &data)
{
    in >> data.bRet >> data.adus >> data.aduParas;
    return in;
}

inline QDataStream& operator <<(QDataStream &out, const CM::ServerParaID &data)
{
    int nID = (int)data;
    out << nID;
    return out;
}

inline QDataStream& operator >>(QDataStream &in, CM::ServerParaID &data)
{
    int nID = 0;
    in >> nID;
    data = (CM::ServerParaID)nID;
    return in;
}

inline QDataStream& operator <<(QDataStream &out, const CM::I_SetServerPara &data)
{
    QList<int> lstID;
    for ( int i=0; i<data.serverParas.size(); i++ )
    {
        lstID.append((int)data.serverParas.at(i));
    }
    out << data.bRet << lstID;
    return out;
}

inline QDataStream& operator >>(QDataStream &in, CM::I_SetServerPara &data)
{
    QList<int> lstServerParaID;
    in >> data.bRet >> lstServerParaID;
    data.serverParas.clear();
    for ( int i=0; i<lstServerParaID.size(); i++ )
    {
        data.serverParas.append((CM::ServerParaID)lstServerParaID.at(i));
    }
    return in;
}

inline QDataStream& operator <<(QDataStream &out, const CM::CM_E_SetAduPara &data)
{
    out << data.adus << data.aduParas;
    return out;
}

inline QDataStream& operator >>(QDataStream &in, CM::CM_E_SetAduPara &data)
{
    in >> data.adus >> data.aduParas;
    return in;
}


#endif


#include "dataexportmanager.h"

#include <QDir>

#include "exportsize.h"
#include "websocket.h"
#include "log.h"

DataExportManager::DataExportManager(QObject *parent) : QObject(parent)
{
    m_bCancel = false;
    m_pThread = new QThread;
    moveToThread(m_pThread);
    m_pThread->start();
}

DataExportManager::~DataExportManager()
{
    if (m_pThread)
    {
        m_pThread->exit();
        m_pThread->wait();
        delete m_pThread;
        m_pThread = nullptr;
    }
}

/*************************************************
功能: 取消数据导出
*************************************************************/
void DataExportManager::cancelExport()
{
    m_bCancel = true;
    m_spExportSize->cancel();
}

/*************************************************
* 输入参数：listDir 文件夹、文件列表
功能: 数据导出槽函数
*************************************************************/
void DataExportManager::onDataExport(const QList<QString> &listDir)
{
    infoLog() << "onDataExport";
    filterDir(listDir);
    /* 计算导出数据大小，用户预估压缩所需要时间 */
    m_spExportSize.reset(new ExportSize());
    int size = m_spExportSize->getDataSize(m_listDataDir);
    sendDataSize(size); // 发送数据大小信息
    if(size <= 0) // 数据小于等于0时，停止后续进度
    {
        emit sigCompressFinish();
        return;
    }
    if(m_bCancel)
    {
        emit sigCompressFinish();
        return;
    }

    /* 清空数据、重新生成目录 */
    system("rm -rf /media/backup/download/*"); // 删除
    system("mkdir -p /media/backup/download/tmp");
    if(m_bCancel)
    {
        emit sigCompressFinish();
        return;
    }

    sendBeginCompress(); // 发送开始压缩信息
    if(m_bCancel)
    {
        emit sigCompressFinish();
        return;
    }

    m_spExportSize->compress(m_listDataDir);
    if(m_bCancel)
    {
        emit sigCompressFinish();
        return;
    }

    sendEndCompress();  // 发送压缩结束信息
}

/*************************************************
* 输入参数：listDir 文件夹、文件列表
功能: 非法文件夹、文件过滤
*************************************************************/
void DataExportManager::filterDir(const QList<QString> &listDir)
{
    QDir  dir;
    for(int i = 0; i < listDir.size(); ++i)
    {
        QFileInfo file(listDir[i]);
        if(dir.exists(listDir[i]) || file.exists())
        {
            m_listDataDir.append(listDir[i]);
        }
        else
        {
            warningLog() << "not exist file or dir" << listDir[i];
        }
    }
}

/*************************************************
功能: 发送数据大小ws信息
*************************************************************/
void DataExportManager::sendDataSize(const int size)
{
    QJsonObject dataSize;
    dataSize .insert("dataExportSize", size);
    QJsonObject data;
    data.insert("ExportData", dataSize);
    infoLog() << "sendDataSize" << size;
    emit sigSendData(QJsonDocument(encapsulationData(data)).toJson());
}

/*************************************************
功能: 发送开始压缩ws信息
*************************************************************/
void DataExportManager::sendBeginCompress()
{
    QJsonObject dataCom;
    dataCom.insert("dataCompress", "begin");
    QJsonObject data;
    data.insert("ExportData", dataCom);
    infoLog() << "sendBeginCompress" ;
    emit sigSendData(QJsonDocument(encapsulationData(data)).toJson());
}

/*************************************************
功能: 发送压缩结束ws信息
*************************************************************/
void DataExportManager::sendEndCompress()
{
    QJsonObject dataCom;
    dataCom.insert("dataCompress", "end");
    dataCom.insert("path", m_spExportSize->getCompressFileName());
    QJsonObject data;
    data.insert("ExportData", dataCom);
    infoLog() << "sendEndCompress" ;
    emit sigSendData(QJsonDocument(encapsulationData(data)).toJson());
    emit sigCompressFinish();
}



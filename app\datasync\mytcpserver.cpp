#include "mytcpserver.h"
#include <QThread>
#include <QNetworkInterface>
#define TCPSERVER_PRINT  //模块打印开关
/*************************************************
函数名： TcpClient(QString strServerIP = "*************", quint16 usSocketPort = 12345, QObject *parent = 0)
输入参数： strServerIP：Server地址
          usSocketPort：端口号
          parent：父指针
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
TcpServer::TcpServer(QString strServerIP, quint16 usSocketPort,QObject *parent):
   AbstractComm(parent)
  ,m_strServerIP(strServerIP)
  ,m_usSocketPort(usSocketPort)
  ,m_bIsOpen(false)
  ,m_pSocket(NULL)
  ,m_pServer(NULL)
{

}

/*************************************************
功能： 析构函数
*************************************************************/
TcpServer::~TcpServer()
{
    close();
}

/*************************************************
功能： 判断是socket是否合法
返回值：false -- 否
       true -- 是
*************************************************/
//bool TcpServer::isSocketValid( LinkState eLinkState )
//{
//    bool bValid = false;
//    if( ( eLinkState == STATE_CONNECTED ) &&
//        ( m_pSocket != NULL ) )
//    {
//        bValid = true;
//    }
//    else
//    {
//#ifdef TCPSERVER_PRINT
//        qWarning( " TcpServer socket is not valid " );
//#endif
//    }
//    return bValid;
//}

/*************************************************
返回值： true -- 成功
        false -- 失败
功能： 开启监听
*************************************************************/
bool TcpServer::startListen( void )
{
    bool bSuccess = true;

    QHostAddress hostAddr;
    hostAddr.setAddress( m_strServerIP );

    if(!m_pServer->listen( hostAddr, m_usSocketPort))
    {
        error( LISTEN_FAILED );
        bSuccess = false;
    }
    else
    {
        #ifdef TCPSERVER_PRINT
        qWarning("listen success");
#endif
        connect(m_pServer, SIGNAL(newConnection()), this, SLOT(onGetNewConnection()));
    }
    return bSuccess;
}

/*************************************************
函数名： open()
输入参数： NULL
输出参数： NULL
返回值： 操作结果
功能： 打开操作
*************************************************************/
bool TcpServer::open()
{
    if( m_pServer == NULL )
    {
        m_pServer = new QTcpServer(this);
    }

    m_bIsOpen = startListen();
    return m_bIsOpen;
}

/*************************************************
函数名： close()
输入参数： NULL
输出参数： NULL
返回值： 操作结果
功能： 关闭操作
*************************************************************/
bool TcpServer::close()
{
    closeServer();
    m_bIsOpen = false;
    return true;
}

/*************************************************
功能： 关闭Server
*************************************************/
void TcpServer::closeServer()
{
    if( NULL != m_pServer )
    {
        disconnect( m_pServer, 0, 0, 0 );
        m_pServer->close();
    }
}

/*************************************************
函数名： read(char *pRecvBuf, unsigned int uiWantLen)
输入参数： uiWantLen：期望读取长度，单位为字节
输出参数： pRecvBuf：接收缓冲
返回值： 操作结果，-1 - 操作失败，其它值 - 实际读取长度
功能： 读取操作
*************************************************************/
int TcpServer::read(char *pRecvBuf, unsigned int uiWantLen)
{
    int iReadLen = -1;

//    if( isSocketValid( m_eLinkState ) )
    {
        if (m_bIsOpen && m_pSocket->bytesAvailable() > 0)
        {
            iReadLen = m_pSocket->read(pRecvBuf, uiWantLen);
        }
    }
    return iReadLen;
}

/*************************************************
函数名： read(unsigned int uiWantLen)
输入参数： uiWantLen：期望读取长度，单位为字节
输出参数： NULL
返回值： 读取到的数据
功能： 读取操作
*************************************************************/
QByteArray TcpServer::read(unsigned int uiWantLen)
{
    QByteArray baData;

//    if( isSocketValid( m_eLinkState ) )
    {
        if (m_bIsOpen && m_pSocket->bytesAvailable() > 0)
        {
            baData = m_pSocket->read(uiWantLen);
        }
    }
    return baData;
}

/*************************************************
函数名： write(const char *pSendBuf, unsigned int uiWantLen)
输入参数： pSendBuf：发送缓冲
          uiWantLen：期望写入长度，单位为字节
输出参数： NULL
返回值： 操作结果，-1 - 操作失败，其它值 - 实际写入长度
功能： 写入操作
*************************************************************/
int TcpServer::write(const char *pSendBuf, unsigned int uiWantLen)
{
    int iWriteLen = -1;

//    if( isSocketValid( m_eLinkState ) )
    {
        if (m_bIsOpen)
        {
            iWriteLen = m_pSocket->write(pSendBuf, uiWantLen);
            m_pSocket->flush();
        }
    }
    return iWriteLen;
}

/*************************************************
函数名： write(const QByteArray &baSendData)
输入参数： baSendData：待发送数据
输出参数： NULL
返回值： 操作结果，-1 - 操作失败，其它值 - 实际写入长度
功能： 写入操作
*************************************************************/
int TcpServer::write(const QByteArray &baSendData)
{
    int iWriteLen = -1;

//    if( isSocketValid( m_eLinkState ) )
    {
        if (m_bIsOpen)
        {
            iWriteLen = m_pSocket->write(baSendData);
            m_pSocket->flush();
        }
    }
    return iWriteLen;
}

/*************************************************
函数名： onReadyRead()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 响应Socket有可读取的数据信号
*************************************************************/
void TcpServer::onReadyRead()
{
    emit sigReadyRead();
}

/*************************************************
函数名： onConnected()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 响应Socket连接上信号
*************************************************************/
void TcpServer::onConnected()
{

}

/*************************************************
函数名： onDisconnected()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 响应Socket断开连接信号
*************************************************************/
void TcpServer::onDisconnected()
{

}

/*************************************************
函数名： onError(QAbstractSocket::SocketError eCode)
输入参数： eCode：错误码
输出参数： NULL
返回值： NULL
功能： 响应Socket连接错误信号
*************************************************************/
void TcpServer::onError(QAbstractSocket::SocketError eCode)
{
    switch (eCode)
    {
        case QAbstractSocket::RemoteHostClosedError:
        {
            //m_pSocket->disconnectFromHost();
            error( REMOTE_HOST_CLOSED );
        }
        break;

        case QAbstractSocket::ConnectionRefusedError:
        {
            error( CONNECT_REFUSED );
        }
        break;

        case QAbstractSocket::HostNotFoundError:
        {
            error( HOST_NOT_FOUND );
        }
        break;
        default:
        break;
    }
}

/*************************************************
功能： new connection 槽函数---获取建立的tcp连接
*************************************************************/
void TcpServer::onGetNewConnection()
{
    qWarning(" get new connection ");
    m_pSocket = m_pServer->nextPendingConnection();
    m_pSocket->moveToThread(thread());

    onConnected();
    connect(m_pSocket, SIGNAL(readyRead()), this, SLOT(onReadyRead()));
    //connect(m_pSocket, SIGNAL(connected()), this, SLOT(onConnected()));
    connect(m_pSocket, SIGNAL(disconnected()), this, SLOT(onDisconnected()));
    connect(m_pSocket, SIGNAL(disconnected()), m_pSocket, SLOT(deleteLater()));
    connect(m_pSocket, SIGNAL(error(QAbstractSocket::SocketError)),
            this, SLOT(onError(QAbstractSocket::SocketError)));
    qDebug() << "tcpserver threadid is " << QThread::currentThreadId();
}

/****************************
功能： 根据error code添加不同打印，方便调试
输入参数:
    eErrorCode -- error类型
*****************************/
void TcpServer::error( ErrorCode eErrorCode )
{
#ifdef TCPSERVER_PRINT
    switch( eErrorCode )
    {
        case LISTEN_FAILED:
        {
            qWarning(" listn failed ");
        }
            break;
        case REMOTE_HOST_CLOSED:
        {
            qWarning(" remote host closed ");
        }
            break;
        case CONNECT_REFUSED:
        {
            qWarning(" connect refused ");
        }
            break;
        case HOST_NOT_FOUND:
        {
            qWarning(" host not found ");
        }
            break;
        default:
        {
            qWarning(" other error ");
        }
            break;
    }
#endif
}

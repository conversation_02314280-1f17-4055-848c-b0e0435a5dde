#ifndef AEFLYCHARTDATAGENERATOR_H
#define AEFLYCHARTDATAGENERATOR_H


#include <QJsonObject>
#include "dbrecord.h"


/**
 * @brief AE 飞行图数据生成器
 * @details 根据 AERecord 数据生成特定格式的 QJsonObject 用于前端显示
 */
class AeFlyChartDataGenerator
{
public:
    /**
     * @brief 构造函数
     * @param aeRecord 输入的 AE 记录数据
     */
    explicit AeFlyChartDataGenerator(const AERecord& aeRecord);

    /**
     * @brief 生成完整的 AE 飞行图数据 JsonObject
     * @return 包含图表数据的 QJsonObject
     */
    QJsonObject generateChartData();

private:
    /**
     * @brief 生成坐标轴信息部分 (axisInfo)
     * @return 包含坐标轴信息的 QJsonObject
     */
    QJsonObject generateAxisInfo();

    /**
     * @brief 生成触发信息部分 (trigger)
     * @return 包含触发信息的 QJsonObject
     */
    QJsonObject generateTriggerInfo();

    /**
     * @brief 生成数据系列部分 (series)
     * @return 包含数据系列的 QJsonArray
     */
    QJsonArray generateSeriesData();

    /**
     * @brief 将脉冲间隔单位枚举转换为字符串
     * @param unit 脉冲间隔单位枚举值
     * @return 对应的单位字符串
     */
    QString getPulseIntervalUnitString(AERecord::PulseIntervalUnit unit);
private:
    const AERecord& m_aeRecord; // 存储传入的 AE 记录引用
};

#endif // AEFLYCHARTDATAGENERATOR_H

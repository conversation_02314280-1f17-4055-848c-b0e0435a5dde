#include <QDebug>
#include <QThread>
#include <QFile>
#include <QDateTime>
#include "syncmanager.h"
#ifdef SERVER_BLUETOOTH
#include "bluetoothserver.h"
#endif
#include "protocolmanager.h"
#include "iprotocolbean.h"
#include "abstractcomm.h"
#include "testdatastruct.h"
#include "syncserver.h"
#include "networkdevicemanager.h"
#include "tcpserver.h"
#include "log.h"
#include "cmcomm.h"


//----静态变量初始化----
quint32 SyncManager::m_uSrcAddress = 1010;
quint32 SyncManager::m_uDestAddress = 500;
//----end


/*************************************************
函数名： SyncManager
功能： 构造对象
*************************************************************/
SyncManager::SyncManager()
{
    m_pSyncServer = new SyncServer(this);

    m_iReadCnt = 0;

    // 创建蓝牙通讯
    //createCommunicateLink(LINK_BLUETOOTH);
    createCommunicateLink(LINK_GROUP_WIFI);

    m_pCM = QSharedPointer<CMComm>(new CMComm());

    connect( (CMComm*)m_pCM.data(), SIGNAL(sigStationMD5(const QString&)), this, SIGNAL(sigStationMD5(const QString&)));

    m_pThread = new QThread;
    m_pThread->start();
    moveToThread(m_pThread);
}

void SyncManager::initInstance()
{
    instance();
}

SyncManager &SyncManager::instance()
{
    static SyncManager s_instance;
    return s_instance;
}

/*************************************************
函数名： ~MonitorManager
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 析构对象
*************************************************************/
SyncManager::~SyncManager()
{

}

/*************************************************
函数名： sendData
输入参数：data 需要发送的数据
         stParam 协议参数
输出参数： NULL
返回值： true-发送成功 false-发送失败
功能： 阻塞调用，发送数据
*************************************************************/
bool SyncManager::sendData(const QByteArray &data, const ProtocolParam &stParam)
{
    QMutexLocker lock(&m_mutex);

    bool bRet = false;
    if ( m_spProtocolBean )
    {
        bRet = m_spProtocolBean->sendData(data, stParam);
    }
    else
    {
        outputMsg("pProtocolBean is null.");
    }

    return bRet;
}

/*************************************************
函数名： onDataToBusinessLayer
输入参数： stParam--协议参数
         data--接收到的数据
输出参数： NULL
返回值： NULL
功能： 收到数据并处理
*************************************************************/
void SyncManager::onDataToBusinessLayer(ProtocolParam stParam, const QByteArray &data)
{
    Q_UNUSED(stParam)

    // 获取信号发送方
    IProtocolBean *pProtocolBean = dynamic_cast<IProtocolBean*>(sender());
    if (NULL == pProtocolBean)
    {
        outputMsg( "pProtocolBean is null." );
        return;
    }

    // 获取命令字
    CommandType cmdType = m_pSyncServer->getCmdType(data);
    switch ( cmdType )
    {
    case CMD_CONN_REQ:          // 连接请求
        m_pSyncServer->procConnectRequest(data);
        break;
    case CMD_CONFIG_REQ:        // 配置内容请求
        m_pSyncServer->procConfigRequest(data);
        break;
    case CMD_SYNC_INFO_REQ:     // 数据信息请求
        m_pSyncServer->procDbInfoRequest(data);
        break;
    case CMD_SYNC_DATA_REQ:     // 取数据请求
        m_pSyncServer->addToBuffer(data);
        break;

    case CMD_SYNC_DBFILE_HEAD_REQ:
        m_pSyncServer->procDbFileHeadRequest(data);
        break;

    case CMD_SYNC_DBFILE_CONTENT_REQ:
        m_pSyncServer->procDbFileContentRequest(data);
        break;

    case CMD_STOP_SYNC:         // 停止采集
        m_pSyncServer->stopSync();
        break;
    default:
        outputMsg("Unknown command.");
        break;
    }
}

/*************************************************
函数名： createCommunicateLink
输入参数： linkType--链路方式
输出参数： NULL
返回值： 结果状态--true表示成功
功能： 建立通讯链路
*************************************************************/
bool SyncManager::createCommunicateLink(const LinkType &linkType)
{
    switch ( linkType )
    {
#ifdef SERVER_BLUETOOTH
    case DataSync::LINK_BLUETOOTH:
        m_pAbstractComm = NetworkDeviceManager::instance().getBluetoothServer();
//        connect( m_pAbstractComm.data(),SIGNAL(sigReadyRead()),
//                 this,SLOT(onDataRead()));
        break;
#endif
    case DataSync::LINK_GROUP_WIFI:
        m_pAbstractComm.reset( new TcpServer() );
        break;
    default:
        break;
    }

    m_linkType = linkType;

    if ( m_pAbstractComm.isNull() )
    {
        logError("Fail to get AbstractComm, type=" + QString::number((int)m_linkType));
        return false;
    }

    if ( !m_pAbstractComm->open() )
    {
        logError("Fail to open AbstractComm.");
        return false;
    }
    outputMsg("m_pAbstractComm->open() succeed!");

    // 创建协议组件
    m_spProtocolBean = ProtocolManager::instance().createProtocolBean(m_pAbstractComm);

    QList<Route> listRoute;
    Route route;
    route.ullDestAddress = m_pSyncServer->getPrtcParam().ullAddressSrc;
    listRoute.push_back(route);
    m_spProtocolBean->setRoute(listRoute);

    if ( m_spProtocolBean == NULL )
    {
        logError("Fail to get createProtocolBean.");
        return false;
    }
    // 绑定接收数据的信号
    QObject::connect( m_spProtocolBean.data(), SIGNAL(sigDataToBusinessLayer(ProtocolParam, QByteArray)), this,
                      SLOT(onDataToBusinessLayer(ProtocolParam, QByteArray)) );

    outputMsg("succeed to createCommunicateLink.");

    return true;
}

//void SyncManager::onDataRead( void )
//{
//    static qint64 lTime = QDateTime::currentMSecsSinceEpoch();
//    QByteArray ba = m_pAbstractComm.data()->read( 1024 * 1024 );
//    m_iReadCnt += ba.size();
//    qDebug() << "SyncManager::onDataRead data size is:" << ba.size()
//             << "current all size is:" << m_iReadCnt;
//    if( m_iReadCnt == 4983980 )
//    {
//        qDebug() << "*******************8" << QDateTime::currentMSecsSinceEpoch() - lTime;
//    }
//}

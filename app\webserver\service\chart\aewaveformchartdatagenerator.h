#ifndef AEWAVEFORMCHARTDATAGENERATOR_H
#define AEWAVEFORMCHARTDATAGENERATOR_H

#include <QJsonObject>

class AERecord;

/**
 * @brief AE 波形图数据生成器
 * @details 根据 AERecord 数据生成特定格式的 QJsonObject 用于前端显示
 */
class AEWaveformChartDataGenerator
{
public:
    /**
     * @brief 构造函数
     * @param aeRecord 输入的 AE 记录数据
     */
    explicit AEWaveformChartDataGenerator(const AERecord& aeRecord);

    /**
     * @brief 生成完整的 AE 波形图数据 JsonObject
     * @return 包含图表数据的 QJsonObject
     */
    QJsonObject generateChartData();

private:
    /**
     * @brief 生成坐标轴信息部分 (axisInfo)
     * @return 包含坐标轴信息的 QJsonObject
     */
    QJsonObject generateAxisInfo();

    /**
     * @brief 生成触发信息部分 (trigger)
     * @return 包含触发信息的 QJsonObject
     */
    QJsonObject generateTriggerInfo();

    /**
     * @brief 生成数据系列部分 (series)
     * @return 包含数据系列的 QJsonArray
     */
    QJsonArray generateSeriesData();

private:
    const AERecord& m_aeRecord; // 存储传入的 AE 记录引用
};

#endif // AEWAVEFORMCHARTDATAGENERATOR_H

/*
* Copyright (c) 2017.7，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：testdatastruct.h
*
* 初始版本：1.0.1    [7.26-10]
* 作者：wujun
* 创建日期：2017年7月14日
* 摘要：该文件主要定义同步测试数据所需的结构体
*/

#ifndef TESTDATASTRUCT_H
#define TESTDATASTRUCT_H

//#include "configdatastruct.h"
#include "devicetree.h"
#include "dbrecord.h"
#include <QDateTime>
#include <QByteArray>

namespace DataSync
{

// UHF数据表的结构体
typedef struct _UHFData
{
    int    battery;        //电量
    int    recordID;       //前端给的id（1~1000）
    int    freq;           //频率
    int    gain;           //增益
    int    bandWidth;      //带宽
    int    syncType;       //同步类型
    int    syncFlag;       //同步标志
    int    period;         //周期
    int     phase;          //相位
    float   maxQ;           //Prps数组中的最大值
    float   minQ;           //最小值
    float   avgQ;           //平均值
    int     pdCount;        //放电次数
    int     isPd;           //是否为局放（0-不是，1-是）
    float   pdPeriod;       //局放相位
    int     pdType;         //局放类型
    QByteArray array;       //Prps数组

} UHFData;

// AE数据表的结构体
typedef struct _AEData
{
    int    freq;           //频率
    int    gain;           //增益
    int    syncType;       //同步类型
    int    syncFlag;       //同步标志
    float  maxV;           //最大值
    float  rmsV;           //有效值
    float  freq1;          //频率分量1
    float  freq2;          //频率分量2
} AEData;

typedef struct _TbData
{
    ADUChannelType channelType;     //通道类型（用于判断何种类型，不入库）

    //----以下只有一个有效----
    AERecord aeData;
    TEVRecord tevData;
    PRPSRecord uhfData;
    PRPSRecord hfctData;
    //----end

} TbData;


// 自定义同步数据信息请求的数据结构
typedef struct _CustmSyncInfoReqst
{
    ADUChannelType channelType;     //通道类型
    QString devCode;                //设备外部编号
    QString tpCode;                 //测点外部编号
    QDateTime dtStart;              //数据起始时间
    QDateTime dtEnd;                //数据终止时间
} CustmSyncInfoReqst ;

// 自定义同步数据信息应答的数据结构（单个）
typedef struct _CustmSyncInfoReply
{
    QString dbName;                 //数据库名
    QString tbName;                 //表名
    uint    startIndex;             //最旧数据序号（表内自增序号）
    uint    endIndex;               //最新数据序号
} CustmSyncInfoReply;

// 读取数据的索引
typedef struct _DbIndex
{
    ADUChannelType channelType;     //通道类型
    QString audId;                  //前端ID
    QString channelId;              //通道ID
    QString autoId;                 //自增序号
} DbIndex;

typedef QList<CustmSyncInfoReply> CustmSyncInfoReplyList;

// 自定义同步取数据请求
typedef struct _CustmSyncDataReqst
{
    QString dbName;                 //库名
    QString tbName;                 //表名
    uint    index;                  //数据最新序号（表内自增序号）
} CustmSyncDataReqst;

// 自定义同步取数据应答
typedef struct _CustmSyncDataReply
{
    QString dbName;                 //库名
    QString tbName;                 //表名
    uint    globalID;               //全局ID

    TbData  tbData;                 //记录内容

} CustmSyncDataReply;


// 默认同步数据信息应答的数据结构（单个）
typedef struct _SyncInfoReply
{
    QString dbName;              //库名
    uint    startID;             //最旧数据序号（global ID）
    uint    endID;               //最新数据序号
} SyncInfoReply;


// 默认同步取数据请求
typedef struct _SyncDataReqst
{
    QString dbName;               //库名
    uint    globalID;             //全局id
} SyncDataReqst;

// 默认同步取数据应答
typedef struct _SyncDataReply
{
    QString dbName;              //库名
    uint    globalID;            //全局id
    TbData  tbData;              //记录内容

} SyncDataReply;

// 连接应答
typedef struct _ConnReply
{
    bool bAgree;            //是否同意连接
    QString hostCode;       //主机标识
    QString stationCode;    //站点编码
    QString stationName;    //站点名
} ConnReply;

}

#endif


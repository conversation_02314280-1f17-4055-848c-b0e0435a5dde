/*
* Copyright (c) 2017.7，南京华乘电气科技有限公司
* All rights reserved.
*
* syncclient.h
*
* 初始版本：1.0
* 作者：wujun
* 创建日期：2017年07月21日
* 摘要：包含同步业务处理流程

* 当前版本：1.0
*/
#ifndef __SYNCCLIENT_H_
#define __SYNCCLIENT_H_

#include "processdata.h"
#include "protocoldefine.h"
#include "patroldefines.h"
#include <QThread>
#include <QTimer>
#include <QReadWriteLock>
#include <QMutex>
#include <QFile>


// 工作状态
enum JobStep
{
    NoJob = 0,
    Conn<PERSON><PERSON>,
    Config<PERSON>ob,
    DbInfoJob,
    DbDataJob
    //StopJob
};


class SyncManager;
class SyncClient;

class SyncClient: public QObject
{
    Q_OBJECT

public:
    /*************************************************
    函数名： SyncClient
    输入参数：pMain--同步管理器指针
    输出参数： NULL
    返回值： NULL
    功能：构造函数
    *************************************************************/
    SyncClient(SyncManager *pMain);

    /*************************************************
    函数名： ~SyncClient
    输入参数：NULL
    输出参数： NULL
    返回值： NULL
    功能：析构函数
    *************************************************************/
    ~SyncClient();

    /*************************************************
    函数名： connectToMonitor
    输入参数：NULL
    输出参数： NULL
    返回值： 对方主机id
    功能： 连接主机，并返回主机id
    *************************************************************/
    QString connectToMonitor(uint nOverTime);

    /*************************************************
    函数名： getStationInfo
    输入参数： NULL
    输出参数： sInfo--站点信息
    返回值： 结果状态--已连接返回true，否则返回false
    功能： 获取站点信息
    *************************************************************/
    bool getStationInfo(PatrolServiceNS::StationOutlineInfo &sInfo);

    bool IsStopSync();

    //--------------------
    struct DataInfo
    {
        QString strPointId;
        ADUChannelType eChannelType;
        QList<int> lstStart;
        QList<int> lstEnd;
    };

    QList<DataInfo> m_dbRequestList;

    /*************************************************
    函数名： sendSyncDataRequest
    输入参数：syncTask--同步任务
    输出参数： NULL
    返回值： 处理结果状态
    功能：线程函数，逐条发送取数据请求
    *************************************************************/
    bool createSyncDataRequest(const QMap<QString, QMap<ADUChannelType, QList<int> > > &syncTask);

    //-------------------

signals:
    /*************************************************
    信号名： sigConnect
    输入参数：NULL
    功能：需要请求连接时，发送此信号
    *************************************************************/
    void sigConnect();

    /*************************************************
    信号名： sigConfigReq
    输入参数：NULL
    功能：需要请求配置文件时，发送此信号
    *************************************************************/
    void sigConfigReq(QVector<PatrolServiceNS::StationOutlineInfo> stationInfo);

    /*************************************************
    信号名： sigSyncDb
    输入参数：NULL
    功能：需要同步数据库时，发送此信号
    *************************************************************/
    void sigSyncDb(PatrolServiceNS::SyncDataParam param);

    /*************************************************
    参数： 从目标获取的站点配置信息列表
    功能： 信号，获取配置的概要信息的结果()
    *************************************************/
    void sigConfigInfos(QVector<PatrolServiceNS::StationOutlineInfo> infos);

    /*************************************************
    信号名： sigStartTimer
    输入参数： nOvertime--定時器間隔
    功能：啟動定時器（多線程問題）
    *************************************************************/
    void sigStartTimer(uint nOvertime);

    void sigStopTimer();

    void sigProcDbData();

    void sigProcDbFile();

    void sigCancelDB();

    void sigSyncDbInfoRep(QByteArray);

private slots:

    /*************************************************
    函数名： onConnected
    输入参数：NULL
    输出参数： NULL
    返回值： 处理结果状态
    功能：发送连接请求
    *************************************************************/
    bool onConnected();

    /*************************************************
    函数名： onCancelDB
    输入参数：NULL
    输出参数： NULL
    返回值： NULL
    功能： 取消当前数据库
    *************************************************************/
    void onCancelDB();

    /*************************************************
    函数名： configReq
    输入参数：NULL
    输出参数： NULL
    返回值： true-发送成功 false-发送失败
    功能： 发送配置内容请求
    *************************************************************/
    bool onConfigReqed(QVector<PatrolServiceNS::StationOutlineInfo> stationInfo);

    /*************************************************
    函数名： onSyncDb
    输入参数：param--同步参数
    输出参数： NULL
    返回值： NULL
    功能： 同步数据
    *************************************************************/
    void onSyncDb(PatrolServiceNS::SyncDataParam param);

    /*************************************************
    函数名： onStartTimer
    输入参数： nOvertime--定時器間隔
    输出参数： NULL
    返回值： NULL
    功能： 啟動定時器
    *************************************************************/
    void onStartTimer(uint nOvertime);

    void onStopTimer();

    //void onProcDbData();

    void onProcDbFile();

    void onSyncDbInfoRep(QByteArray baData);

protected:
    /*************************************************
    函数名： timerEvent
    输入参数： event
    输出参数： NULL
    返回值： NULL
    功能： 响应定时器
    *************************************************************/
    virtual void timerEvent(QTimerEvent *event);

public:

    /*************************************************
    函数名： getStationInfo
    输入参数：NULL
    输出参数： NULL
    返回值： 站点概要信息
    功能： 获取站点概要信息
    *************************************************************/
    PatrolServiceNS::StationOutlineInfo getStationInfo();

    /*************************************************
    函数名： getCmdType
    输入参数： data--数据内容
    输出参数： NULL
    返回值： 命令字
    功能： 获取命令字类型
    *************************************************************/
    CommandType getCmdType(const QByteArray &data);

    /*************************************************
    函数名： procConnectReply
    输入参数：data--连接应答内容
    输出参数： NULL
    返回值： 处理结果状态
    功能： 处理连接应答
    *************************************************************/
    bool procConnectReply(const QByteArray &data);

    /*************************************************
    函数名： procConfigReply
    输入参数：data--配置文件应答内容
    输出参数： NULL
    返回值： 处理结果状态
    功能： 处理配置文件应答内容
    *************************************************************/
    bool procConfigReply(const QByteArray &data);

    /*************************************************
    函数名： procCustSyncInfoReply
    输入参数：data--自定义同步数据信息应答内容
    输出参数： NULL
    返回值： 处理结果状态
    功能： 处理自定义同步数据信息应答内容
    *************************************************************/
    //bool procCustSyncInfoReply(const QByteArray &data);

    /*************************************************
    函数名： procDefSyncInfoReply
    输入参数：data--默认同步数据信息应答内容
    输出参数： NULL
    返回值： 处理结果状态
    功能： 处理默认同步数据信息应答内容
    *************************************************************/
    bool procSyncInfoReply(const QByteArray &data);

    void doSyncInfoRep(const QByteArray &data);

    bool askForDbData();

    /*************************************************
    函数名： procSyncDataReply
    输入参数：data--同步取数据应答内容
    输出参数： NULL
    返回值： 处理结果状态
    功能： 处理同步取数据应答
    *************************************************************/
    bool procSyncDataReply(const QByteArray &data);

    void addToFileBuff(const QByteArray &data);

    bool procSyncDbFileHead(const QByteArray &data);

    bool sendDbFileContentReq();

    void sendProgress();

    /*************************************************
    函数名： stopSyncJob
    功能： 停止同步
    *************************************************************/
    void stopSyncJob();

    /*************************************************
    函数名： procCustmSyncDataReply
    输入参数：data--同步取数据应答内容
    输出参数： NULL
    返回值： 处理结果状态
    功能： 处理同步取数据应答
    *************************************************************/
    //bool procCustmSyncDataReply(const QByteArray &data);

    /*************************************************
    函数名： sendData
    输入参数：data--数据内容
    输出参数： NULL
    返回值： 发送结果状态
    功能： 发送数据
    *************************************************************/
    bool sendData(const QByteArray &data, JobStep step);

    /*************************************************
    函数名： getCurJob
    输入参数：NULL
    输出参数： NULL
    返回值： 返回当前工作状态
    功能：获取当前工作状态
    *************************************************************/
    JobStep getCurJob();

    /*************************************************
    函数名： getAllDbData
    输入参数：allTask--同步数据任务列表
    输出参数： NULL
    返回值： 处理结果状态
    功能： 同步数据
    *************************************************************/
    //bool getAllDbData(const QMap<QString, QVector<quint64> > allTask);

    // 是否通过密码验证
    bool m_bConnected;

    /*************************************************
    函数名： initConnectState
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 初始化连接状态为false
    *************************************************************/
    void initConnectState();

    //int getBuffSize();

private:
    /*************************************************
    函数名： getLostId
    输入参数：org--s1010的id列表（不能有重复）；
             back--s00的id列表（不能有重复）
    输出参数： NULL
    返回值： 返回缺少的id列表
    功能：比较org和back，返回back缺少的id列表
    *************************************************************/
    QList<int> getLostId(const QList<int> &org, const QList<int> &back);

    bool procSyncDbFileContent(const QByteArray &data);

    void closeFile();

private:

    SyncManager *m_pMain;           // 同步管理器
    DataProcesser m_dataProcesser;  // 数据解析组包
    ProtocolParam m_ptParam;        // 通讯协议参数

    QThread m_thread;               // 运行线程

    PatrolServiceNS::StationOutlineInfo m_stationInfo;   // 当前对接的站点信息
    QString m_strMonitorId;                              // 当前对接的主机ID

    JobStep m_curJob;           // 当前任务

    //QTimer  m_waitTimer;        // 超时定时器
    int     m_iWaitTimer;       // 超时定时器

    uint m_syncDbTotal;         // 要同步的总记录数
    uint m_syncDbNum;           // 已经同步的记录数
    uint m_syncDbFailed;        // 同步失败的记录数

    bool m_bSetDb;              // 是否已设置db

    QList<QByteArray> m_fileBuff;

    QMutex  m_mutex;

    bool m_bStopSync;

    QString m_curPointID;
    ADUChannelType m_curChannelType;

    QFile *m_pFile;
    quint64 m_lByteReceived;
    quint64 m_lTotalSize;

    uint m_nTotalFile;
    uint m_nCurIndex;
};

#endif

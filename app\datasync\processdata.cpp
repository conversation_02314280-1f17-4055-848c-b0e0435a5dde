#include <QBuffer>
#include <QDataStream>
#include "processdata.h"
#include "serialize.h"
#include "webserver/commandfuns.h"


CommandType DataProcesser::getCmdType(const QByteArray &data)
{
    if ( data.size() < int(2 * sizeof(int)))
    {
        return CMD_INVALID;
    }

    int nCmdType = 0;
    int nLen = 0;

    QByteArray baTmp = data;
    QBuffer buffer(&baTmp);
    buffer.open(QIODevice::ReadOnly);
    QDataStream in(&buffer);
    in >> nCmdType >> nLen;
    buffer.close();

    CommandType eCmdType;
    if ( nCmdType<CMD_CONN_REQ || nCmdType>=CMD_NUM_TOTAL )
    {
        eCmdType = CMD_INVALID;
    }
    else if ( nLen != int(data.size() - 2 * sizeof(int)) )
    {
        eCmdType = CMD_INVALID;
    }
    else
    {
        eCmdType = (CommandType)nCmdType;
    }

    return eCmdType;
}

QString DataProcesser::parseConnReq(const QByteArray &data)
{
    QString strCipher;
    QByteArray baTmp = data.mid(8);
    QBuffer buffer(&baTmp);
    buffer.open(QIODevice::ReadOnly);
    QDataStream in(&buffer);
    in >> strCipher;
    buffer.close();

    return strCipher;
}

/*************************************************
函数名： packConnRep
输入参数：reply--连接应答内容
输出参数：data--序列化后的数据
返回值： NULL
功能： 组包连接应答
*****************************************************/
void DataProcesser::packConnRep(QByteArray &data, const ConnReply &reply)
{
    data.clear();
    QDataStream out(&data, QIODevice::WriteOnly);
    int nLen = 0;
    out << CMD_CONN_REP << nLen << reply;
    nLen = data.size() - 8;
    if ( nLen > 0 )
    {
        out.device()->seek(4);
        out << nLen;
    }
}

/*************************************************
函数名： packConfigRep
输入参数：cNode--配置文件应答内容
输出参数：data--序列化后的数据
返回值： NULL
功能： 组包配置文件应答
*****************************************************/
void DataProcesser::packConfigRep(QByteArray &data, const ConfigNode &cNode)
{
    data.clear();
    QDataStream out(&data, QIODevice::WriteOnly);
    int nLen = 0;
    out << CMD_CONFIG_REP << nLen << cNode;
    nLen = data.size() - 8;
    if ( nLen > 0 )
    {
        out.device()->seek(4);
        out << nLen;
    }
}

void DataProcesser::packConfigRep(QByteArray &data, const ConfigNode &cNode, const QByteArray &baConfig)
{
    data.clear();
    QDataStream out(&data, QIODevice::WriteOnly);
    int nLen = 0;
    out << CMD_CONFIG_REP << nLen << cNode << baConfig;
    nLen = data.size() - 8;
    if ( nLen > 0 )
    {
        out.device()->seek(4);
        out << nLen;
    }
}

/*************************************************
函数名： parseConfigRep
输入参数： data--數據內容
输出参数： cNode--反序列化后的結果
返回值： NULL
功能： 解析配置內容應答
*****************************************************/
void DataProcesser::parseConfigRep(const QByteArray &data, ConfigNode &cNode)
{
    QByteArray baTmp = data.mid(8);
    QDataStream in(&baTmp, QIODevice::ReadOnly);
    in >> cNode;
}

/*************************************************
函数名： parseConfigReq
输入参数： data--數據內容
输出参数： NULL
返回值： 站点id
功能： 解析配置内容请求
*****************************************************/
QString DataProcesser::parseConfigReq(const QByteArray &data)
{
    QString stationCode;
    QByteArray baTmp = data.mid(8);
    QBuffer buffer(&baTmp);
    buffer.open(QIODevice::ReadOnly);
    QDataStream in(&buffer);
    in >> stationCode;
    buffer.close();

    return stationCode;
}

/*************************************************
函数名： parseSyncInfoReq
输入参数：data--序列化后的数据
输出参数：param--同步参数
返回值： NULL
功能： 解析同步数据信息请求
*****************************************************/
void DataProcesser::parseSyncInfoReq(const QByteArray &data, PatrolServiceNS::SyncDataParam &param)
{
    QByteArray baTmp = data.mid(8);
    QDataStream in(&baTmp, QIODevice::ReadOnly);
    in >> param;
}

/*************************************************
函数名： parseSyncInfoReq
输入参数：data--序列化后的数据
输出参数： info--测点通道信息；startTime--起始时间；endTime--终止时间；
返回值： NULL
功能： 解析同步数据信息请求
*****************************************************/
void DataProcesser::parseSyncInfoReq(const QByteArray &data, QMap<QString, QList<ADUChannelType> > &info, QDateTime &statTime, QDateTime &endTime)
{
    QByteArray baTmp = data.mid(8);
    QDataStream in(&baTmp, QIODevice::ReadOnly);
    in >> info >> statTime >> endTime;
}

/*************************************************
函数名： packSyncInfoRep
输入参数：param--同步参数
输出参数：data--序列化后的数据
返回值： NULL
功能： 组包同步数据信息应答
*****************************************************/
void DataProcesser::packSyncInfoRep(QByteArray &data, const QList<SyncInfoReply> &reply)
{
    data.clear();
    QDataStream out(&data, QIODevice::WriteOnly);
    int nLen = 0;
    out << CMD_SYNC_INFO_REQ << nLen << reply;
    nLen = data.size() - 8;
    if (nLen > 0)
    {
        out.device()->seek(4);
        out << nLen;
    }
}

/*************************************************
函数名： packSyncInfoRep
输入参数： reply--数据信息应答
输出参数： data--序列化后的数据
返回值： NULL
功能： 组包同步数据信息应答
*****************************************************/
void DataProcesser::packSyncInfoRep(QByteArray &data, QMap<QString, QMap<ADUChannelType, QList<int> > > reply)
{
    data.clear();
    QDataStream out(&data, QIODevice::WriteOnly);
    int nLen = 0;
    out << CMD_SYNC_INFO_REP << nLen << reply;
    nLen = data.size() - 8;
    if (nLen > 0)
    {
        out.device()->seek(4);
        out << nLen;
    }
}

void DataProcesser::packSyncFileContent(QByteArray &data, const QByteArray &content)
{
    QDataStream out(&data, QIODevice::WriteOnly);
    int nLen = 0;
    out << CMD_SYNC_DBFILE_CONTENT_REP << nLen << content;
    nLen = data.size() - 8;
    if ( nLen > 0 )
    {
        out.device()->seek(4);
        out << nLen;
    }
    out.device()->close();

    //qDebug() << "send size: " << content.size();
}

/*************************************************
函数名： parseSyncDataReq
输入参数：data--序列化后的数据
输出参数：vecGID--globalID列表
返回值： NULL
功能： 解析取数据请求
*****************************************************/
void DataProcesser::parseSyncDataReq(const QByteArray &data, QVector<quint64> vecGID)
{
    QByteArray baTmp = data.mid(8);
    QDataStream in(&baTmp, QIODevice::ReadOnly);
    in >> vecGID;
}

/*************************************************
函数名： parseSyncDataReq
输入参数：data--序列化后的数据
输出参数：pointId--测点id；
         eType--通道类型；
         ids--自增序号列表
返回值： NULL
功能： 解析取数据请求
*****************************************************/
void DataProcesser::parseSyncDataReq(const QByteArray &data, QString &pointId, ADUChannelType &eType, QList<int> &ids)
{
    QByteArray baTmp = data.mid(8);
    QDataStream in(&baTmp, QIODevice::ReadOnly);
    in >> pointId >> eType >> ids;
}

/*************************************************
函数名： packSyncDataRep
输入参数：data--序列化后的数据
输出参数：reply--单条数据记录
返回值： NULL
功能： 组包取数据应答(单条数据)
*****************************************************/
void DataProcesser::packSyncDataRep(QByteArray &data, const TbData &reply)
{
    data.clear();
    QDataStream out(&data, QIODevice::WriteOnly);
    int nLen = 0;
    out << CMD_SYNC_DATA_REP << nLen << reply;
    nLen = data.size() - 8;
    if (nLen > 0)
    {
        out.device()->seek(4);
        out << nLen;
    }
}

/*************************************************
函数名： parseDbDataRep
输入参数： data--序列化后的数据内容
输出参数： reply--反序列化后的結果
返回值： NULL
功能： 解析数据库记录内容（测试）
*****************************************************/
void DataProcesser::parseDbDataRep(const QByteArray &data, TbData &reply)
{
    QByteArray baTmp = data.mid(8);
    QDataStream in(&baTmp, QIODevice::ReadOnly);
    in >> reply;
}

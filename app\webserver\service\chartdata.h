/*
* Copyright (c) 2017.1，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：chartdata.h
*
* 初始版本：1.0
* 作者：贺小强
* 创建日期：2017年1月6日
* 摘要：监测表页面接口，提供监测表数据和实时数据
*
*/

#ifndef CHARTDATA_H
#define CHARTDATA_H

#include <QJsonObject>
#include "devicetree.h"
#include "dbserver.h"

class ChartData : public QJsonObject
{
public:
    /************************************************
    * 函数名:  ChartData
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  构造函数
    ************************************************/
    ChartData();

    /************************************************************
     * 功能：PRPS图谱转为PRPD图谱
     * 描述：将单条PRPS图谱数据转为对应的PRPD图谱数据
     * 输入参数：
     *      prps -- PRPS数据（单位dB/dBm)，[周期][相位]
     *      lowerBound -- 幅值下限（单位dB/dBm)
     *      upperBound -- 幅值上限（单位dB/dBm)
     *      ampNum -- 幅值分区数
     * 输出参数：
     *      prpd -- PRPD数据，大小为相位数*幅值分区数
     * 返回值：
     *      true:成功 false:失败
    *************************************************************/
    bool getPrpdFromPrps(const std::vector<std::vector<float> >& prps, float lowerBound, float upperBound, unsigned int ampNum, std::vector<std::vector<int> >& prpd);

    /*************************************************
     * 函数功能： 将一维的prps转为二维的prps[周期][相位]
     * 输入参数：
     *      prps --  一维prps,大小为iTNum*iPhaseNum,按周期*相位的顺序排列
     *      iTNum  --  prps的周期数
     *      iPhaseNum  --  prps的相位数
     * 输出参数：
     *      无
     * 返回参数：
     *      二维PRPS数据[周期][相位]
    *************************************************************/
    std::vector<std::vector<float> > convertPrpsTo2D(std::vector<float> prps, int iTNum, int iPhaseNum);

    /************************************************
    * 函数名:  reFresh
    * 输入参数:  eChannelType -- 通道类型
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  根据条件刷新数据
    ************************************************/
    void reFresh(ADUChannelType eChannelType);

    /************************************************
    * 函数名:  channelData
    * 输入参数:  strChannelName -- 通道名
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  某测点的最新数据
    ************************************************/
    void refreshLastRecord(const QString &strChannelName);

    /************************************************
    * 函数名:  getChartData
    * 输入参数:  strPointId -- 测点类型
    *           eChannelType -- 通道类型
    *           iDataType -- 数据类型
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取指定图谱数据
    ************************************************/
    void getChartData(const QString &strPointId, ADUChannelType eChannelType, int iDataType, int iDataId);

    /************************************************
    * 函数名:  getChartData
    * 输入参数:  strPointId -- 测点类型
    *           eChannelType -- 通道类型
    *           iDataType -- 数据类型
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取指定图谱数据
    ************************************************/
    void getChartParamData(const QString &strPointId, ADUChannelType eChannelType, int iDataType, int iDataId);

    /************************************************
    * 函数名:  getTrendData
    * 输入参数:  strPointId -- 测点类型
    *           eChannelType -- 通道类型
    *           iDataType -- 数据类型
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取指定趋势图谱数据
    ************************************************/
    void getTrendData(const QString &strPointId, ADUChannelType eChannelType, const QDateTime &startDate, const QDateTime &endDate );

    void getHistoryDatas(const QString &strPointId,
                         ADUChannelType eChannelType,
                         const QDateTime &startDate,
                         const QDateTime &endDate,
                         int iPageSize, int iPageCount);

    /************************************************
    * 函数名:  getAEData
    * 输入参数:  aeRecord -- ae数据
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  转换ae波形数据
    ************************************************/
    void getAEData(const AERecord &aeRecord);



    /************************************************
    * 函数名:  getPRPSData
    * 输入参数:  PrpsRecord -- prps数据
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  转换PRPS数据
    ************************************************/
    void getPRPSData(ADUChannelType eChannelType, const PRPSRecord &PrpsRecord);

    /************************************************
    * 函数名:  getPRPDData
    * 输入参数:  PrpsRecord -- prps数据
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  转换PRPD数据
    ************************************************/
    void getPRPDData(ADUChannelType eChannelType, const PRPSRecord &PrpsRecord);

    /************************************************
    * 函数名:  getTEVPRPSData
    * 输入参数:  PrpsRecord -- prps数据
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  转换PRPS数据
    ************************************************/
    void getTEVPRPSData(ADUChannelType eChannelType, const PRPSRecord &PrpsRecord);

    /************************************************
    * 函数名:  getTEVPRPDData
    * 输入参数:  PrpsRecord -- prps数据
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  转换PRPD数据
    ************************************************/
    void getTEVPRPDData(ADUChannelType eChannelType, const PRPSRecord &PrpsRecord);

    /************************************************
    * 函数名:  getMechLoopData
    * 输入参数:  stMechRecord -- 机械特性数据
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  机械特性线圈数据
    ************************************************/
    void getMechLoopData(const MechDefine::MechISRecord &stMechRecord);

    /************************************************
    * 函数名:  getMechOriginalData
    * 输入参数:  stMechRecord -- 机械特性数据
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  机械特性原始电流数据
    ************************************************/
    void getMechOriginalData(const MechDefine::MechISRecord &stMechRecord);

    /************************************************
    * 函数名:  getMechMechData
    * 输入参数:  stMechRecord -- 机械特性数据
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  机械特性电机数据
    ************************************************/
    void getMechMechData(const MechDefine::MechISRecord &stMechRecord);

    /************************************************
    * 函数名:  getArresterData
    * 输入参数:  stArresterData -- 机械特性数据
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  避雷器数据
    ************************************************/
    void getArresterData(const ArresterRecord &stArresterRecord);

    /************************************************
    * 函数名:  getArresterData
    * 输入参数:  stArresterData -- 机械特性数据
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  避雷器数据
    ************************************************/
    void getArresterParamData(const ArresterRecord &stArresterRecord, QJsonObject &jsonData);

    /************************************************
    * 函数名:  getVibrationData
    * 输入参数:  getVibrationData -- 振动数据
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  振动数据
    ************************************************/
    void getVibrationTimeDomainData(const VibrationRecord &stVibrationRecord, int iDataType);

    /************************************************
    * 函数名:  getVibrationData
    * 输入参数:  getVibrationData -- 振动数据
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  振动数据
    ************************************************/
    void getVibrationTimeDomainData(const QString &strPointId, int iDataType);

    /************************************************
    * 函数名:  getVibrationData
    * 输入参数:  getVibrationData -- 振动数据
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  振动数据
    ************************************************/
    void getVibrationFrequencyDomainData(const VibrationRecord &stVibrationRecord, int iDataType);

    /************************************************
    * 函数名:  getVibrationData
    * 输入参数:  getVibrationData -- 振动数据
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  振动数据
    ************************************************/
    void getVibrationParamData(const VibrationRecord &stVibrationRecord);

    /************************************************
    * 函数名:  packageVibrationData
    * 输入参数:  stArresterRecord -- 避雷器数据
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  振动数据
    ************************************************/
    void packageVibrationData(const QString &strPointId);

    /************************************************
    * 函数名:  getArresterData
    * 输入参数:  stArresterRecord -- 避雷器数据
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  避雷器数据
    ************************************************/
    void packageArresterData(const QString &strPointId, const QDateTime &startDate, const QDateTime &endDate);


    /************************************************
    * 函数名:  getVideoData
    * 输入参数:  hkRecord -- 视频数据
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  视频数据
    ************************************************/
    void videoData(const HkVideoRecord& hkRecord);

    /************************************************
    * 函数名:  getTrendData
    * 输入参数:  strPointId -- 测点类型
    *           strState 数据状态
    *           startDate 开始时间
    *           endDate 结束时间
    *           page  页面
    *           size  每页个数
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取指定趋势图谱数据
    ************************************************/
    void getTrendData(const QString& strPointId, ADUChannelType eChannelType, const QString& strState,
                      const QDateTime& startDate, const QDateTime& endDate,
                      const int page, const int size);
};


#endif // CHARTDATA_H

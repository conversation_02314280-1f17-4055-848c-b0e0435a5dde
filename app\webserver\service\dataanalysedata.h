/*
* Copyright (c) 2017.1，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：dataanalysedata.h
*
* 初始版本：1.0
* 作者：贺小强
* 创建日期：2017年1月6日
* 摘要：数据分析页面接口，提供数据分析数据
*
*/

#ifndef DATAANALYSEDATA_H
#define DATAANALYSEDATA_H

#include <QJsonObject>
#include "devicetree.h"

class DataAnalyseData : public QJsonObject
{
public:
    /************************************************
    * 函数名:  AlarmData
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  构造函数
    ************************************************/
    DataAnalyseData();

    /************************************************
    * 函数名:  reFreshStationData
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  刷新站点报警信息
    ************************************************/
    void reFreshStationData(void);

    /************************************************
    * 函数名:  reFreshDeviceData
    * 输入参数:  ucDeviceID -- 设备ID
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  根据条件刷新设备报警信息
    ************************************************/
    void reFreshDeviceData(int ucDeviceID);

    /************************************************
    * 函数名:  reFreshChannelData
    * 输入参数:  strChannelName -- 通道名
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  根据条件刷新通道报警信息
    ************************************************/
    void reFreshChannelData(const QString &strChannelName);

};

#endif // DATAANALYSEDATA_H

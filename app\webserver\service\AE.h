/*
* Copyright (c) 2016.1，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：AE.h
*
* 初始版本：1.0
* 作者：邵震宇
* 修改日期：2016年11月18日
* 摘要：AE模块相关定义
* 当前版本：1.0
*/
#ifndef AE_H
#define AE_H
#include "DataDefine.h"
#include "log/logger.h"
#include "log/LogInstance.h"

namespace AE
{
/*******************************************
 *          工作参数
*******************************************/
    typedef enum _WorkSet
    {
        SAMPLING_RATE = 250*50/2,
        AD_RANGE = 4096,
        AD_BITS = 12,
    }WorkSet;
/*******************************************
 *          AE幅值
*******************************************/
    //工作模式
    typedef enum _WorkMode
    {
        MODE_AMPLITUDE = 0,//幅值图谱模式
        MODE_PHASE,//相位图谱模式
        MODE_FLY,//飞行图谱模式
        MODE_WAVE//波形图谱
    }WorkMode;

    //采集模式
    typedef enum _SampleMode
    {
        SAMPLEMODE_SINGLE = 0,//单次模式
        SAMPLEMODE_CONTINUOUS, //连续模式

        SAMPLEMODE_MIN = SAMPLEMODE_SINGLE,
        SAMPLEMODE_MAX = SAMPLEMODE_CONTINUOUS,
        SAMPLEMODE_DEFAULT = SAMPLEMODE_CONTINUOUS
    }SampleMode;

    //幅值类型
    typedef enum _Amplitude
    {
        EFFECTIVE = 0,//有效值
        MAX,          //最大值
        FRE_ONE,      //频率成分一
        FRE_TWO,      //频率成分二
        AMPLITUDE_VALUETYPE_COUNT,
    }Amplitude;

    //增益
    typedef enum _GainType
    {
        GAIN_X1 = 0,//增益X1
        GAIN_X10,   //增益X10
        GAIN_X100,  //增益X100
        GAIN_COUNT,

        GAIN_MIN = GAIN_X1,
        GAIN_MAX = GAIN_X100,
        GAIN_DEFAULT = GAIN_X100
    }GainType;
    const UINT16 g_ausGainValues[GAIN_COUNT] =
    {
        1, 10, 100
    };

    inline GainType gainVal2Type(int iGainVal)
    {
        if( iGainVal == 60 )
        {
            return AE::GAIN_X1;
        }
        else if( iGainVal == 80 )
        {
            return AE::GAIN_X10;
        }
        else if( iGainVal ==  100)
        {
            return AE::GAIN_X100;
        }
        else
        {
            return AE::GAIN_DEFAULT;
        }
    }

    //触发值
    typedef enum _TriggerValue
    {
        TRIGGER_LEVEL_0 = 0,
        TRIGGER_LEVEL_1,
        TRIGGER_LEVEL_2,
        TRIGGER_LEVEL_3,
        TRIGGER_LEVEL_4,
        TRIGGER_LEVEL_5,
        TRIGGER_LEVEL_COUNT,

        TRIGGER_LEVEL_MIN = TRIGGER_LEVEL_0,
        TRIGGER_LEVEL_MAX = TRIGGER_LEVEL_5,
        TRIGGER_LEVEL_DEFAULT = TRIGGER_LEVEL_0
    }TriggerValue;

    //幅值范围
    typedef enum _AmpRange
    {
        AMP_RANGE_0 = 0,
        AMP_RANGE_1,
        AMP_RANGE_2,
        AMP_RANGE_3,
        AMP_RANGE_4,
        AMP_RANGE_COUNT,

        AMP_RANGE_MIN = AMP_RANGE_0,
        AMP_RANGE_MAX = AMP_RANGE_4,
        AMP_RANGE_DEFAULT = AMP_RANGE_4
    }AmpRange;

    //触发值组
    typedef enum _TriggerGroup
    {
        MV_GAIN_X100 = 0,
        MV_GAIN_X10,
        MV_GAIN_X1,
        DB_GAIN_X100,
        DB_GAIN_X10,
        DB_GAIN_X1,
        TRIGGER_GROUP_COUNT
    }TriggerGroup;

    const UINT16 g_ausTriggerValues[TRIGGER_GROUP_COUNT][TRIGGER_LEVEL_COUNT] = {

        {1, 2, 5, 10, 15, 18}, //x100 mv
        {10, 20, 50, 100, 150, 180}, //x10 mv
        {100, 200, 500, 600, 800, 900}, //x1 mv
        {0, 6, 14, 20, 24, 25}, //x100 db
        {20, 26, 34, 40, 44, 56}, //x10 db
        {40, 46, 54, 56, 58, 59} //x1 db
    };

    //单位
    typedef enum _UnitOption
    {
        UNIT_MV = 0, //单位 mV
        UNIT_DB,     //单位dB
        UNIT_COUNT,

        UNIT_MIN = UNIT_MV,
        UNIT_MAX = UNIT_DB,
        UNIT_DEFAULT = UNIT_MV
    }UnitOption;

    //音量
    typedef enum _Volume
    {
        VOLUME_MIN = 0,
        VOLUME_MAX = 9,
        VOLUME_STEP = 1,
        VOLUME_DEFAULT = 9
    }Volume;

    //频率成分
    typedef enum _Spectrum
    {
        SPECTRUM_MIN = 10,
        SPECTRUM_MAX = 500,
        SPECTRUM_STEP = 10,
        SPECTRUM_DEFAULT = 50
    }Spectrum;

    //AE通道
    typedef enum _ChannelType
    {
        AIR_SOUND = 0, //空声
        SURFACE_MOUNT, //表贴
        WIRELESS, //无线通道  保留接口，暂不用
        CHANNEL_TYPE_COUNT,

        CHANNEL_MIN = AIR_SOUND,
        CHANNEL_MAX = WIRELESS,
        CHANNEL_DEFAULT = AIR_SOUND
    }ChannelType;

    //采样时间
    typedef enum _SampleTime
    {
        SAMPLE_TIME_1 = 0,  //采样时间 1个周期
        SAMPLE_TIME_2,  //采样时间 2个周期
        SAMPLE_TIME_5,  //采样时间 5个周期
        SAMPLE_TIME_10, //采样时间 10个周期
        SAMPLE_TIME_COUNT,

        SAMPLE_TIME_MIN = SAMPLE_TIME_1,
        SAMPLE_TIME_MAX = SAMPLE_TIME_10,
        SAMPLE_TIME_DEFAULT = SAMPLE_TIME_2

    }SampleTime;
    const UINT16 SAMPLE_TIME[SAMPLE_TIME_COUNT] =
    {
        1,2,5,10
    };

    //幅值图谱数据
    typedef struct _AmplitudeData
    {
        float fPeakValue; //峰值
        float fRMS; //有效值
        float fFirstFreqComValue; //第一频率分量值
        float fSecondFreqComValue; //第二频率分量值
        _AmplitudeData()
        {
            fPeakValue = 0;
            fRMS = 0;
            fFirstFreqComValue = 0;
            fSecondFreqComValue = 0;
        }
    }AmplitudeData;

    typedef struct _PhaseData  // AE 相位图谱保存的数据结构
    {
        float fPhaseValue;  // 相对相位值(0~360)
        float fPeakValue;  // 峰值
        _PhaseData()
        {
            fPhaseValue = 0;
            fPeakValue = 0;
        }
    }PhaseData;

    typedef struct _FlyData  // AE 飞行图谱保存的数据结构
    {
        UINT32 uiPulseInterval;  // 脉冲间隔时间 us
        float fPeakValue;  // 峰值
        _FlyData()
        {
            uiPulseInterval = 0;
            fPeakValue = 0;
        }
    }FlyData;

    typedef struct _WaveData  // AE 波形图谱保存的数据结构
    {
        float fWaveValue;
        _WaveData()
        {
            fWaveValue = 0;
        }
    }WaveData;

    //增益状态
    typedef enum _GainStatus
    {
        GAIN_STATUS_NONE = 0,//增益正常
        GAIN_STATUS_HIGH,//增益偏高
        GAIN_STATUS_LOW,//增益偏低
        GAIN_STATUS_COUNT
    }GainStatus;
    //AE增益偏低临界值
    const float AE_LOWGAIN_THRESHOLDS[AE::UNIT_COUNT][AE::GAIN_COUNT] =
    {
        { 27.73, 2.77, 0.27 },//mv
        { 29, 9, -11 }//db
    };
    //AE增益偏高临界值
    const float AE_HIGHGAIN_THRESHOLDS[AE::UNIT_COUNT][AE::GAIN_COUNT] =
    {
        { 832.03, 149.07, 14.04 },//mv
        { 58, 43, 23 }//db
    };

    //同步状态
    typedef enum _SyncState
    {
        Not_Sync = 0, //未同步
        Synced, //已同步
    } SyncState;

    //同步源
    typedef enum _SyncSource
    {
        INTER_SYNC = 0, //内同步
        WIRELESS_SYNC, //电源同步
        LIGHT_SYNC, //光同步
        SYNC_SOURCE_MIN = INTER_SYNC,
        SYNC_SOURCE_MAX = LIGHT_SYNC,
        SYNC_SOURCE_COUNT = SYNC_SOURCE_MAX - SYNC_SOURCE_MIN + 1,
        SYNC_SOURCE_DEFAULT = WIRELESS_SYNC,
    } SyncSource;

    /*************************************************
    功能： 获取触发值组
    输入参数：
            eUnit -- 量纲
            eGain -- 增益
    返回：
            触发值组
    *************************************************/
    inline TriggerGroup getTriggerGroup( UnitOption eUnit, GainType eGain )
    {
        TriggerGroup eTriggerGroup = TRIGGER_GROUP_COUNT;

        if( eUnit == UNIT_MV && eGain == GAIN_X100)
        {
            eTriggerGroup = AE::MV_GAIN_X100;
        }
        else if( eUnit == UNIT_MV && eGain == GAIN_X10)
        {
            eTriggerGroup = AE::MV_GAIN_X10;
        }
        else if( eUnit == UNIT_MV && eGain == GAIN_X1)
        {
            eTriggerGroup = AE::MV_GAIN_X1;
        }
        else if( eUnit == UNIT_DB && eGain == GAIN_X100)
        {
            eTriggerGroup = AE::DB_GAIN_X100;
        }
        else if( eUnit == UNIT_DB && eGain == GAIN_X10)
        {
            eTriggerGroup = AE::DB_GAIN_X10;
        }
        else if( eUnit == UNIT_DB && eGain == GAIN_X1)
        {
            eTriggerGroup = AE::DB_GAIN_X1;
        }
        else
        {
            eTriggerGroup = AE::MV_GAIN_X100;
        }
        return eTriggerGroup;
    }


    /*************************************************
    功能： 获取触发门槛
    输入参数：
            eUnit -- 量纲
            eGain -- 增益
            eTrigger -- 触发等级
    *************************************************/
    inline float getThreshold( UnitOption eUnit, GainType eGain, TriggerValue eTrigger )
    {
        TriggerGroup eTriggerGroup = getTriggerGroup( eUnit, eGain );
        float fTrigger = g_ausTriggerValues[ eTriggerGroup][eTrigger];

        return fTrigger;
    }


    //幅值图谱的范围（单位和增益决定）
    typedef enum _AmplitudeValue
    {
        COMPONENT_EFFECTIVE = 0,   // 有效值
        COMPONENT_MAX,             // 最大值
        COMPONENT_ONE,             // 频率成分一
        COMPONENT_TWO,             // 频率成分二
        COMPONENT_COUNT            // 数值成分的总数
    }AmplitudeValue;
    //最大值
    const UINT16 AMPLITUDE_MAX_VALUES[UNIT_COUNT][GAIN_COUNT][COMPONENT_COUNT] =
    {
        //mv
        {
            //1
            { 500, 1000, 100, 100 },
            //10
            { 100, 200, 20, 20 },
            //100
            { 10, 20, 2, 2 },
        },
        //db
        {
            //1
            { 70, 70, 70, 70 },
            //10
            { 50, 50, 50, 50 },
            //100
            { 30, 30, 30, 30 },
        }
    };
    //最小值
    const INT16 AMPLITUDE_MIN_VALUES[UNIT_COUNT][GAIN_COUNT][COMPONENT_COUNT] =
    {
        //mv
        {
            //1
            { 0, 0, 0, 0 },
            //10
            { 0, 0, 0, 0 },
            //100
            { 0, 0, 0, 0 },
        },
        //db
        {
            //1
            { 10, 10, 10, 10 },
            //10
            { 0, 0, 0, 0 },
            //100
            { -15, -15, -15, -15 },
        }
    };

/*******************************************
 *          AE相位
*******************************************/
    typedef enum _CloseDoorTime
    {
        CLOSE_DOOR_TIME_NONE = -1,
        CLOSE_DOOR_TIME_2 = 0, //关门时间 2ms
        CLOSE_DOOR_TIME_3, //关门时间 3ms
        CLOSE_DOOR_TIME_5,  //关门时间 5ms
        CLOSE_DOOR_TIME_7,  //关门时间 7ms
        CLOSE_DOOR_TIME_10, //关门时间 10ms
        CLOSE_DOOR_TIME_14, //关门时间 14ms
        CLOSE_DOOR_TIME_18, //关门时间 18ms
        CLOSE_DOOR_TIME_25, //关门时间 25ms
        CLOSE_DOOR_TIME_COUNT,
        CLOSE_DOOR_TIME_MIN = 2,
        CLOSE_DOOR_TIME_MAX = 25,
        CLOSE_DOOR_TIME_DEFAULT = CLOSE_DOOR_TIME_2
    }CloseDoorTime;
    const int CLOSE_DOOR_TIME[CLOSE_DOOR_TIME_COUNT] =
    {
        2,3,5,7,10,14,18,25
    };

    //相位偏移
    typedef enum _PhaseShift
    {
        PHASE_SHIFT_MIN = 0,
        PHASE_SHIFT_MAX = 360,
        PHASE_SHIFT_STEP = 5,
        PHASE_SHIFT_DEFAULT = 0,
    }PhaseShift;

    //最小值
    const INT16 Y_RANGE_VALUES[GAIN_COUNT] =
    {
            //1
            1000,
            //10
            200,
            //100
            20,
    };

/*******************************************
 *          AE飞行
*******************************************/
    //开门时间
    typedef enum _OpenDoorTime
    {
        OPEN_DOOR_TIME_NONE = -1,
        OPEN_DOOR_TIME_100 = 0, //开门时间 2us
        OPEN_DOOR_TIME_150, //开门时间 150us
        OPEN_DOOR_TIME_200,  //开门时间 200us
        OPEN_DOOR_TIME_250,  //开门时间 250us
        OPEN_DOOR_TIME_300, //开门时间 300us
        OPEN_DOOR_TIME_350, //开门时间 350us
        OPEN_DOOR_TIME_400, //开门时间 400us
        OPEN_DOOR_TIME_450, //开门时间 450us
        OPEN_DOOR_TIME_500, //开门时间 500us
        OPEN_DOOR_TIME_600, //开门时间 600us
        OPEN_DOOR_TIME_700, //开门时间 700us
        OPEN_DOOR_TIME_800, //开门时间 800us
        OPEN_DOOR_TIME_900, //开门时间 900us
        OPEN_DOOR_TIME_COUNT,
        OPEN_DOOR_TIME_MIN = 100,
        OPEN_DOOR_TIME_MAX = 900,
        OPEN_DOOR_TIME_DEFAULT = OPEN_DOOR_TIME_100
    }OpenDoorTime;

    const int OPEN_DOOR_TIME[OPEN_DOOR_TIME_COUNT] = {
        100,150,200,250,300,350,400,450,500,600,700,800,900
    };

    typedef enum _TimeInterval
    {
        TIME_INTERVAL_NONE = -1,
        TIME_INTERVAL_20 = 0, //时间间隔 20us
        TIME_INTERVAL_50, //时间间隔 50us
        TIME_INTERVAL_100,  //时间间隔 100us
        TIME_INTERVAL_200,  //时间间隔 200us
        TIME_INTERVAL_500, //时间间隔 500us
        TIME_INTERVAL_1000, //时间间隔 1000us
        TIME_INTERVAL_2000, //时间间隔 2000us
        TIME_INTERVAL_5000, //时间间隔 5000us
        TIME_INTERVAL_10000, //时间间隔 10000us
        TIME_INTERVAL_30000, //时间间隔 30000us
        TIME_INTERVAL_60000, //时间间隔 6000us
        TIME_INTERVAL_COUNT,
        TIME_INTERVAL_MIN = 20,
        TIME_INTERVAL_MAX = 60000,
        TIME_INTERVAL_DEFAULT = TIME_INTERVAL_20
    }TimeInterval;

    const int TIME_INTERVAL_VALUE[TIME_INTERVAL_COUNT] =
    {
        20,50,100,200,500,1000,2000,5000,10000,30000,60000
    };

/*******************************************
 *          AE波形
*******************************************/
    const UINT16 AE_AMPLITUDE_SCOPE[GAIN_COUNT][AMP_RANGE_COUNT] =
    {
        {50,100,200,500,1000}, // X1
        {10,20,50,100,200}, // X10
        {2,5,10,15,20} // X100
    };

    const UINT16 AE_SAMPLE_TIME[SAMPLE_TIME_COUNT] =
    {
        1,2,5,10
    };

}

#endif

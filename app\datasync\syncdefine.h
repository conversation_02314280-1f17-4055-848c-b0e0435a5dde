/*
* Copyright (c) 2017.7，南京华乘电气科技有限公司
* All rights reserved.
*
* syncdefine.h
*
* 初始版本：1.0
* 作者：wujun
* 创建日期：2017年07月17日
* 摘要：数据同步的相关定义

* 当前版本：1.0
*/
#ifndef __SYNCDEFINE_H_
#define __SYNCDEFINE_H_

#include <QString>
#include <QDateTime>
#include "devicetree.h"


enum SyncException
{
    Conn_Cipher_Error = 1,      // 连接密码错误
    Link_OverTime,              // 连接超时
    Not_Connected,              // 未连接
    Send_Error,                 // 发送失败
    GetConfig_OverTime,         // 下载配置文件超时
    GetDbInfo_OverTime,         // 获取数据信息超时
    GetDbData_OverTime,         // 取数据超时
    Data_Error,                 // 数据错误
    Write_Error,                // 写文件错误
    Config_Empty,               // 配置文件为空
    Stop_Sync,                  // 停止同步
    CONFIG_NOT_SYNC             // 配置未同步
};

// 站点概要信息 只包括站点自身信息
struct StationOutlineInfo
{
    QString stationName;      //站点名称
    QString stationCode;      //站点编号
};


// 配置内容传输状态
enum ConfigTransState
{
    StateOk = 0,            //正常
    WrongStationCode        //错误的站点识别码
    //GetConfigError          //获取配置文件失败
};

// 配置文件结构体
typedef struct _ConfigNode
{  
    ConfigTransState state;     // 状态信息
    QString strCode;            // 主机编号
    quint8 nGroup;              // 主机组号
    StationNode stationNode;    // 站点信息
    QList<ADUUnitInfo> adus;    // 多个前端
} ConfigNode;

// 进度类型
enum ProgressTrigger
{
    TRIGGER_DOWNLOAD_CONFIG,          // 下载配置
    TRIGGER_SYNC_DATA,                // 同步数据
};

namespace DataSync {

enum CommandType                     	// 命令字
{
    CMD_INVALID=0,                      // 无效命令字
    CMD_CONN_REQ,                       // 连接请求
    CMD_CONN_REP,                  		// 连接应答
    CMD_CONFIG_REQ,                     // 配置内容请求
    CMD_CONFIG_REP,                     // 配置内容应答
    CMD_SYNC_INFO_REQ,                  // 同步数据信息请求
    CMD_SYNC_INFO_REP,                  // 同步数据信息应答
    CMD_SYNC_DATA_REQ,                  // 同步取数据请求
    CMD_SYNC_DATA_REP,                  // 同步取数据应答
    CMD_CUSTSYNC_INFO_REP,              // 自定义同步数据信息应答
    CMD_CUSTSYNC_DATA_REQ,              // 自定义同步取数据请求
    CMD_CUSTSYNC_DATA_REP,              // 自定义同步取数据息应答
    CMD_DEFSYNC_INFO_REP,               // 默认同步数据信息应答
    CMD_DEFSYNC_DATA_REQ,               // 默认同步取数据请求
    CMD_DEFSYNC_DATA_REP,               // 默认同步取数据息应答
    CMD_STOP_SYNC,                      // 停止采集
    CMD_SYNC_DBFILE_HEAD_REQ,           // 数据文件头请求
    CMD_SYNC_DBFILE_HEAD_REP,           // 数据文件头应答
    CMD_SYNC_DBFILE_CONTENT_REQ,        // 数据文件内容请求
    CMD_SYNC_DBFILE_CONTENT_REP,        // 数据文件内容应答
    CMD_NUM_TOTAL
};

enum LinkType                       // 链路类型
{
    LINK_INVALID = -1,              // 无效链路类型
    LINK_BLUETOOTH,                 // 蓝牙
    LINK_GROUP_WIFI                // wifi
};

}

#endif


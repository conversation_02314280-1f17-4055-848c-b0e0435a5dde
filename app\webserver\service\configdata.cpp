#include "configdata.h"
#include "dbserver.h"
#include "monitorservice.h"
#include "statemonitor.h"
#include "commandfuns.h"
#include "log.h"
#include "networkdevicemanager.h"
ConfigData::ConfigData()
{
}

/************************************************
* 函数名:  SystemSettingData
* 输入参数:  NULL
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取系统设置数据
************************************************/
void ConfigData::SystemSettingData(void)
{
    ConfigService &configService = ConfigService::instance();
    //辅控RtuID、汇集RtuID配置
    insert("auxRtuID", configService.auxCtrlTerminalInfo().strID);
    insert("iedRtuID", configService.getIedRtuID());

    SystemSetting systemSetting = configService.systemSettings();
    insert("date",QDateTime::currentDateTime().toString("MM/dd/yyyy"));
    insert("time",QDateTime::currentDateTime().toString("hh:mm:ss"));
    //insert(STR_LANGUAGE,systemSetting.strLanguage);
    QString strSystemLanguage = "zh-CN";
    if("PMDT" == configService.getSystemReleaseInfo().strReleaseEdition)
    {
        strSystemLanguage = "en-US"; //PMDT默认语言必须英文
    }
    else
    {
        strSystemLanguage = configService.getSystemReleaseInfo().strlanguage;
    }
    insert(STR_LANGUAGE, strSystemLanguage);

    insert(STR_MONITOR_LORA_FREQUENCEY,systemSetting.loraFreConf);
    insert(STR_FREQUNCY,QString::number(systemSetting.iFreq)+"Hz");
    //insert("MonitorType", ConfigService::instance().listMonitorType().at(systemSetting.eMonitorHostType));
    MonitorHostType hardType = ConfigService::instance().getHardType();
    if(MONITOR_TYPE_HANGING == hardType)
    {
        insert("MonitorType", "MonitorTypeHanging");
    }
    else if(MONITOR_TYPE_COLLOCTION_NODE == hardType)
    {
        insert("MonitorType", "MonitorTypeCollectionNode");
    }
    else if(MONITOR_TYPE_LOW_POWER == hardType)
    {
        insert("MonitorType", "MonitorTypeLowPower");
    }
    else if(MONITOR_TYPE_AUX == hardType)
    {
        insert("MonitorType", "MonitorTypeAux");
    }
    insert("MonitorName",systemSetting.strMonitorName);
    //#ifndef VERSION_SICHUAN_DEYANG
    // 和云通信的间隔
    insert(STR_SYS_UPLOAD_INTERVAL,QString::number(systemSetting.iUploadInterval));
    //#endif

    if(MONITOR_TYPE_LOW_POWER == ConfigService::instance().getHardType())
    {
        insert(STR_MONITOR_SLEEP_SPACE,QString::number(systemSetting.uiMonitorSleepSpace));
    }
#ifdef SERVER_IS_OLD_PROTOCOL

    insert(STR_MONITOR_SAMPLE_TIME,systemSetting.iMonitorSampleStartTime);
    insert(STR_MONITOR_SAMPLE_INTERVAL,systemSetting.iMonitorSampleSpace);
    if(MONITOR_TYPE_LOW_POWER == configs.getHardType())
    {
       insert(STR_SAMPLE_INTERVAL,QString::number(systemSetting.uiSampleinterval));
    }
#endif
#ifdef SERVER_PD_THREE
    insert(STR_MONITOR_PD_SAMPLE_TIME,(int)systemSetting.uiPdSampleInterval);
#endif

#ifdef SERVER_PD_FIVE
    insert(STR_MONITOR_PD_SAMPLE_TIME,(int)systemSetting.uiPdSampleInterval);
#endif

#ifdef SERVER_MECH
    insert(STR_MONITOR_MECH_SAMPLE_TIME,(int)systemSetting.uiMechSyncDataInterval);
#endif
#ifdef SERVER_LORA_BEAN
    insert(STR_MONITOR_WORK_GROUP, systemSetting.ucMonitorConnectionGroup);
#endif

    //传感器连接断开状态判断阈值
    insert("aduOfflineThreshold", systemSetting.uiAduOfflineThreshold);
}

/*************************************************
函数名： checkPassword
输入参数： strPassword -- 用户输入的密码
        eUserName -- 用户名
输出参数： NULL
返回值： 密码正确与否
功能： 验证密码
*************************************************************/
bool ConfigData::checkPassword(const QString &strPassword, const UserName &eUserName)
{
    Q_UNUSED( strPassword );
    Q_UNUSED( eUserName );

    return true;
}

/************************************************
 * 输入参数：listCkData 审核数据列表
 * 功能：获取审核数据
 ************************************************/
void ConfigData::getCheckList(const QList<safe::CheckData> &listCkData)
{
    QJsonArray arrayData;
    for(auto i = 0; i < listCkData.size(); ++i)
    {
        QJsonObject ckObj;
        ckObj.insert(STR_SPNOSOR, listCkData[i].sponsor);
        ckObj.insert(STR_CHECKTYPE, QString::number(listCkData[i].checkType));
        ckObj.insert(STR_DATE_TIME, listCkData[i].dateTime.toString(STR_DATE_TIME_QSTRING_CNA));
        ckObj.insert(STR_TARGET, listCkData[i].target);
        ckObj.insert(STR_ADU_EXTEND, listCkData[i].extraInfo);
        arrayData.append(ckObj);
    }
    insert(STR_TOTAL, listCkData.size());
    insert(STR_ROWS, arrayData);
}

/************************************************
 * 输入参数:  userInfo 普通用户列表信息
 * 功能:  获取普通用户列表信息
 ************************************************/
void ConfigData::getNormalUser(const QList<safe::UserInfo> &listUserInfo)
{

    QJsonArray arrayData;
    for(auto i = 0; i < listUserInfo.size(); ++i)
    {
        safe::UserLoginData loginInfo;
        safe::UserManager::instance().getUserLoginInfo(listUserInfo[i].strAccount, safe::CTYPE_NORMAL, loginInfo);
        safe::UserPasData pasInfo;
        safe::UserManager::instance().getUserPasInfo(listUserInfo[i].strAccount, safe::CTYPE_NORMAL, pasInfo);

        QJsonObject userObj;
        userObj.insert(STR_INDEX, i);
        userObj.insert(STR_USER_NAME, listUserInfo[i].strAccount);
        userObj.insert(STR_USERSTATE, listUserInfo[i].stState);
        userObj.insert(STR_CHARATYPE, listUserInfo[i].eCharaType);
        userObj.insert(STR_LANGUAGE, listUserInfo[i].eLanguage);
        userObj.insert(STR_FIRSTLOGIN, listUserInfo[i].bFirstLogin);
        userObj.insert(STR_PASPAST, listUserInfo[i].bPasPast);

        userObj.insert(STR_BEGIN_TIME, loginInfo.iBeginLoginHoure);
        userObj.insert(STR_END_TIME, loginInfo.iEndLoginHoure);
        userObj.insert(STR_BEGINIP, loginInfo.strBeginIp);
        userObj.insert(STR_ENDIP, loginInfo.strEndIp);
        userObj.insert(STR_LOGINFAILCOUNT, loginInfo.iLoginFailCount);
        userObj.insert(STR_MAXLOGINFAILCOUNT, 5);

        userObj.insert(STR_PASSWORD, pasInfo.strPassWord);
        userObj.insert(STR_PASSWORDDAYS, pasInfo.iValidPasDay);
        userObj.insert(STR_PASSWORDACTIVEDATE, pasInfo.effectDate.toString(STR_DATE_TIME_QSTRING_CNA));
        arrayData.append(userObj);
    }
    insert(STR_TOTAL, listUserInfo.size());
    insert(STR_ROWS, arrayData);
}


/************************************************
 * 函数名:  syncData
 * 输入参数:  ADUList -- 前端列表
 *      eADUType -- 前端类型
 *      startTime -- 开始时间
 *      endTime -- 结束时间
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能: 数据同步前端
 ************************************************/
bool ConfigData::syncData(const QStringList &ADUList, ADUType eADUType)
{
    bool bRet = false;
    switch (eADUType)
    {
    case ADU_MECH:
    case ADU_IS:
    case ADU_MECH_IS:
    case ADU_UHF_IS:
    case ADU_HFCT_IS:
    case ADU_PD_IS:
    case ADU_GIS_AE_IS:
    case ADU_TRANSFORMER_AE_IS:
    case ADU_ARRESTER_I_IS:
    case ADU_ARRESTER_U_IS:
    case ADU_GROUNDDINGCURRENT_IS:
    case ADU_LEAKAGECURRENT_IS:
    case ADU_VIBRATION_IS:
    case ADU_TEMP_HUM_IS:
    case ADU_FLOOD_IS:
    case ADU_TEVPRPS_IS:
    {
        bRet = true;
    }
        break;
    default:
        break;
    }
    if (bRet)
    {
        bRet = ConfigService::instance().isADUIDMatchType(ADUList, eADUType);
    }
    return bRet;
}

/************************************************
 * 函数名:  getADUConfig
 * 输入参数:  eADUType -- 前端类型
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取前端参数
 ************************************************/
void ConfigData::getADUConfig(ADUType eADUType)
{
    ADUUnitInfo adu;
    if (ConfigService::instance().getADUConfig(eADUType, adu))
    {
        QString strName = ConfigService::instance().getADUTypName(adu.eType);
        insert(STR_ADU_TYPE, strName);
        insert(STR_ADU_COMM_TYPE, ConfigService::instance().mapLinkGrp().key(adu.eLinkGroup));
        insert(STR_FREQUNCY, adu.stADUParam.ucFrequency);
        insert(STR_ADU_WORK_GROUP, adu.stADUParam.ucWorkGroup);
        insert(STR_ADU_NUM_IN_GROUP, adu.stADUParam.usNumInGroup);
        insert(STR_ADU_COMM_LOAD, adu.stADUParam.ucConnectionLoad);
        insert(STR_ADU_COMM_SPEED, adu.stADUParam.ucConnectionSpeed);
        insert(STR_ADU_SLEEP_TIME, (int)adu.stADUParam.uiSleepTime);
        insert(STR_ADU_SAMPLE_SPACE, (int)adu.stADUParam.uiSampleSpace);
        insert(STR_ADU_SAMPEL_START_TIMR, adu.stADUParam.usStartSampleTime);
        insert(STR_ADU_B_AUTO_GIVING, adu.stADUParam.ucAutoUpdate);
        insert(STR_SF6_ALARM_TASK_GROUP, (int)adu.iTaskGroup);
        insert(STR_ADU_ARTIFICIAL_START_TIME,8);//adu.stADUParam.ucStartArtificialTime);
        insert(STR_ADU_ARTIFICIAL_END_TIME,20);//adu.stADUParam.ucEndArtificialTime);
        insert(STR_ADU_ARTIFICIAL_STATE_WAKE_UP_SPACE,3);//adu.stADUParam.usArtificialWakeUpInterval);
        insert(STR_ADU_UNARTIFICIAL_STATE_WAKE_UP_SPACE,30);//adu.stADUParam.usNotArtificialWalkUpInterval);
        insert(STR_ADU_IS_AUTO_CHANGE_MODE, 1);
        insert(STR_ADU_EXTEND, adu.stADUParam.extendInfo);
        insert(STR_ADU_MONITOR_MODE_SAMPLE_SPACE,adu.stADUParam.usTutelageSampleSpace);
        insert(STR_ADU_AUTO_GIVISPACE, static_cast<qint32>(adu.stADUParam.uiAutoGivingSpace));
        insert("thresholdMode", (int)adu.stADUParam.eAduFilterNoiseMode);
        insert("threshold", static_cast<int>(adu.stADUParam.fUesdThreshold*100));
    }

    bool bLowPowerADU = false;
    QList<ADUUnitInfo> listADUs = ConfigService::instance().ADUList(eADUType);
    for (int i = 0; i < listADUs.size(); i++)
    {
        const ADUUnitInfo &stADU = listADUs.at(i);
        if ( Monitor::WORKER_MODEL_NORMAL != stADU.stADUParam.eADUWorkModel )
        {
            bLowPowerADU = true;
        }
    }
    if ( bLowPowerADU )
    {
        insert(STR_ADU_MODE,  ConfigService::instance().getADUWorkMode(adu.stADUParam.eADUWorkModel));
    }
}

void ConfigData::getGroupNoRange(int iLoraFrequency)
{
    Q_UNUSED(iLoraFrequency)

    const int START_GROUP_NO = 0;
    insert("startGroupNo", QJsonValue(START_GROUP_NO)); //起始组号
    insert("endGroupNo", QJsonValue((int)LoraInfoKeys[iLoraFrequency].uiGroupIdLimit));
}

/************************************************
 * 函数名:  SystemSettingConfig
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取系统设置选项
 ************************************************/
void ConfigData::SystemSettingConfig(void)
{
    QJsonArray jsonLanguage;
    jsonLanguage.append("en_US");
    jsonLanguage.append("zh_CN");
    insert(STR_LANGUAGE,jsonLanguage);
    QJsonArray jsonFrequncy;
    jsonFrequncy.append("50Hz");
    jsonFrequncy.append("60Hz");
    insert(STR_FREQUNCY,jsonFrequncy);
}

/************************************************
 * 函数名:  ModbusSettingConfig
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取modbus信息参数
 ************************************************/
void ConfigData::ModbusSettingConfig(void)
{
    QJsonArray jsonSerialPort;
    jsonSerialPort.append("/dev/ttyO2");
    jsonSerialPort.append("/dev/ttyO4");
    insert("serialPort", jsonSerialPort);

    QJsonArray jsonBaudRate;
    jsonBaudRate.append("110");
    jsonBaudRate.append("300");
    jsonBaudRate.append("600");
    jsonBaudRate.append("1200");
    jsonBaudRate.append("2400");
    jsonBaudRate.append("4800");
    jsonBaudRate.append("9600");
    jsonBaudRate.append("19200");
    jsonBaudRate.append("38400");
    jsonBaudRate.append("57600");
    jsonBaudRate.append("115200");
    insert("baudRate", jsonBaudRate);

    QJsonArray jsonDataBit;
    jsonDataBit.append("7");
    jsonDataBit.append("8");
    insert("dataBit", jsonDataBit);

    QJsonArray jsonStopBit;
    jsonStopBit.append("1");
    jsonStopBit.append("2");
    insert("stopBit", jsonStopBit);

    QJsonArray jsonCheckBit;
    jsonCheckBit.append("None");
    jsonCheckBit.append("Odd");
    jsonCheckBit.append("Even");
    insert("checkBit", jsonCheckBit);

    QJsonArray jsonRTS;
    jsonRTS.append("None");
    jsonRTS.append("Up");
    jsonRTS.append("Down");
    insert("RTS", jsonRTS);
}

void ConfigData::SF6AlarmConfigInfoPara(void)
{
    QJsonArray jsonAlarmTaskGroup;
    ConfigService &pConfig = ConfigService::instance();
    QList<ADUUnitInfo> adus = pConfig.ADUList();
    QSet<int> iTaskGruopNum;
    for( int i = 0; i < adus.size(); i++ )
    {
        if(ADU_VAISALADTP145 == adus[i].eType || ADU_WIKA_GDT20 == adus[i].eType
                || ADU_WIKA_GDHT20 == adus[i].eType || ADU_SHQIUQI_SC75D_SF6 == adus[i].eType)
        {
            iTaskGruopNum.insert(adus[i].iTaskGroup);
        }
    }

    for(QSet<int>::iterator iter = iTaskGruopNum.begin(); iter != iTaskGruopNum.end(); ++iter)
    {
        jsonAlarmTaskGroup.append(*iter);
    }
    insert(STR_SF6_ALARM_TASK_GROUP, jsonAlarmTaskGroup);

    QJsonArray jsonAlarmChannelType;
    jsonAlarmChannelType.append(STR_FROST_RAW);
    jsonAlarmChannelType.append(STR_FROST_ATM);
    jsonAlarmChannelType.append(STR_DEW_RAW);
    jsonAlarmChannelType.append(STR_DEW_ATM);
    jsonAlarmChannelType.append(STR_MOISTURE);
    jsonAlarmChannelType.append(STR_PRESS_ABSO);
    jsonAlarmChannelType.append(STR_PRESS_NORM);
    jsonAlarmChannelType.append(STR_DENSITY);
    jsonAlarmChannelType.append(STR_OXYGEN);
    jsonAlarmChannelType.append(STR_SF6);
    insert(STR_SF6_ALARM_CHANNEL_TYPE, jsonAlarmChannelType);

    QJsonArray jsonAlarmChannel;
    jsonAlarmChannel.append(ALARM_CHANNEL_BUILTIN);
    jsonAlarmChannel.append(ALARM_CHANNEL_EXTERNAL);
    jsonAlarmChannel.append(ALARM_CHNNEL_NONE);
    insert(STR_SF6_ALARM_CHANNEL, jsonAlarmChannel);

    QJsonArray jsonAlarmExternalIOChannel;
    jsonAlarmExternalIOChannel.append("1");
    jsonAlarmExternalIOChannel.append("2");
    jsonAlarmExternalIOChannel.append("3");
    jsonAlarmExternalIOChannel.append("4");
    jsonAlarmExternalIOChannel.append("5");
    jsonAlarmExternalIOChannel.append("6");
    jsonAlarmExternalIOChannel.append("7");
    jsonAlarmExternalIOChannel.append("8");
    insert(STR_SF6_ALARM_EXTERNAL_IO_CHANNEL, jsonAlarmExternalIOChannel);

    QJsonArray jsonAlarmWiringMode;
    jsonAlarmWiringMode.append(ALARM_WIRING_OPEN);
    jsonAlarmWiringMode.append(ALARM_WIRING_CLOSE);
    insert(STR_SF6_ALARM_WIRING_MODE, jsonAlarmWiringMode);

    QJsonArray jsonAlarmMode;
    jsonAlarmMode.append(ALARM_MODE_CONTINUOUS);
    jsonAlarmMode.append(ALARM_MODE_TIMING);
    jsonAlarmMode.append(ALARM_MODE_INTERVAL);
    insert(STR_SF6_ALARM_MODE, jsonAlarmMode);
}

/************************************************
* 函数名:  ModbusSettingData
* 输入参数:  NULL
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取modbus设置数据
************************************************/
void ConfigData::ModbusSettingData(void)
{
    ConfigService &configService = ConfigService::instance();
    ModbusSetting modbusSetting = configService.modbusSetting();
    insert("modbusAddress",modbusSetting.ucModbusAddress);
    insert(STR_SERIAL_PORT,modbusSetting.strDevice);
    insert(STR_BAURATE,QString::number(modbusSetting.iBaud));
    insert(STR_DATA_BIT,QString::number(modbusSetting.iDatabit));
    insert(STR_STOP_BIT,QString::number(modbusSetting.iStopbit));
    insert(STR_CHECK_BIT,modbusSetting.strParity);
    insert(STR_RTS,modbusSetting.strRts);
}

void ConfigData::StdModbusSettingData()
{
    ConfigService &configService = ConfigService::instance();
    ModbusSetting stdModbusSetting = configService.getStandModbus();

    insert("modbusAddress", stdModbusSetting.ucModbusAddress);
    insert(STR_SERIAL_PORT, stdModbusSetting.strDevice);
    insert(STR_BAURATE, stdModbusSetting.iBaud);
    insert(STR_DATA_BIT, stdModbusSetting.iDatabit);
    insert(STR_STOP_BIT, stdModbusSetting.iStopbit);
    insert(STR_CHECK_BIT, stdModbusSetting.strParity);
    insert(STR_RTS, stdModbusSetting.strRts);
    insert(STR_STAND_MODBUS, 1);
}

void ConfigData::SF6AlarmConfigData(const int &iTaskGroup, const ADUChannelType &eChannelType)
{
    QList<AlarmConfigInfo> listAlarmConfigInfoByGroup = ConfigService::instance().alarmConfigInfoByWorkGruop(iTaskGroup);
    for( int i = 0; i < listAlarmConfigInfoByGroup.size(); i++ )
    {
        if( eChannelType == listAlarmConfigInfoByGroup.at(i).eChannelType )
        {
            insert(STR_SF6_ALARM_TASK_GROUP, iTaskGroup);
            insert(STR_SF6_ALARM_CHANNEL_TYPE,   ConfigService::instance().getChanTypName(eChannelType));
            insert(STR_SF6_ALARM_THRESHOLD, listAlarmConfigInfoByGroup.at(i).fAlarmThreshold);
            insert(STR_SF6_ALARM_RECOVERY_THRESHOLD, listAlarmConfigInfoByGroup.at(i).fAlarmRecoveryThershold);
            insert(STR_SF6_ALARM_CHANNEL, ConfigService::instance().getAlarmChannelStr(listAlarmConfigInfoByGroup.at(i).eAlarmChannel));
            insert(STR_SF6_ALARM_EXTERNAL_IO_SN, listAlarmConfigInfoByGroup.at(i).strAlarmExternalIOSN);
            insert(STR_SF6_ALARM_EXTERNAL_IO_CHANNEL, listAlarmConfigInfoByGroup.at(i).iAlarmExternalIOChannel);
            insert(STR_SF6_ALARM_WIRING_MODE, ConfigService::instance().getAlarmWiringModeStr(listAlarmConfigInfoByGroup.at(i).eWiringMode));
            insert(STR_SF6_ALARM_MODE, ConfigService::instance().getAlarmModeStr(listAlarmConfigInfoByGroup.at(i).eAlarmMode));
            insert(STR_SF6_ALARM_TIME, listAlarmConfigInfoByGroup.at(i).iAlarmTime);
            insert(STR_SF6_ALARM_INTERVAL, listAlarmConfigInfoByGroup.at(i).iAlarmInterval);
            insert(STR_SF6_ALARM_DURATION, listAlarmConfigInfoByGroup.at(i).iAlarmDuration);
        }
        else
        {
            //非所需查阅项，不操作
        }
    }
}

/************************************************
* 函数名:  DeviceTreeData
* 输入参数:  ucDeviceID -- 设备id
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取设备树数据
************************************************/
void ConfigData::DeviceTreeData(const QString &strDeviceID)
{
    ConfigService &configService = ConfigService::instance();
    const StationNode &deviceTree =  configService.stationNode();
    if (deviceTree.strName != "")
    {
        insert(STR_STATION_NAME,deviceTree.strName);
        QJsonArray jsonDevices;
        for (int i = 0; i < deviceTree.devices.size(); i++)
        {
            const DeviceNode &device = deviceTree.devices.at(i);
            QJsonObject jsonDevice;
            jsonDevice.insert(STR_DEVICE_NAME, device.strName);
            jsonDevice.insert(STR_DEVICE_ID, device.strDeviceGUID);
            QJsonArray jsonChannels;
            if (device.strDeviceGUID == strDeviceID)
            {
                for (int j = 0; j < device.testPoints.size(); j++)
                {
                    const TestPointInfo &stTestPoint = device.testPoints.at(j);
                    QJsonObject jsonTestPoint;
                    jsonTestPoint.insert(STR_POINT_NAME,stTestPoint.strName);
                    jsonTestPoint.insert(STR_POINT_ID,stTestPoint.strPointGUID);
                    jsonChannels.append(jsonTestPoint);
                }
            }

            if (jsonChannels.size() > 0)
            {
                jsonDevice.insert(STR_POINT_ITEMS,jsonChannels);
            }
            jsonDevices.insert(i,jsonDevice);
        }
        if (jsonDevices.size() > 0)
        {
            insert(STR_DEVICE_ITEMS,jsonDevices);
        }
    }
}

/************************************************
* 函数名:  getStation
* 输入参数:  NULL
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取站点信息（站点名）
************************************************/
void ConfigData::getStation(void)
{
    ConfigService &configService = ConfigService::instance();
    StationNode stDeviceTree = configService.stationNode();
    if (!stDeviceTree.strSiteGUID.isEmpty())
    {
        insert(STR_STATION_NAME,stDeviceTree.strName);
        insert(STR_NAME,stDeviceTree.strName);
        insert(STR_ID,stDeviceTree.strSiteGUID);
        QString voltage = ConfigService::instance().listVoltageLevelName().at(stDeviceTree.eVoltage);
        insert(STR_STATION_VOLTAGE, voltage);
        insert(STR_STATION_COMPANY,stDeviceTree.strCompany);
        insert(STR_STATION_PMS,stDeviceTree.strPMS);
        insert(STR_IS_PARENT,!stDeviceTree.devices.isEmpty());
    }
}

/************************************************
* 函数名:  getDeviceList
* 输入参数:  NULL
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取设备信息列表
************************************************/
void ConfigData::getDeviceList(void)
{
    ConfigService &configService = ConfigService::instance();
    const StationNode &deviceTree =  configService.stationNode();
    int i = 0;
    QJsonArray jsonDeviceList;
    for (;i < deviceTree.devices.size(); i++)
    {
        QJsonObject jsonDevice;
        const DeviceNode &device = deviceTree.devices.at(i);
        jsonDevice.insert(STR_DEVICE_ID,device.strDeviceGUID);
        jsonDevice.insert(STR_DEVICE_NAME,device.strName);
        jsonDevice.insert(STR_DEVICE_PMS,device.strPMS);
        //        jsonDevice.insert(STR_DEVICE_ALARM,device.staDeviceAlarmPara.iDeviceAlarm);
        //        jsonDevice.insert(STR_DEVICE_ALARM_MAX,device.staDeviceAlarmPara.iDeviceAlarmMax);
        jsonDeviceList.insert(i,jsonDevice);
    }
    insert(STR_DATA,jsonDeviceList);
    insert(STR_COUNT,i);
}

/************************************************
* 函数名:  getDeviceNameList
* 输入参数:  NULL
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取设备名称列表
************************************************/
void ConfigData::getDeviceNameList(void)
{
    ConfigService &configService = ConfigService::instance();
    const StationNode &deviceTree =  configService.stationNode();
    QJsonArray jsonDeviceList;
    for (int i = 0;i < deviceTree.devices.size(); i++)
    {
        QJsonObject jsonDevice;
        const DeviceNode &device = deviceTree.devices.at(i);
        jsonDevice.insert(STR_DEVICE_ID, device.strDeviceGUID);
        jsonDevice.insert(STR_DEVICE_NAME, device.strName);
        jsonDevice.insert(STR_IS_PARENT, !device.testPoints.isEmpty());
        jsonDevice.insert(STR_NODE_HAS_CHILD,!device.testPoints.isEmpty());

        jsonDeviceList.append(jsonDevice);
    }
    insert(STR_DATA,jsonDeviceList);
}

/************************************************
* 函数名:  getDeviceNameListApp
* 输入参数:  NULL
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取设备名称列表
************************************************/
void ConfigData::getDeviceNameListApp(void)
{
    ConfigService &configService = ConfigService::instance();
    const StationNode &deviceTree =  configService.stationNode();
    QJsonArray jsonDeviceList;
    for (int i = 0;i < deviceTree.devices.size(); i++)
    {
        QJsonObject jsonDevice;
        const DeviceNode &device = deviceTree.devices.at(i);
        jsonDevice.insert(STR_DEVICE_ID, device.strDeviceGUID);
        jsonDevice.insert(STR_DEVICE_NAME, device.strName);
        jsonDevice.insert(STR_IS_PARENT, !device.testPoints.isEmpty());
        jsonDevice.insert(STR_NODE_HAS_CHILD,!device.testPoints.isEmpty());

        jsonDevice.insert(STR_DEVICE_PMS,device.strPMS);
        QString deviceName = ConfigService::instance().listDeviceTypeName().at(device.eDeviceType - 1);
        jsonDevice.insert(STR_DEVICE_TYPE_NAME, deviceName);
        QString voltageName = ConfigService::instance().listVoltageLevelName().at(device.eVoltage);
        jsonDevice.insert(STR_DEVICE_VOL_LEVEL, voltageName);
        jsonDevice.insert(STR_POINT_COUNT, deviceTree.devices.at(i).testPoints.size());

        jsonDeviceList.append(jsonDevice);
    }
    insert(STR_DATA,jsonDeviceList);
}

/************************************************
* 函数名:  GetDeviceInfo
* 输入参数:  NULL
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取设备详情
************************************************/
void ConfigData::GetDeviceInfo(const QString &strDeviceCode)
{
    ConfigService &configService = ConfigService::instance();
    const StationNode &deviceTree =  configService.stationNode();
    if (deviceTree.strName != "")
    {
        for (int i = 0; i < deviceTree.devices.size(); i++)
        {
            const DeviceNode &device = deviceTree.devices.at(i);
            if (device.strDeviceGUID == strDeviceCode)
            {
                insert(STR_DEVICE_ID,device.strDeviceGUID);
                //                insert(STR_DEVICE_ALARM,device.staDeviceAlarmPara.iDeviceAlarm);
                //                insert(STR_DEVICE_ALARM_MAX,device.staDeviceAlarmPara.iDeviceAlarmMax);
                insert(STR_DEVICE_NAME,device.strName);
                insert(STR_DEVICE_PMS,device.strPMS);
                QString deviceName = ConfigService::instance().listDeviceTypeName().at(device.eDeviceType - 1);
                insert(STR_DEVICE_TYPE_NAME, deviceName);
                QString voltageName = ConfigService::instance().listVoltageLevelName().at(device.eVoltage);
                insert(STR_DEVICE_VOL_LEVEL, voltageName);
            }
        }
    }
}

/************************************************
* 函数名:  GetADUInfo
* 输入参数:  strADUID -- 前端id
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取前端树信息
************************************************/
void ConfigData::GetADUInfo(const QString &strADUID)
{
    ADUUnitInfo adu;
    if (ConfigService::instance().getADU(strADUID,adu))
    {
        insert(STR_ADU_ID,adu.strID);
        insert(STR_ADU_NAME,adu.strName);
        QString strName = ConfigService::instance().getADUTypName(adu.eType);
        insert(STR_ADU_TYPE, strName);
        insert(STR_ADU_COMM_TYPE, ConfigService::instance().mapLinkGrp().key(adu.eLinkGroup));
        insert(STR_FREQUNCY,adu.stADUParam.ucFrequency);
        insert(STR_ADU_WORK_GROUP,adu.stADUParam.ucWorkGroup);
        insert(STR_ADU_NUM_IN_GROUP,adu.stADUParam.usNumInGroup);
        insert(STR_ADU_COMM_LOAD,adu.stADUParam.ucConnectionLoad);
        insert(STR_ADU_COMM_SPEED,adu.stADUParam.ucConnectionSpeed);
        insert(STR_ADU_SLEEP_TIME,(int)adu.stADUParam.uiSleepTime);
        insert(STR_ADU_SAMPLE_SPACE,(int)adu.stADUParam.uiSampleSpace);
        insert(STR_ADU_SAMPEL_START_TIMR,adu.stADUParam.usStartSampleTime);
        insert(STR_ADU_ARTIFICIAL_START_TIME,8);//adu.stADUParam.ucStartArtificialTime);
        insert(STR_ADU_ARTIFICIAL_END_TIME,20);//adu.stADUParam.ucEndArtificialTime);
        insert(STR_ADU_ARTIFICIAL_STATE_WAKE_UP_SPACE,3);//adu.stADUParam.usArtificialWakeUpInterval);
        insert(STR_ADU_UNARTIFICIAL_STATE_WAKE_UP_SPACE,30);//adu.stADUParam.usNotArtificialWalkUpInterval);
        insert(STR_ADU_IS_AUTO_CHANGE_MODE, 1);
        //        if ( Monitor::WORKER_MODEL_NORMAL != adu.stADUParam.eADUWorkModel)
        //        {
        insert(STR_ADU_MODE, ConfigService::instance().getADUWorkMode(adu.stADUParam.eADUWorkModel));
        //        }
        insert(STR_ADU_B_AUTO_GIVING,  adu.stADUParam.ucAutoUpdate);
        insert(STR_ADU_AUTO_GIVISPACE, (int)adu.stADUParam.uiAutoGivingSpace);
        insert(STR_ADU_MONITOR_MODE_SAMPLE_SPACE,adu.stADUParam.usTutelageSampleSpace);
        insert(STR_ADU_RS485_COM_PORT, adu.strRS485ComPort);
        insert(STR_SF6_ALARM_TASK_GROUP, (int)adu.iTaskGroup);
        insert(STR_ADU_EXTEND, adu.stADUParam.extendInfo);
        insert("thresholdMode", (int)adu.stADUParam.eAduFilterNoiseMode);
        insert("threshold", static_cast<int>(adu.stADUParam.fUesdThreshold*100));

    }
}

/************************************************
* 函数名:  getChannelInfo
* 输入参数:  strADUID -- 前端id
*           ucChannelID -- 通道索引
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取前端信息
************************************************/
void ConfigData::getChannelInfo(const QString &strADUID, quint8 ucChannelID)
{
    ADUChannelInfo channel;
    if (ConfigService::instance().getChannelInfo(strADUID,ucChannelID,channel))
    {
        QString strName = ConfigService::instance().getChanTypName(channel.etype);
        insert(STR_CHANNEL_TYPE, strName);
        insert(STR_CHANNEL_INDEX,channel.unID);
        insert(STR_CHANNEL_NAME,channel.strName);

        switch (channel.etype)
        {
        case CHANNEL_AE://AE参数
        {
            AEChannelPara stAEChannelPara =  channel.stachpara.staAEPara;
            insert(STR_PARA_GAIN,stAEChannelPara.cGain);//增益
            QJsonArray jsonGains;
            jsonGains.append(0);
            jsonGains.append(60);
            jsonGains.append(80);
            jsonGains.append(100);
            insert(STR_PARA_GAIN_LIST, jsonGains);
            insert(STR_PARA_GAIN_MODEL, ConfigService::instance().listGainModel().at(stAEChannelPara.eChannelGainType));//增益类型
            QJsonArray jsonGainModels;
            QList<QString> gainModels = ConfigService::instance().listGainModel();
            for (int i = 0; i < gainModels.size(); i++)
            {
                jsonGainModels.append(gainModels.at(i));
            }
            insert(STR_PARA_GAIN_MODEL_LIST, jsonGainModels);
            insert(STR_PARA_SAMPLE_PERIOD,stAEChannelPara.ucSampleCycles);//采样周期数
            QJsonArray jsonSamplePeriods;
            jsonSamplePeriods.append(2);
            jsonSamplePeriods.append(5);
            jsonSamplePeriods.append(10);
            insert(STR_PARA_SAMPLE_PERIOD_LIST, jsonSamplePeriods);
            insert(STR_PARA_SAMPLE_POINTS,stAEChannelPara.usSampelCount);//采样点数

            //触发阈值列表
            QJsonArray jsonTriggerThresholds{1};
            insert(STR_PARA_TRIGGER_THRESHOLDS, jsonTriggerThresholds);
            //触发阈值
            insert(STR_PARA_TRIGGER_THRESHOLD, stAEChannelPara.fTriggerThreshold);
            //开门时间列表
            QJsonArray jsonOpenTimeList{100, 150, 200, 250, 300, 350, 400, 450, 500, 600, 700, 800, 900};
            insert(STR_PARA_OPEN_TIME_LIST, jsonOpenTimeList);
            //开门时间
            insert(STR_PARA_OPEN_TIME, stAEChannelPara.sOpenTime);
            //关门时间列表
            QJsonArray jsonCloseTimeList{2000, 3000, 5000, 7000, 10000, 14000, 18000, 25000};
            insert(STR_PARA_CLOSE_TIME_LIST, jsonCloseTimeList);
            //关门时间
            insert(STR_PARA_CLOSE_TIME, stAEChannelPara.sCloseTime);
        }
            break;
        case CHANNEL_TEV://TEV参数
        {
            insert(STR_PARA_BACK_GROUND_DATA, channel.stachpara.staTEVPara.cBackGroundNum);
        }
            break;
        case CHANNEL_UHF:
        {
            UHFChannelPara stUHFChannelPara =  channel.stachpara.staUHFPara;
            insert(STR_PARA_GAIN,stUHFChannelPara.cGain);//增益
            QJsonArray jsonGains;
            jsonGains.append(0);
            jsonGains.append(20);
            insert(STR_PARA_GAIN_LIST, jsonGains);
            insert(STR_PARA_GAIN_MODEL, ConfigService::instance().listGainModel().at(stUHFChannelPara.eChannelGainType));//增益类型
            QJsonArray jsonGainModels;
            QList<QString> gainModels = ConfigService::instance().listGainModel();
            for (int i = 0; i < gainModels.size(); i++)
            {
                jsonGainModels.append(gainModels.at(i));
            }
            insert(STR_PARA_GAIN_MODEL_LIST, jsonGainModels);
            insert(STR_PARA_SAMPLE_PERIOD,stUHFChannelPara.ucSampleCycles);//采样周期数
            QJsonArray jsonSamplePeriods;
            jsonSamplePeriods.append(50);
            jsonSamplePeriods.append(60);
            insert(STR_PARA_SAMPLE_PERIOD_LIST, jsonSamplePeriods);
            insert(STR_PARA_SAMPLE_POINTS,stUHFChannelPara.usSampelCount);//采样点数
            QJsonArray jsonSamplePonits;
            jsonSamplePonits.append(60);
            jsonSamplePonits.append(72);
            insert(STR_PARA_SAMPLE_POINTS_LIST, jsonSamplePonits);
            insert(STR_PARA_FILTER, ConfigService::instance().getBandWidthName(stUHFChannelPara.cBandWidth));//带宽
            QJsonArray jsonBandWidths;
            QList<QString> bandWidths = ConfigService::instance().listBandWidth();
            for (int i = 0; i < bandWidths.size(); i++)
            {
                jsonBandWidths.append(bandWidths.at(i));
            }
            insert(STR_PARA_BAND_WIDTH_LIST, jsonBandWidths);
        }
            break;
        case CHANNEL_HFCT:
        {
            HFCTChannelPara stHFCTChannelPara =  channel.stachpara.staHFCTPara;
            insert(STR_PARA_GAIN, -1 == stHFCTChannelPara.cGain ? 0 : stHFCTChannelPara.cGain);//增益为-1时，表示无增益
            QJsonArray jsonGains;
            if(-1 == stHFCTChannelPara.cGain)
            {
                jsonGains.append(0);
            }
            else
            {
                jsonGains.append(0);
                jsonGains.append(20);
                //jsonGains.append(-20);
                //jsonGains.append(-40);
            }
            insert(STR_PARA_GAIN_LIST, jsonGains);
            insert(STR_PARA_GAIN_MODEL, ConfigService::instance().listGainModel().at(stHFCTChannelPara.eChannelGainType));//增益类型
            QJsonArray jsonGainModels;
            QList<QString> gainModels = ConfigService::instance().listGainModel();
            for (int i = 0; i < gainModels.size(); i++)
            {
                jsonGainModels.append(gainModels.at(i));
            }
            insert(STR_PARA_GAIN_MODEL_LIST, jsonGainModels);
            insert(STR_PARA_SAMPLE_PERIOD,stHFCTChannelPara.ucSampleCycles);//采样周期数
            QJsonArray jsonSamplePeriods;
            jsonSamplePeriods.append(50);
            jsonSamplePeriods.append(60);
            insert(STR_PARA_SAMPLE_PERIOD_LIST, jsonSamplePeriods);
            insert(STR_PARA_SAMPLE_POINTS,stHFCTChannelPara.usSampelCount);//采样点数
            QJsonArray jsonSamplePonits;
            jsonSamplePonits.append(60);
            jsonSamplePonits.append(72);
            insert(STR_PARA_SAMPLE_POINTS_LIST, jsonSamplePonits);
        }
            break;
        case CHANNEL_MECH:
        {
            MechChannelPara stMECHChannelPara =  channel.stachpara.staMechPara;
            insert(STR_PARA_LOOP_CURRENT_THRED,stMECHChannelPara.usLoopCurrentThred);//线圈电流阈值
            insert(STR_PARA_MOTOR_CURRENT_THRED,stMECHChannelPara.usMotorCurrentThred);//电机电流阈值
            insert(STR_PARA_SWITCH_STATE,int(stMECHChannelPara.bSwitchState));//开关量初始状态
            insert(STR_PARA_BREAK_TYPE,int(stMECHChannelPara.bBreakerType));//断路器机构类型
            insert(STR_PARA_MOTOR_FUNCTION_TYPE,int(stMECHChannelPara.bMotorFunctionType));//电机工作类型
        }
            break;
        case CHANNEL_ARRESTER_I:
        case CHANNEL_GROUNDDINGCURRENT:
        case CHANNEL_LEAKAGECURRENT:
        {
            ArresterIChannelPara stArresterIChannelPara =  channel.stachpara.staArresterIPara;
            insert(STR_PARA_CHANNEL_PHASE, stArresterIChannelPara.eChannelPhase);//
        }
            break;
        case CHANNEL_ARRESTER_U:
        {
            ArresterUChannelPara stArresterUChannelPara =  channel.stachpara.staArresterUPara;
            insert(STR_PARA_CHANNEL_PHASE,stArresterUChannelPara.eChannelPhase);//
            insert(STR_PARA_TRANSFORMATION_RATIO,stArresterUChannelPara.fTransformationRatio);//
        }
            break;
        case CHANNEL_VIBRATION:
        {
            QJsonArray jsonSamplePeriods;
            jsonSamplePeriods.append(2);
            jsonSamplePeriods.append(5);
            jsonSamplePeriods.append(10);

            insert(STR_PARA_SAMPLE_PERIOD_LIST, jsonSamplePeriods);
            insert(STR_PARA_SAMPLE_RATE,channel.stachpara.staVibrationParam.usSampelCount);//采样点数
            insert(STR_PARA_SAMPLE_PERIOD,channel.stachpara.staVibrationParam.ucSampleCycles);//采样周期数
        }
            break;
        case CHANNEL_SAW:
        {
        }
            break;
        case CHANNEL_AX8:
        {
        }
            break;
        case CHANNEL_TEMPERATURE:
        {
            insert("upThreshold", channel.stachpara.staTempParam.fUpperThreshold);//温度阈值上限
            insert("lowerThreshold", channel.stachpara.staTempParam.fLowerThreshold);//温度阈值下限
            insert("changeThreshold", channel.stachpara.staTempParam.fChangedThreshold);//温度变送阀值
        }
            break;
        case CHANNEL_HUMIDITY:
        {
            insert("upThreshold", channel.stachpara.staHumParam.fUpperThreshold);//湿度阈值上限
            insert("lowerThreshold", channel.stachpara.staHumParam.fLowerThreshold);//湿度阈值下限
            insert("changeThreshold", channel.stachpara.staHumParam.fChangedThreshold);//湿度变送阀值
        }
            break;
        case CHANNEL_FLOOD:
        {
            insert(STR_PARA_ALARM, channel.stachpara.stFloodChannelPara.cFlood);//水浸告警
        }
            break;
        case CHANNEL_TEVPRPS:
        {
            insert(STR_PARA_SAMPLE_POINTS, channel.stachpara.staTEVPRPSPara.usSampelCount);
        }
            break;
        default:
            break;
        }

    }


}

/************************************************
* 函数名:  GetADUTree
* 输入参数:  NULL
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取前端树信息
************************************************/
void ConfigData::GetADUTree(void)
{
    //    ConfigService &configService = ConfigService::instance();
    //    const QList<ADUUnitInfo> &deviceTree =  configService.ADUList();


    //    if (deviceTree.strName != "")
    //    {
    //        insert(STR_STATION_NAME,deviceTree.strName);
    //        QJsonArray jsonADUs;

    //            for (int j = 0; j < device.adus.size(); j++)
    //            {
    //                const ADUUnitInfo &adu = device.adus.at(j);
    //                if (adu.eType == ADU_TYPE_NUM)
    //                {
    //                    continue;
    //                }
    //                QJsonObject jsonADU;
    //                jsonADU.insert(STR_ADU_ID, adu.strID);
    //                jsonADU.insert(STR_ADU_NAME, adu.strName);
    //                jsonADU.insert(STR_ADU_TYPE, getstrADUType(adu.eType));
    //                QJsonArray jsonChannels;
    //                for(int k = 0; k <adu.Channels.count(); k++)
    //                {
    //                    const ADUChannelInfo &channel = adu.Channels.at(k);
    //                    QJsonObject jsonChannel;
    //                    jsonChannel.insert(STR_POINT_NAME, channel.strName);
    //                    jsonChannel.insert(STR_POINT_TYPE, getstrChannelType(channel.etype));
    //                    jsonChannels.append(jsonChannel);
    //                }
    //                jsonADU.insert(STR_POINT_ITEMS,jsonChannels);
    //                jsonADUs.append(jsonADU);
    //            }
    //        }
    //        insert("aduItems",jsonADUs);
    //    }
}

/************************************************
 * 函数名:  getChannelPara
 * 输入参数:  eChannelType -- 通道类型枚举
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取通道参数
 ************************************************/
QJsonObject ConfigData::getChannelPara(ADUChannelType eChannelType, const Channelpara & stachpara)
{
    QJsonObject jsonChannelPara;
    switch (eChannelType)
    {
    case CHANNEL_AE://AE参数
    {
        jsonChannelPara.insert(STR_PARA_GAIN,stachpara.staAEPara.cGain);//增益
//        jsonChannelPara.insert(STR_PARA_THRE_NUM,stachpara.staAEPara.cThreNum);//阈值报警数量
//        jsonChannelPara.insert(STR_PARA_THRE_RAT,stachpara.staAEPara.cThreRat);//阈值报警比例
//        jsonChannelPara.insert(STR_PARA_THRE_WARNING,stachpara.staAEPara.cThreWarning);//阈值预警值
//        jsonChannelPara.insert(STR_PARA_THRE_ALARM,stachpara.staAEPara.cThreAlarm);//阈值报警值
//        jsonChannelPara.insert(STR_PARA_DIFF_WARNING,stachpara.staAEPara.cDiffWarning);//差值预警值
//        jsonChannelPara.insert(STR_PARA_DIFF_ALARM,stachpara.staAEPara.cDiffAlarm);//差值报警值
        jsonChannelPara.insert(STR_PARA_CHANNEL_PHASE,stachpara.staAEPara.eChannelPhase);//相别
    }
        break;
    case CHANNEL_TEV://TEV参数
    {
//        jsonChannelPara.insert(STR_PARA_THRE_NUM,stachpara.staTEVPara.cThreNum);//阈值报警数量
//        jsonChannelPara.insert(STR_PARA_THRE_RAT,stachpara.staTEVPara.cThreRat);//阈值报警比例
//        jsonChannelPara.insert(STR_PARA_THRE_WARNING,stachpara.staTEVPara.cThreWarning);//阈值预警值
//        jsonChannelPara.insert(STR_PARA_THRE_ALARM,stachpara.staTEVPara.cThreAlarm);//阈值报警值
//        jsonChannelPara.insert(STR_PARA_DIFF_WARNING,stachpara.staTEVPara.cDiffWarning);//差值预警值
//        jsonChannelPara.insert(STR_PARA_DIFF_ALARM,stachpara.staTEVPara.cDiffAlarm);//差值报警值
        jsonChannelPara.insert(STR_PARA_CHANNEL_PHASE,stachpara.staTEVPara.eChannelPhase);//相别
    }
        break;
    case CHANNEL_UHF:
    {
        jsonChannelPara.insert(STR_PARA_GAIN,stachpara.staUHFPara.cGain);//增益
        jsonChannelPara.insert(STR_PARA_FILTER,stachpara.staUHFPara.cBandWidth);//带宽
//        jsonChannelPara.insert(STR_PARA_PRPS_THRE,stachpara.staUHFPara.cPRPSThre);//prps图谱报警阈值
//        jsonChannelPara.insert(STR_PARA_THRE_NUM,stachpara.staUHFPara.cThreNum);//阈值报警数量
//        jsonChannelPara.insert(STR_PARA_THRE_RAT,stachpara.staUHFPara.cThreRat);//阈值报警比例
//        jsonChannelPara.insert(STR_PARA_THRE_WARNING,stachpara.staUHFPara.cThreWarning);//阈值预警值
//        jsonChannelPara.insert(STR_PARA_THRE_ALARM,stachpara.staUHFPara.cThreAlarm);//阈值报警值
//        jsonChannelPara.insert(STR_PARA_DIFF_WARNING,stachpara.staUHFPara.cDiffWarning);//差值预警值
//        jsonChannelPara.insert(STR_PARA_DIFF_ALARM,stachpara.staUHFPara.cDiffAlarm);//差值报警值
        jsonChannelPara.insert(STR_PARA_CHANNEL_PHASE,stachpara.staUHFPara.eChannelPhase);//相别
    }
        break;
    case CHANNEL_HFCT:
    {
        jsonChannelPara.insert(STR_PARA_GAIN,stachpara.staHFCTPara.cGain);//增益
//        jsonChannelPara.insert(STR_PARA_PRPS_THRE,stachpara.staHFCTPara.cPRPSThre);//prps图谱报警阈值
//        jsonChannelPara.insert(STR_PARA_THRE_NUM,stachpara.staHFCTPara.cThreNum);//阈值报警数量
//        jsonChannelPara.insert(STR_PARA_THRE_RAT,stachpara.staHFCTPara.cThreRat);//阈值报警比例
//        jsonChannelPara.insert(STR_PARA_THRE_WARNING,stachpara.staHFCTPara.cThreWarning);//阈值预警值
//        jsonChannelPara.insert(STR_PARA_THRE_ALARM,stachpara.staHFCTPara.cThreAlarm);//阈值报警值
//        jsonChannelPara.insert(STR_PARA_DIFF_WARNING,stachpara.staHFCTPara.cDiffWarning);//差值预警值
//        jsonChannelPara.insert(STR_PARA_DIFF_ALARM,stachpara.staHFCTPara.cDiffAlarm);//差值报警值
//        jsonChannelPara.insert(STR_PARA_CHANNEL_PHASE,stachpara.staHFCTPara.eChannelPhase);//相别
    }
        break;
    case CHANNEL_MECH:
    {
        jsonChannelPara.insert(STR_PARA_CLOSE_TIME,stachpara.staMechPara.fCloseTime);//合闸时间
        jsonChannelPara.insert(STR_PARA_OPEN_TIME,stachpara.staMechPara.fOpenTime);//分闸时间
        jsonChannelPara.insert(STR_PARA_CLOSE_SYNC,stachpara.staMechPara.fCloseSync);//合闸同期性
        jsonChannelPara.insert(STR_PARA_OPEN_SYNC,stachpara.staMechPara.fOpenSync);//分闸同期性
        jsonChannelPara.insert(STR_PARA_CIRCUIT_CURRETN,stachpara.staMechPara.fCircuitCurrent);//线圈额定电流
        jsonChannelPara.insert(STR_PARA_MOTOR_CURRETN,stachpara.staMechPara.fMotorCurrent);//电机额定电流
        jsonChannelPara.insert(STR_PARA_ENGERGY_STORE_TIME,stachpara.staMechPara.fEnergyStoreTime);//电机储能时间
    }
        break;
    case CHANNEL_SAW:
    {
        jsonChannelPara.insert(STR_PARA_ALARM,stachpara.staSAWPara.cAlarm);//报警阈值
        jsonChannelPara.insert(STR_PARA_TEMPERATURE_DIFFERENCE,stachpara.staSAWPara.cTemperatureDifference);//温升差值
        jsonChannelPara.insert(STR_PARA_THREE_TRIP_DIFFERENCE,stachpara.staSAWPara.cThreeTripDifference);//三项差值
    }
        break;
    case CHANNEL_AX8:
    {
        jsonChannelPara.insert(STR_PARA_TEMPERATURE,stachpara.staAX8Para.cTemperature);//温度
        jsonChannelPara.insert(STR_PARA_DISTANCE_UNIT,stachpara.staAX8Para.cDistanceUnit);//距离单位
        jsonChannelPara.insert(STR_PARA_FTP,stachpara.staAX8Para.cFTP);//文件传输协议
        jsonChannelPara.insert(STR_PARA_FOLDER,stachpara.staAX8Para.cFolder);//文件夹
    }
        break;
    case CHANNEL_TEMPERATURE:
    {
        //TODO 温湿度智能传感器
    }
        break;
    case CHANNEL_HUMIDITY:
    {
        //TODO 温湿度智能传感器
    }
        break;
    case CHANNEL_FLOOD:
    {
        jsonChannelPara.insert(STR_PARA_ALARM,stachpara.stFloodChannelPara.cFlood);//水浸报警
    }
        break;
    default:
        break;
    }
    return jsonChannelPara;
}

/*************************************************
函数名： ChannelParaApplyAll
输入参数： jsonData -- 用户数据
输出参数： NULL
返回值： NULL
功能： 将单个通道参数应用于全部传感器
*************************************************************/
void ConfigData::ChannelParaApplyAll(const QJsonObject &jsonData)
{
    ConfigService &configs = ConfigService::instance();
    Channelpara staChannelPara;
    ADUChannelType eChannelType = ADUChannelType(jsonData.value(STR_POINT_TYPE).toInt());
    switch (eChannelType)
    {
    case CHANNEL_AE://AE参数
    {
        staChannelPara.staAEPara.cGain = jsonData.value(STR_PARA_GAIN).toInt();//增益
//        staChannelPara.staAEPara.cThreWarning = jsonData.value(STR_PARA_ALARM).toInt();//报警值
//        staChannelPara.staAEPara.cThreAlarm = jsonData.value(STR_PARA_WARNING).toInt();//预警值
//        staChannelPara.staAEPara.cDiffWarning = jsonData.value(STR_PARA_ALARM).toInt();//报警值
//        staChannelPara.staAEPara.cDiffAlarm = jsonData.value(STR_PARA_WARNING).toInt();//预警值
//        staChannelPara.staAEPara.cDiffTime = jsonData.value(STR_PARA_WARNING).toInt();//预警值
    }
        break;
    case CHANNEL_TEV://TEV参数
    {
//        staChannelPara.staTEVPara.cThreWarning = jsonData.value(STR_PARA_ALARM).toInt();//报警值
//        staChannelPara.staTEVPara.cThreAlarm = jsonData.value(STR_PARA_WARNING).toInt();//预警值
//        staChannelPara.staTEVPara.cDiffWarning = jsonData.value(STR_PARA_ALARM).toInt();//报警值
//        staChannelPara.staTEVPara.cDiffAlarm = jsonData.value(STR_PARA_WARNING).toInt();//预警值
//        staChannelPara.staTEVPara.cDiffTime = jsonData.value(STR_PARA_WARNING).toInt();//预警值
    }
        break;
    case CHANNEL_UHF:
    {
        staChannelPara.staUHFPara.cGain = jsonData.value(STR_PARA_GAIN).toInt();//增益
//        staChannelPara.staUHFPara.cPRPSThre = jsonData.value(STR_PARA_ALARM).toInt();//报警值
//        staChannelPara.staUHFPara.cThreWarning = jsonData.value(STR_PARA_WARNING).toInt();//预警值
//        staChannelPara.staUHFPara.cThreAlarm = jsonData.value(STR_PARA_ALARM).toInt();//报警值
//        staChannelPara.staUHFPara.cDiffWarning = jsonData.value(STR_PARA_WARNING).toInt();//预警值
//        staChannelPara.staUHFPara.cDiffAlarm = jsonData.value(STR_PARA_ALARM).toInt();//报警值
//        staChannelPara.staUHFPara.cDiffTime = jsonData.value(STR_PARA_WARNING).toInt();//预警值
        staChannelPara.staUHFPara.cBandWidth = (UHFDataFilter)jsonData.value(STR_PARA_FILTER).toInt();//带宽
    }
        break;
    case CHANNEL_HFCT:
    {
        staChannelPara.staHFCTPara.cGain = jsonData.value(STR_PARA_GAIN).toInt();//增益
//        staChannelPara.staHFCTPara.cPRPSThre = jsonData.value(STR_PARA_ALARM).toInt();//报警值
//        staChannelPara.staHFCTPara.cThreWarning = jsonData.value(STR_PARA_WARNING).toInt();//预警值
//        staChannelPara.staHFCTPara.cThreAlarm = jsonData.value(STR_PARA_ALARM).toInt();//报警值
//        staChannelPara.staHFCTPara.cDiffWarning = jsonData.value(STR_PARA_WARNING).toInt();//预警值
//        staChannelPara.staHFCTPara.cDiffAlarm = jsonData.value(STR_PARA_ALARM).toInt();//报警值
//        staChannelPara.staHFCTPara.cDiffTime = jsonData.value(STR_PARA_WARNING).toInt();//预警值
    }
        break;
    case CHANNEL_MECH:
    {
        staChannelPara.staMechPara.fCloseTime = jsonData.value(STR_PARA_CLOSE_TIME).toDouble();//合闸时间
        staChannelPara.staMechPara.fOpenTime = jsonData.value(STR_PARA_OPEN_TIME).toDouble();//分闸时间
        staChannelPara.staMechPara.fCloseSync = jsonData.value(STR_PARA_CLOSE_SYNC).toDouble();//合闸同期性
        staChannelPara.staMechPara.fOpenSync = jsonData.value(STR_PARA_OPEN_SYNC).toDouble();//分闸同期性
        staChannelPara.staMechPara.fCircuitCurrent = jsonData.value(STR_PARA_CIRCUIT_CURRETN).toDouble();//线圈额定电流
        staChannelPara.staMechPara.fMotorCurrent = jsonData.value(STR_PARA_MOTOR_CURRETN).toDouble();//电机额定电流
        staChannelPara.staMechPara.fEnergyStoreTime = jsonData.value(STR_PARA_ENGERGY_STORE_TIME).toDouble();//电机储能时间
    }
        break;
    case CHANNEL_SAW:
    {
        staChannelPara.staSAWPara.cAlarm = jsonData.value(STR_PARA_ALARM).toInt();//报警值
        staChannelPara.staSAWPara.cTemperatureDifference = jsonData.value(STR_PARA_TEMPERATURE_DIFFERENCE).toInt();//温差报警
        staChannelPara.staSAWPara.cThreeTripDifference = jsonData.value(STR_PARA_THREE_TRIP_DIFFERENCE).toInt();//三项升温
    }
        break;
    case CHANNEL_AX8:
    {
        staChannelPara.staAX8Para.cTemperature = jsonData.value(STR_PARA_TEMPERATURE).toInt();//
        staChannelPara.staAX8Para.cDistanceUnit = jsonData.value(STR_PARA_DISTANCE_UNIT).toInt();//
        staChannelPara.staAX8Para.cFTP = jsonData.value(STR_PARA_FTP).toInt();//
        staChannelPara.staAX8Para.cFolder = jsonData.value(STR_PARA_FOLDER).toInt();//
    }
        break;
    case CHANNEL_TEMPERATURE:
    {
        //TODO 温湿度智能传感器
    }
        break;
    case CHANNEL_HUMIDITY:
    {
        //TODO 温湿度智能传感器
    }
        break;
    default:
        break;
    }

    configs.setAllChannelPara(staChannelPara, eChannelType);
}

/************************************************
 * 函数名:  getChannelParaFromJson
 * 输入参数:  jsonData -- 用户数据
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  从请求中读取通道参数
 ************************************************/
bool ConfigData::getChannelParaFromJson(const QJsonObject &jsonData, Channelpara &staChannelPara)
{
    bool bRet = false;
    ADUUnitInfo ADU;
    if (ConfigService::instance().getADU(jsonData.value(STR_ADU_ID).toString(), ADU))
    {
        if (jsonData.value(STR_ADU_ID).toString() == ADU.strID)
        {
            quint8 ucChannelIndex = jsonData.value(STR_CHANNEL_INDEX).toInt();
            staChannelPara = ADU.Channels.at(ucChannelIndex).stachpara;
            switch (ADU.Channels.at(ucChannelIndex).etype)
            {
            case CHANNEL_AE://AE参数
            {
                staChannelPara.staAEPara.cGain = jsonData.value(STR_PARA_GAIN).toInt();
                staChannelPara.staAEPara.eChannelGainType = (ChannelGainType)jsonData.value(STR_PARA_GAIN_MODEL).toInt();
                staChannelPara.staAEPara.ucSampleCycles = jsonData.value(STR_PARA_SAMPLE_PERIOD).toInt();
                staChannelPara.staAEPara.usSampelCount = jsonData.value(STR_PARA_SAMPLE_POINTS).toInt();
                bRet = true;
            }
                break;
            case CHANNEL_UHF:
            {
                //增益
                staChannelPara.staUHFPara.cGain = jsonData.value(STR_PARA_GAIN).toInt();
                //增益模式
                staChannelPara.staUHFPara.eChannelGainType = (ChannelGainType)jsonData.value(STR_PARA_GAIN_MODEL).toInt();
                //采样周期
                staChannelPara.staUHFPara.ucSampleCycles = jsonData.value(STR_PARA_SAMPLE_PERIOD).toInt();
                //采样点数
                staChannelPara.staUHFPara.usSampelCount = jsonData.value(STR_PARA_SAMPLE_POINTS).toInt();
                //滤波设置
                staChannelPara.staUHFPara.cBandWidth = (UHFDataFilter)jsonData.value(STR_PARA_FILTER).toInt();
                bRet = true;
            }
                break;
            case CHANNEL_HFCT:
            {
                staChannelPara.staHFCTPara.cGain = jsonData.value(STR_PARA_GAIN).toInt();
                staChannelPara.staHFCTPara.eChannelGainType = (ChannelGainType)jsonData.value(STR_PARA_GAIN_MODEL).toInt();
                staChannelPara.staHFCTPara.ucSampleCycles = jsonData.value(STR_PARA_SAMPLE_PERIOD).toInt();
                staChannelPara.staHFCTPara.usSampelCount = jsonData.value(STR_PARA_SAMPLE_POINTS).toInt();
                bRet = true;
            }
                break;
            case CHANNEL_VIBRATION:
            {
                staChannelPara.staVibrationParam.ucSampleCycles = jsonData.value(STR_PARA_SAMPLE_PERIOD).toInt();
                staChannelPara.staVibrationParam.usSampelCount = jsonData.value(STR_PARA_SAMPLE_POINTS).toInt();
                bRet = true;
            }
                break;
            case CHANNEL_ARRESTER_U:
            {
                staChannelPara.staArresterUPara.fTransformationRatio = jsonData.value(STR_PARA_RATIO).toInt();
                bRet = true;
            }
                break;
            case CHANNEL_TEMPERATURE:
            {
                //TODO 温湿度智能传感器
            }
                break;
            case CHANNEL_HUMIDITY:
            {
                //TODO 温湿度智能传感器
            }
                break;
            default:
                break;
            }
        }
    }
    return bRet;
}

/************************************************
 * 函数名:  GetADUVersionList
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取前端版本号信息
 ************************************************/
QJsonArray ConfigData::GetADUVersionList(const QString &strADUType)
{
    QJsonArray jsonData;
    ADUType eADUType = ConfigService::instance().getADUTypEnum(strADUType);
    ConfigService &configService = ConfigService::instance();
    const QList<ADUUnitInfo> &ADUList =  configService.ADUList();
    for (int j = 0; j < ADUList.size(); j++)
    {
        const ADUUnitInfo &adu = ADUList.at(j);
        if (adu.eType == ADU_TYPE_NUM || eADUType != adu.eType)
        {
            continue;
        }
        QJsonObject jsonADU;
        jsonADU.insert(STR_ADU_ID,adu.strID);
        jsonADU.insert(STR_ADU_NAME,adu.strName);
        jsonADU.insert(STR_ADU_VERSION,adu.strVersion);

        jsonData.append(jsonADU);
    }
    return jsonData;
}

/************************************************
 * 函数名:  getRelationInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取测点关联信息
 ************************************************/
void ConfigData::getRelationInfo(const QString &strTestPointID)
{
    ConfigService &configService = ConfigService::instance();
    TestPointInfo stTestPoint;
    if (configService.getTestPoint(strTestPointID, stTestPoint))
    {
        QJsonObject jsonpoint;

        jsonpoint.insert(STR_TEST_POINT_ID, stTestPoint.strPointGUID);
        jsonpoint.insert(STR_TEST_POINT_NAME_DOC, stTestPoint.strName);
        insert(STR_TEST_POINT_NAME, stTestPoint.strOutName);
        insert(STR_TEST_POINT, jsonpoint);

        QJsonArray jsonADUChannels;
        for (int i = 0; i < stTestPoint.ConnectionInfo.size(); i++)
        {
            QJsonObject jsonADUChannel;

            jsonADUChannel.insert(STR_ADU_ID, stTestPoint.ConnectionInfo.at(i).strID);
            ADUType eADUType;
            configService.getADUTypeFromID(stTestPoint.ConnectionInfo.at(i).strID, eADUType);
            QString strName = ConfigService::instance().getADUTypName(eADUType);
            jsonADUChannel.insert(STR_ADU_TYPE, strName);

            jsonADUChannel.insert(STR_CHANNEL_INDEX, stTestPoint.ConnectionInfo.at(i).unID);
            QString strChaName = ConfigService::instance().getChanTypName(stTestPoint.ConnectionInfo.at(i).etype);
            jsonADUChannel.insert(STR_CHANNEL_TYPE, strChaName);
            jsonADUChannels.append(jsonADUChannel);
        }
        insert(STR_ADU_CHANNELS, jsonADUChannels);
    }
}

/************************************************
 * 函数名:  SavaModbus
 * 输入参数:  jsonData -- 用户数据
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  保存modbus信息参数
 ************************************************/
void ConfigData::SavaModbus(const QJsonObject &jsonData)
{
    ConfigService &configService = ConfigService::instance();
    ModbusSetting modbusSetting = configService.modbusSetting();
    modbusSetting.iBaud = jsonData.value(STR_BAURATE).toInt();
    modbusSetting.iDatabit = jsonData.value(STR_DATA_BIT).toInt();
    modbusSetting.iRts = jsonData.value(STR_RTS).toInt();
    modbusSetting.iStopbit = jsonData.value(STR_STOP_BIT).toInt();
    modbusSetting.strDevice = jsonData.value(STR_SERIAL_PORT).toString();
    modbusSetting.ucParity = jsonData.value(STR_CHECK_BIT).toString().at(0).toLatin1();
    configService.saveModbusSetting(modbusSetting);
}

/************************************************
 * 函数名:  NetworkSetting
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取网络信息
 ************************************************/
void ConfigData::NetworkSetting(void)
{
    ConfigService &configService = ConfigService::instance();
    WebSetting webSetting =  configService.webSetting();
    insert(STR_SERVER_PORT,webSetting.iServerPort);//端口
    insert(STR_SERVER_IP,webSetting.strServerAddress);//服务器地址
    insert(STR_NETWORK_NAME,getStringWebType(webSetting.eWebType));//所选网络类型（4G，以太）
    insert(STR_DEVICE_IP,webSetting.strIP);//本地ip
    insert(STR_SUBNET_MASK,webSetting.strSubnetMask);//子网掩码
    insert(STR_GATE_WAY,webSetting.strGateWay);//网关
    insert(STR_NETWORK_APN,/*getStringNetWorkAPN*/(webSetting.strWorkAPN));//蜂窝网络接入点（AT&T、China Unicom）
    insert(STR_NETWORK_USERNAME,webSetting.strNetworkUserName);//上网卡用户名
    insert(STR_NETWORK_PASSWORD,webSetting.strNetworkPassword);//上网卡密码
    insert(STR_NTP_IP, webSetting.strSNTPAddress);      // ntp服务器地址
    insert(STR_NTP_PORT, webSetting.strSNTPPort);       // ntp服务器端口
    insert(STR_4G_STRATEGY, (int)webSetting.bStrategy4G);    // 4G处理策略
}

/************************************************
 * 函数名:  NetworkSettingConfig
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取网络信息参数
 ************************************************/
void ConfigData::NetworkSettingConfig(void)
{
    QJsonArray jsonWebType;
    jsonWebType.append(STR_NETWORK_WIRED_NETWORK);
    jsonWebType.append(STR_NETWORK_3G);
    jsonWebType.append(STR_NETWORK_4G);
    insert(STR_NETWORK_NAME,jsonWebType);//所选网络类型（4G，以太）
    QJsonArray jsonNetWorkAPN;
    jsonNetWorkAPN.append(STR_NETWORK_AT_T);
    jsonNetWorkAPN.append(STR_NETWORK_CHINA_MOBILE);
    jsonNetWorkAPN.append(STR_NETWORK_CHINA_UNICOM);
    insert(STR_NETWORK_APN,jsonNetWorkAPN);//蜂窝网络接入点（AT&T、China Unicom）
}

/************************************************
 * 函数名:  SwitchUser
 * 输入参数:  jsonData -- 用户数据
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  切换用户
 ************************************************/
bool ConfigData::SwitchUser(const QString &strUserName, const QString &strPsaaword)
{
    UserName euserName;
    if (strUserName == STR_ADMIN)
    {
        euserName = USER_ADMIN;
    }
    else if (strUserName == STR_SUPERUSER)
    {
        euserName = USER_SUPERUSER;
    }

    return ConfigService::instance().isPasswordValid(strPsaaword, euserName);
}

/************************************************
 * 函数名:  SyncPonintData
 * 输入参数:  jsonData -- 用户数据
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  同步测点信息
 ************************************************/
void ConfigData::SyncPonintData(const QJsonArray &jsonData)
{
    for (int i = 0; i < jsonData.size(); i++)
    {
        //        int strChannelID = jsonData.at(i).toInt();

        //同步测点信息
    }
}

static QString getTestData(const PointConnectionInfo &cnnInfo)
{
    ADUChannelInfo channel;
    if(ConfigService::instance().getChannelInfo(cnnInfo.strID, cnnInfo.unID, channel))
    {
        return ConfigService::instance().channelDatatoString(channel.etype, channel.sampleData.singlePointData.data)
                + ConfigService::instance().getDataUnitString(channel.sampleData.singlePointData.eUnit);
    }
    else
    {
        return QString();
    }
}

/************************************************
 * 函数名:  getMonitoringFormsInfo
 * 输入参数:  iPage -- 页码
 *            iSize -- 每页的条数
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取检测表信息
 ************************************************/
void ConfigData::getMonitoringFormsInfo(int iPage, int iSize)
{
    const StationNode &deviceTree = ConfigService::instance().stationNode();

    int iCount = 0;

    QJsonArray arrayData;

    for (int i = 0; i < deviceTree.devices.size(); i++)
    {
        for (int j = 0; j < deviceTree.devices.at(i).testPoints.size(); j++)
        {
            const TestPointInfo &stPoint = deviceTree.devices.at(i).testPoints.at(j);

            if (!stPoint.ConnectionInfo.isEmpty())
            {
                iCount++;
                if ((iCount > ((iPage - 1) * iSize)) && (iCount <= (iPage * iSize)))
                {
                    ADUChannelType eChannelType = CHANNEL_TYPE_NUM;

                    QJsonObject jsonPointInfo;
                    jsonPointInfo.insert(STR_DEVICE_NAME, deviceTree.devices.at(i).strName);
                    jsonPointInfo.insert(STR_INDEX, iCount);
                    jsonPointInfo.insert(STR_POINT_NAME, stPoint.strOutName);
                    eChannelType = stPoint.ConnectionInfo.first().etype;
                    if (eChannelType == CHANNEL_INVALID)
                    {
                        PDS_SYS_ERR_LOG("Invalid ChannelType: %s %s", stPoint.strOutName.toLatin1().data(),
                                        stPoint.strPointGUID.toLatin1().data());
                        continue;
                    }

                    test::ADUUpdateStateInfo stateInfo;
                    QDateTime dateTime = QDateTime::currentDateTime();
                    bool isOnline = true;

                    if(ConfigService::instance().getAduBusState(stPoint.ConnectionInfo.first().strID, stateInfo))
                    {
                        if(stateInfo.isOnLine())
                        {
                            isOnline = true;
                        }
                        else
                        {
                            isOnline = false;
                        }

                        dateTime = stateInfo.dataUploadTime;
                    }

                    jsonPointInfo.insert(STR_POINT_ID, stPoint.strPointGUID);
                    jsonPointInfo.insert(STR_ADU_ID, stPoint.ConnectionInfo.first().strID);
                    QString strChanName = ConfigService::instance().getChanTypName(eChannelType);

                    jsonPointInfo.insert(STR_POINT_TYPE, strChanName);
                    jsonPointInfo.insert(STR_POINT_CONNECTION, isOnline);
                    if((eChannelType ==  CHANNEL_FAN) || (eChannelType == CHANNEL_LIGHT))
                    {
                        auto data = DBServer::instance().lastEnvStatusRecord(stPoint.strPointGUID);
                        if(data.status == EnvStatus::NORMAL)    //最近一次开关状态是：关
                        {
                            jsonPointInfo.insert(STR_POINT_DATA, 1);
                            PDS_SYS_INFO_LOG("is valid: %d, status: %d, data: 1", data.isValid(), data.status);

                        }
                        else                                    //最近一次开关状态是：开
                        {
                            jsonPointInfo.insert(STR_POINT_DATA, 0);
                            PDS_SYS_INFO_LOG("is valid: %d, status: %d, data: 0", data.isValid(), data.status);
                        }
                    }
                    if(dateTime.date().year() <= QDateTime::currentDateTime().date().year())
                    {
                        jsonPointInfo.insert(STR_TIME, dateTime.toString(STR_DATE_TIME_QSTRING_CNA));
                    }

                    bool bADUConnection = false;
                    ConfigService::instance().getADUConnection(stPoint.ConnectionInfo.first().strID, bADUConnection);
                    jsonPointInfo.insert("isConnection", bADUConnection);

                    int totalDataNum = DBServer::instance().totalDataNumInTable(stPoint.strPointGUID, eChannelType);
                    jsonPointInfo.insert(STR_TOTAL_DATA_NUM, totalDataNum);
                    arrayData.append(jsonPointInfo);
                }
            }
        }
    }

    insert(STR_TOTAL, iCount);
    insert("isConnecting", MonitorService::instance().isConnectingADU());
    insert(STR_ROWS, arrayData);
}

/************************************************
 * 函数名:  getMonitoringFormsInfo
 * 输入参数:  iPage -- 页码
 *            iSize -- 每页的条数
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取检测表信息
 ************************************************/
void ConfigData::GetADUStateInfo(int iPage, int iSize, QString strAduID, QString isOnline)
{
    QList<test::ADUUpdateStateInfo> listStateInfo;
    ConfigService::instance().getAduBusStates(listStateInfo);

    int iCount = 0;
    int onlineADUCount = 0;
    QJsonArray arrayData;

    for (int i = 0; i < listStateInfo.size(); i++)
    {
        test::ADUUpdateStateInfo stStateInfo = listStateInfo.at(i);
        if(stStateInfo.isOnLine())
        {
            onlineADUCount++;
        }
        if(!strAduID.isEmpty() && !stStateInfo.strAduId.contains(strAduID))
        {
           continue;
        }
        if(!isOnline.isEmpty() && (isOnline != "all"))
        {
            if(((isOnline == "online")&&(!stStateInfo.isOnLine())) || ((isOnline == "offline")&&(stStateInfo.isOnLine())))
            {
                continue;
            }
        }
        iCount++;
        if ((iCount > ((iPage - 1) * iSize)) && (iCount <= (iPage * iSize)))
        {
            ADUUnitInfo stInfo;
            ConfigService::instance().getADU(stStateInfo.strAduId, stInfo);
            QJsonObject jsonPointInfo;
            jsonPointInfo.insert(STR_INDEX, iCount);
            QString strADU = ConfigService::instance().getADUTypName(stInfo.eType);
            jsonPointInfo.insert(STR_ADU_TYPE, strADU);
            jsonPointInfo.insert(STR_ADU_ID, stStateInfo.strAduId);
            jsonPointInfo.insert(STR_ADU_POWER_SUPPLY_MODE, "byCable");

            const StationNode &deviceTree = ConfigService::instance().stationNode();
            for(int k = 0; k < deviceTree.devices.size(); ++k)
            {
                for(int j = 0; j < deviceTree.devices[k].testPoints.size(); j++)
                {
                    if( deviceTree.devices[k].testPoints[j].ConnectionInfo.size() &&
                            deviceTree.devices[k].testPoints[j].ConnectionInfo[0].strID == stInfo.strID)
                    {
                        jsonPointInfo.insert(STR_DEVICE_NAME,  deviceTree.devices[k].strName);
                        break;
                    }
                }
            }

            if(!stStateInfo.isOnLine())  //连接过，未失联，断开状态
            {
                jsonPointInfo.insert(STR_ADU_CONNECTED_STATE, "aduBreakOff");
                if(stStateInfo.lostTime.isValid() && (stStateInfo.lostTime.date().year() <= QDateTime::currentDateTime().date().year()))
                {
                    jsonPointInfo.insert(STR_ADU_DISCONNECT_TIME, stStateInfo.lostTime.toString(STR_DATE_TIME_QSTRING_CNA));
                }
                else
                {
                    jsonPointInfo.insert(STR_ADU_DISCONNECT_TIME, "");
                }
            }
            else
            {
                jsonPointInfo.insert(STR_ADU_CONNECTED_STATE, "aduIsConnected");
            }

            jsonPointInfo.insert(STR_ADU_COMM_TYPE, ConfigService::instance().mapLinkGrp().key((Monitor::LinkGroup)stInfo.stADUParam.eLinkGroup));
            if ( stStateInfo.bConnected && (stStateInfo.businessStateChangeTime.date().year() <= QDateTime::currentDateTime().date().year()))
            {
                jsonPointInfo.insert(STR_UPDATE_TIME, stStateInfo.businessStateChangeTime.toString(STR_DATE_TIME_QSTRING_CNA));
            }

            //电池功电
            if (  ( ADU_UHF_IS == stInfo.eType)
                  || ( ADU_HFCT_IS == stInfo.eType)
                  || ( ADU_PD_IS == stInfo.eType)
                  || ( ADU_ARRESTER_I_IS == stInfo.eType)
                  || ( ADU_ARRESTER_U_IS == stInfo.eType)
                  || ( ADU_GROUNDDINGCURRENT_IS == stInfo.eType)
                  || ( ADU_LEAKAGECURRENT_IS == stInfo.eType)
                  || ( ADU_VIBRATION_IS == stInfo.eType)
                  || ( ADU_TRANSFORMER_AE_IS == stInfo.eType)
                  || ( ADU_GIS_AE_IS == stInfo.eType)
                  || ( ADU_TEMP_HUM_IS == stInfo.eType)
                  || ( ADU_FLOOD_IS == stInfo.eType)
                  || ( ADU_TEVPRPS_IS == stInfo.eType))
            {
                jsonPointInfo.insert(STR_ADU_POWER_SUPPLY_MODE, "byBattery");

                if(stStateInfo.isOnLine())  //在线
                {
                    jsonPointInfo.insert(STR_ADU_BATTARY_STATE, stStateInfo.eBatteryState);
                    jsonPointInfo.insert(STR_ADU_BATTARY_VOLRTAGE, stStateInfo.BatteryVoltage);
                    jsonPointInfo.insert(STR_ADU_LORA_SIGNAL_LEVEL, stStateInfo.sSignalLevel);
                    jsonPointInfo.insert(STR_ADU_LORA_SNR, stStateInfo.sNSR);
                    if (stStateInfo.bChargeGet)
                    {
                        jsonPointInfo.insert(STR_ADU_BATTARY_PERCENT, stStateInfo.sCharge);
                        if (stStateInfo.sChargeLife >= 0)
                        {
                            jsonPointInfo.insert(STR_ADU_BATTARY_LIFE, stStateInfo.sChargeLife);
                        }
                    }
                }
                else//连接过，未失联，断开状态
                {
                    jsonPointInfo.insert(STR_ADU_BATTARY_STATE, BatteryVoltage_Unknow);
                }
            }
            //电源供电
            else if(ADU_MECH_IS == stInfo.eType || ADU_PD_THREE == stInfo.eType || ADU_PD_FIVE == stInfo.eType)
            {
                if ( stStateInfo.isOnLine() )
                {
                    jsonPointInfo.insert(STR_ADU_BATTARY_STATE, BatteryVoltage_High);
                    if((Monitor::LinkGroup)stInfo.stADUParam.eLinkGroup == Monitor::LINK_GROUP_LORA)
                    {
                        jsonPointInfo.insert(STR_ADU_LORA_SIGNAL_LEVEL, stStateInfo.sSignalLevel);
                        jsonPointInfo.insert(STR_ADU_LORA_SNR, stStateInfo.sNSR);
                    }
                }
                else
                {
                    jsonPointInfo.insert(STR_ADU_BATTARY_STATE, BatteryVoltage_Unknow);
                }
            }
            arrayData.append(jsonPointInfo);
        }
    }

    insert(STR_TOTAL, iCount);
    insert(STR_ADU_ONLINE_RATE,QString("%1/%2").arg(onlineADUCount).arg(listStateInfo.size()));
    insert(STR_ROWS, arrayData);
}

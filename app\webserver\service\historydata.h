/*
* Copyright (c) 2017.1，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：historydata.h
*
* 初始版本：1.0
* 作者：贺小强
* 创建日期：2017年1月6日
* 摘要：数据库查询页面接口，提供历史查询数据
*
*/

#ifndef HISTORYDATA_H
#define HISTORYDATA_H

#include <QJsonObject>
#include <QDateTime>

class HistoryData : public QJsonObject
{
public:
    /************************************************
    * 函数名:  AlarmData
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  构造函数
    ************************************************/
    HistoryData();

    /************************************************
    * 函数名:  monthHasData    daysHasData
    * 输入参数:  strChannel -- 通道名称
                timeStart -- 开始时间
                timeEnd -- 结束时间
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  给出有数据的时间列表
    ************************************************/
    void monthHasData(const QString& strChannel, const QDateTime& timeStart, const QDateTime& timeEnd);
    void daysHasData(const QString& strChannel, const QDateTime& timeStart, const QDateTime& timeEnd);

    /************************************************
    * 函数名:  recordsInDay
    * 输入参数:  strChannel -- 通道名称
                timeStart -- 开始时间
                timeEnd -- 结束时间
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取某天的数据记录
    ************************************************/
    void refreshRecordsInDay( const QString& strChannel, const QDateTime& timeStart, const QDateTime& timeEnd );

};

#endif // HISTORYDATA_H

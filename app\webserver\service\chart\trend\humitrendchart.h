#ifndef HUMITRENDCHART_H
#define HUMITRENDCHART_H
#include "itrendchart.h"
#include <QJsonArray>
#include <QVector>
#include "dbserver.h"


class HumiTrendChart : public ITrendChart
{
public:
    /**
    * @brief 构造函数
    * @param strPointID 测点ID
    * @param startTime 采样开始时间
    * @param endTime 采样结束时间
    * @param trendDataLimit 趋势图数据点数限制
    */
    HumiTrendChart(QString strPointID, QDateTime startTime, QDateTime endTime, int trendDataLimit = 100);

protected:
    /**
     * @brief 添加趋势图数据
     * @return 是否成功添加
     */
    bool addTrendChartData() override;

    /**
     * @brief 添加传感器最后一条数据信息
     * @return 是否成功添加
     */
    bool addLastSensorData() override;

private:
    /**
     * @brief 聚合湿度数据，将大量数据压缩到指定数量
     * @param trendDataList 原始趋势数据列表
     * @param xValues 输出的时间值数组
     * @param yValues 输出的湿度值数组
     */
    void aggregateHumidityData(const QVector<DBServer::TrendDataPoint>& trendDataList,
                               QJsonArray& xValues,
                               QJsonArray& yValues);
};

#endif // HUMITRENDCHART_H

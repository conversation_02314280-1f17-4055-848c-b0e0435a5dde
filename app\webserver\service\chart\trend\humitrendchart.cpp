#include "humitrendchart.h"
#include "webserver/commands.h"
#include <algorithm>
#include <cmath>

HumiTrendChart::HumiTrendChart(QString strPointID, QDateTime startTime, QDateTime endTime, int trendDataLimit):
    ITrendChart(strPointID, startTime, endTime, trendDataLimit)
{

}

bool HumiTrendChart::addTrendChartData()
{
    QVector<DBServer::TrendDataPoint> trendDataList;
    if(!DBServer::instance().getPointTrendData(m_strPointID, m_sampleStartTime, m_sampleEndTime, trendDataList))
    {
        logWarnning("HumiTrendChart::addTrendChartData getPointTrendData failed, pointid: ") << m_strPointID;
        return false;
    }

    QJsonArray xValues;
    QJsonArray yValues;
    QJsonObject dataChartObj;

    // 数据量判断和处理 
    if (trendDataList.size() <= m_trendDataLimit)
    {
        // 数据量小于等于限制，直接填充
        for (const auto& point : trendDataList)
        {
            xValues.append(point.timestamp.toString(STR_DATE_TIME_QSTRING_CNA));
            yValues.append(point.value);
        }
    }
    else
    {
        // 数据量大于限制，进行聚合处理
        aggregateHumidityData(trendDataList, xValues, yValues);
    }

    dataChartObj.insert(STR_X_VALUE, xValues);
    dataChartObj.insert("chartInit", yValues);
    dataChartObj.insert("chartMax", QJsonArray());
    dataChartObj.insert("chartAvg", QJsonArray());
    m_trendDataObj.insert(STR_DATA_CHART, dataChartObj);

    return true;
}

bool HumiTrendChart::addLastSensorData()
{
    bool bRet = false;
    HumidityRecord stHumidityRecord = DBServer::instance().lastHumidityRecord(m_strPointID, &bRet);
    if (!bRet)
    {
        logWarnning("HumiTrendChart::addLastSensorData lastHumidityRecord failed, pointid: ") << m_strPointID;
        return false;
    }

    m_trendDataObj.insert(STR_SAMPLE_TIME, stHumidityRecord.recordTime.toString(STR_DATE_TIME_QSTRING_CNA));
    m_trendDataObj.insert(STR_AMP, stHumidityRecord.humanity);

    m_trendDataObj.insert(STR_ADU_ID, stHumidityRecord.aduId);
    m_trendDataObj.insert(STR_UNIT, "%RH");
    m_trendDataObj.insert(STR_SENSOR_TYPE, "Humidity");

    return true;
}

void HumiTrendChart::aggregateHumidityData(const QVector<DBServer::TrendDataPoint>& trendDataList,
                                           QJsonArray& xValues,
                                           QJsonArray& yValues)
{
    const int dataSize = trendDataList.size();
    const int targetSize = m_trendDataLimit;

    // 计算每个聚合段的大小
    const double stepSize = static_cast<double>(dataSize) / targetSize;

    // 预分配数组大小以提高性能
    xValues = QJsonArray();
    yValues = QJsonArray();

    for (int i = 0; i < targetSize; ++i)
    {
        // 计算当前段的起始和结束索引
        const int startIdx = static_cast<int>(std::round(i * stepSize));
        const int endIdx = static_cast<int>(std::round((i + 1) * stepSize));

        // 确保索引不越界
        const int actualStartIdx = std::max(0, startIdx);
        const int actualEndIdx = std::min(dataSize, endIdx);

        if (actualStartIdx >= actualEndIdx)
        {
            continue; // 跳过无效段
        }

        // 在当前段中查找湿度最大值的点
        auto maxElementIter = std::max_element(
            trendDataList.begin() + actualStartIdx,
            trendDataList.begin() + actualEndIdx,
            [](const DBServer::TrendDataPoint& a, const DBServer::TrendDataPoint& b) {
                return a.value < b.value;
            }
        );

        // 添加最大值点的时间和湿度
        if (maxElementIter != trendDataList.begin() + actualEndIdx)
        {
            xValues.append(maxElementIter->timestamp.toString(STR_DATE_TIME_QSTRING_CNA));
            yValues.append(maxElementIter->value);
        }
    }
}


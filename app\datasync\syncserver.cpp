#include "syncserver.h"
#include <QTimer>
#include "syncmanager.h"
#include "serialize.h"
#include "configservice.h"
#include "webserver/commandfuns.h"
#include "patroldefines.h"
#include "log.h"

using namespace storage;

// 连接密钥
QString ConnectionCipher = "NJHC_S1010_S500";
// 源通讯地址
static quint32 AddressSrc = 1010;
// 目标通讯地址
static quint32 AddressDst = 500;
// 超时时间
const uint  ConnOverTime = 5000;
const uint  ConfigOverTime = 10000;
const uint  DbDataOverTime = 10000;

const uint PerSendSize = 8 * 1024;

/*************************************************
函数名： SyncServer
输入参数： pMain--同步管理器
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
SyncServer::SyncServer(SyncManager *pMain)
    : m_pMain(pMain)
{
    m_bConnected = false;
    m_bStopSync = false;
    m_pLocalFile = NULL;

    m_ptParam.eProtocolType = PROTOCOL_TYPE_LOW;                        //协议类型
    m_ptParam.uiMaxFrameLength = 1024*4;                                //最大帧数据长度
    m_ptParam.ullAddressSrc = AddressSrc;                               //源地址
    m_ptParam.ullAddressDst = AddressDst;                               //目标地址
    m_ptParam.eEncryptFlag = ENCRYPT_NO;                                //不加密
    m_ptParam.eCompressFlag = COMPRESS_NO;                              //不压缩
    m_ptParam.eDataFlag = DATA_FLAG_REQ;                                //数据报文
    m_ptParam.eRetransmitType = RETRANSMIT_TYPE_FRAME;                  //单帧重传
    m_ptParam.uiRetransmitTimes = 3;                                    //重传次数
    m_ptParam.uiConfirmationTimeout = 1000;                             //确认数据
    m_ptParam.uiResponseTimeout = 2000;                                 //应答超时

    connect(this, SIGNAL(sigProcDbData()), this, SLOT(onProcDbData()));
    connect(this, SIGNAL(sigSendFile()), this, SLOT(sendDbFile()));

    m_thread.start();
    moveToThread(&m_thread);
}

/*************************************************
函数名： ~SyncServer
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 析构函数
*************************************************************/
SyncServer::~SyncServer()
{
    m_thread.quit();
    m_thread.wait();
}

void SyncServer::onProcDbData()
{
    if ( !m_dataBuff.empty() )
    {
        procDbFileHeadRequest(m_dataBuff.first());
        m_dataBuff.pop_front();
    }
}

/*************************************************
函数名： getCmdType
输入参数： data--数据内容
输出参数： NULL
返回值： 命令字
功能： 获取命令字类型
*************************************************************/
CommandType SyncServer::getCmdType(const QByteArray &data)
{
    if ( data.size() < int(2 * sizeof(int)))
    {
        return CMD_INVALID;
    }

    int nCmdType = 0;
    int nLen = 0;

    QByteArray baTmp = data;
    QDataStream in(&baTmp, QIODevice::ReadOnly);
    in >> nCmdType >> nLen;

    CommandType eCmdType;
    if ( nCmdType<CMD_CONN_REQ || nCmdType>=CMD_NUM_TOTAL )
    {
        eCmdType = CMD_INVALID;
    }
    else if ( nLen != int(data.size() - 2 * sizeof(int)) )
    {
        eCmdType = CMD_INVALID;
    }
    else
    {
        eCmdType = (CommandType)nCmdType;
    }

    return eCmdType;
}

/************************************************
 * 函数名:  procConnectRequest
 * 输入参数:  data--连接请求内容
 * 输出参数:  NULL
 * 返回值:  处理结果--true表示成功
 * 功能:   处理连接请求
 ************************************************/
bool SyncServer::procConnectRequest(const QByteArray &data)
{
    outputMsg("begin to procConnectRequest...");
    if ( ConnectionCipher == m_dataProcesser.parseConnReq(data) )
    {
        m_bConnected = true;
    }
    else
    {
        m_bConnected = false;
    }

    // 连接应答
    QByteArray baData;

    ConfigService::instance().getStationID(m_stationInfo.stationCode);
    ConfigService::instance().getStationName(m_stationInfo.stationName);
    ConnReply reply;
    reply.bAgree = m_bConnected;
    reply.stationCode = m_stationInfo.stationCode;
    reply.stationName = m_stationInfo.stationName;
    reply.hostCode = monitorID();

    m_dataProcesser.packConnRep(baData, reply);
    if ( !m_pMain->sendData(baData, m_ptParam) )
    {
        outputMsg("Fail to sendData");
        emit m_pMain->sigException(Send_Error);
    }

    outputMsg("finish to procConnectRequest, return " + reply.hostCode);

    return true;
}

/************************************************
 * 函数名:  procConfigRequest
 * 输入参数:  data--配置请求内容
 * 输出参数:  NULL
 * 返回值:  处理结果--true表示成功
 * 功能:   处理配置请求
 ************************************************/
bool SyncServer::procConfigRequest(const QByteArray &data)
{
    outputMsg("begin to procConfigRequest...");
    QString stationCode = m_dataProcesser.parseConfigReq(data);
    bool bFlag = (stationCode == m_stationInfo.stationCode);

    QByteArray baConfig = ConfigService::instance().getConfigFile();
    ConfigNode cNode;
    if ( bFlag )
    {
        cNode.state = StateOk;
        //获取配置文件信息
        StationNode sNode = ConfigService::instance().stationNode();
        QList<ADUUnitInfo> lstADU = ConfigService::instance().ADUList();

        cNode.strCode = monitorID();
        cNode.stationNode = sNode;
        cNode.adus = lstADU;
        cNode.nGroup = ConfigService::instance().monitorConnectionGroup();
    }
    else
    {
        cNode.state = WrongStationCode;
    }

    QByteArray baData;
    //m_dataProcesser.packConfigRep(baData, cNode);
    m_dataProcesser.packConfigRep(baData, cNode, baConfig);

    if ( !m_pMain->sendData(baData, m_ptParam) )
    {
        outputMsg("Fail to sendData.");
        emit m_pMain->sigException(Send_Error);
        return false;
    }

    return true;
}

/************************************************
 * 函数名:  procDbInfoRequest
 * 输入参数:  data--请求内容
 * 输出参数:  NULL
 * 返回值:  处理结果--true表示成功
 * 功能:   处理数据信息请求
 ************************************************/
bool SyncServer::procDbInfoRequest(const QByteArray &data)
{
    outputMsg("begin to procDbInfoRequest...");
    m_syncTotal = 0;
    m_failNum = 0;
    m_bStopSync = false;
    // 获取请求列表
    QMap<QString, QList<ADUChannelType> > reqDbIndex;
    QDateTime dtStart, dtEnd;
    m_dataProcesser.parseSyncInfoReq(data, reqDbIndex, dtStart, dtEnd);

    outputMsg( "reqDbIndex size is " + QString::number(reqDbIndex.size()));
    qDebug() << "dbTime: " << dtStart << "~~" << dtEnd;

    QMap<QString, QList<ADUChannelType> >::iterator it = reqDbIndex.begin();

    // <测点，<类型，所有序号>>
    QMap<QString, QMap<ADUChannelType, QList<int> > > reply;
    while ( it != reqDbIndex.end() )
    {
        QMap<ADUChannelType, QList<int> > typeIds;
        for ( int i=0; i<it.value().size(); ++i )
        {
            QList<int> lstAutoId;
            DBServer::instance().getAutoIdInfo(it.value().at(i), it.key(), lstAutoId,
                                               dtStart, dtEnd);
            if ( !lstAutoId.empty() )
            {
                typeIds[it.value().at(i)] = lstAutoId;
            }
        }

        if ( !typeIds.empty() )
        {
            reply[it.key()] = typeIds;
        }
        else
        {
            outputMsg(it.key() + " has no data.");
        }

        it++;
    }

    outputMsg("db size is " + QString::number(reply.size()));

    QByteArray baReply;
    m_dataProcesser.packSyncInfoRep(baReply, reply);
    if ( !m_pMain->sendData(baReply, m_ptParam) )
    {
        outputMsg("Fail to sendData.");
        emit m_pMain->sigException(Send_Error);
        return false;
    }

    outputMsg("finish to procDbInfoRequest...");
    return true;
}

/************************************************
 * 函数名:  procDbDataRequest
 * 输入参数:  data--请求内容
 * 输出参数:  NULL
 * 返回值:  处理结果--true表示成功
 * 功能:   处理取数据请求
 ************************************************/
//bool SyncServer::procDbDataRequest(const QByteArray &data)
//{
//    logDebug("begin to procDbDataRequest...");
//    // 解析取数请求
//    QString strPointId;
//    ADUChannelType eAduType;
//    QList<int> lstId;
//    m_dataProcesser.parseSyncDataReq(data, strPointId, eAduType, lstId);

//    m_syncTotal += lstId.size();

//    // 取数据记录
//    QByteArray baReply;
//    TbData tbData;
//    // z058不管总条数和序号
//    tbData.channelType = eAduType;

//    if ( eAduType == CHANNEL_AE )
//    {
//        for ( int i=0; i<lstId.size(); ++i )
//        {
//            if ( m_bStopSync )
//            {
//                qDebug() << "Stop Sync Data.";
//                break;
//            }

//            outputMsg("get ae data:" + strPointId + "  " + QString::number(lstId.at(i)));
//            tbData.aeData = DBServer::instance().getAERecord(strPointId, lstId.at(i));
//            m_dataProcesser.packSyncDataRep(baReply, tbData);
//            qDebug() << baReply.size();
//            if ( !m_pMain->sendData(baReply, m_ptParam) )
//            {
//                outputMsg("Fail to sendData.");
//                m_failNum++;
//                emit m_pMain->sigException(Send_Error);
//            }

//            QThread::msleep(20);
//        }
//    }
//    else if ( eAduType == CHANNEL_TEV )
//    {
//        for ( int i=0; i<lstId.size(); ++i )
//        {
//            if ( m_bStopSync )
//            {
//                qDebug() << "Stop Sync Data.";
//                break;
//            }

//            tbData.tevData = DBServer::instance().getTEVRecord(strPointId, lstId.at(i));
//            qDebug() << "get tev data:" << strPointId << " id=" << lstId.at(i);
//            qDebug() << "before: " << "autoid=" << tbData.tevData.iautoId << " gid=" << tbData.tevData.illGlobalId << " reid=" << tbData.tevData.strRecordID
//                     << " channelId=" << tbData.tevData.ucChannelid;
//            m_dataProcesser.packSyncDataRep(baReply, tbData);

//            if ( !m_pMain->sendData(baReply, m_ptParam) )
//            {
//                qDebug() << "Fail to sendData of tev.";
//                m_failNum++;
//                emit m_pMain->sigException(Send_Error);
//            }

//            QThread::msleep(20);
//        }
//    }
//    else if ( eAduType == CHANNEL_UHF )
//    {
//        for ( int i=0; i<lstId.size(); ++i )
//        {
//            if ( m_bStopSync )
//            {
//                qDebug() << "Stop Sync Data.";
//                break;
//            }

//            tbData.uhfData = DBServer::instance().getUHFRecord(strPointId, lstId.at(i));
//            m_dataProcesser.packSyncDataRep(baReply, tbData);

//            if ( !m_pMain->sendData(baReply, m_ptParam) )
//            {
//                qDebug() << "Fail to sendData of uhf.";
//                m_failNum++;
//                emit m_pMain->sigException(Send_Error);
//            }

//            QThread::msleep(20);
//        }
//    }
//    else if ( eAduType == CHANNEL_HFCT )
//    {
//        for ( int i=0; i<lstId.size(); ++i )
//        {
//            if ( m_bStopSync )
//            {
//                qDebug() << "Stop Sync Data.";
//                break;
//            }

//            tbData.uhfData = DBServer::instance().getHFCTRecord(strPointId, lstId.at(i));
//            m_dataProcesser.packSyncDataRep(baReply, tbData);

//            if ( !m_pMain->sendData(baReply, m_ptParam) )
//            {
//                qDebug() << "Fail to sendData of hfct.";
//                m_failNum++;
//                emit m_pMain->sigException(Send_Error);
//            }

//            QThread::msleep(20);
//        }
//    }

//    qDebug() << "sync total: " << m_syncTotal << "  fail num:" << m_failNum;
//    outputMsg("finish to procDbDataRequest");
//    return true;
//}

/************************************************
 * 函数名:  procDbFileHeadRequest
 * 输入参数:  data--请求内容
 * 输出参数:  NULL
 * 返回值:  处理结果--true表示成功
 * 功能:   处理数据文件头请求
 ************************************************/
bool SyncServer::procDbFileHeadRequest(const QByteArray &data)
{
    QString strPointId;
    ADUChannelType eChannelType;
    QList<int> lstStart, lstEnd;

    QByteArray baTmp = data.mid(8);
    QDataStream in(&baTmp, QIODevice::ReadOnly);
    in >> strPointId >> eChannelType >> lstStart >> lstEnd;

    // 首先关闭当前已打开的文件！！
    closeFile();

    bool bFlag = true;
    QString strFileName;

    // 提取出数据文件
    if ( !DBServer::instance().exportData(strPointId, eChannelType, lstStart, lstEnd) )
    {
        QString strInfo = QString("Fail to exportData: %1 %2").arg(strPointId).arg(eChannelType);
        logError(strInfo);
        bFlag = false;
        //return false;
    }
    else
    {
        // 获取文件名
        QDir qdir(g_strExportDataBaseDir);
        QStringList lstTmp = qdir.entryList(QDir::Files);
        if ( lstTmp.size() != 1 )
        {
            QString strInfo = g_strExportDataBaseDir + QString(" files num is %1.").arg(lstTmp.size());
            logError( strInfo );
            bFlag = false;
            //return false;
        }

        strFileName = lstTmp.at(0);
        QString strFileFullPath = g_strExportDataBaseDir + "/" + strFileName;
        //qDebug() << strFileFullPath;

        // 发送文件头信息
        m_pLocalFile = new QFile( strFileFullPath );
        if ( !m_pLocalFile->open( QFile::ReadOnly ) )
        {
            // S500根据超时判断同步失败
            logError("Fail to open " + strFileFullPath);
            bFlag = false;
            //return false;
        }
    }

    //qDebug() << "begin to send db file head info...";

    QByteArray baSend;
    QDataStream out(&baSend, QIODevice::WriteOnly);
    int nLen = 0;
    // 该文件总大小及文件名
    if ( bFlag )
    {
        m_nTotalSize = m_pLocalFile->size();
    }
    else
    {
        m_nTotalSize = 0;
        strFileName.clear();
    }
    out << CMD_SYNC_DBFILE_HEAD_REP << nLen << m_nTotalSize << strFileName;
    nLen = baSend.size() - 2 * sizeof(int);
    out.device()->seek(4);
    out << nLen;
    out.device()->close();

    if ( !m_pMain->sendData(baSend, m_ptParam) )
    {
        logError("Fail to send stopMsg.");
        return false;
    }

    return true;
}

bool SyncServer::procDbFileContentRequest(const QByteArray &data)
{
    Q_UNUSED(data)
    emit sigSendFile();
    return true;
}

/*************************************************
函数名： sendDbFile
输入参数： NULL
输出参数： NULL
返回值： 结果状态--true表示文件发送成功
功能： 发送数据库文件
*************************************************************/
bool SyncServer::sendDbFile()
{
    if ( m_pLocalFile == NULL )
    {
        logError("m_pLocalFile is NULL.");
        return false;
    }

    logDebug("begin to sendDbFile.....");

    quint64 nSend = 0;

    QByteArray baTmp;
    while ( !(baTmp = m_pLocalFile->read(PerSendSize)).isEmpty() )
    {
        if ( m_bStopSync )
        {
            logDebug("sendDbFile has been stopped.");
            break;
        }

        nSend += baTmp.size();

        QByteArray baSend;
        m_dataProcesser.packSyncFileContent(baSend, baTmp);

        if ( !m_pMain->sendData(baSend, m_ptParam) )
        {
            logError(QString("Fail to send file [%1].").arg(nSend));
            break;
        }

        QThread::msleep(5);
    }

    // 关闭文件
    m_pLocalFile->close();
    delete m_pLocalFile;
    m_pLocalFile = NULL;

    if ( nSend != m_nTotalSize )
    {
        //qDebug() << "Fail to send file: total=" << m_nTotalSize << " send=" << nSend;
        logError(QString("Fail to send file: total=%1, send=%2").arg(m_nTotalSize).arg(nSend));
        return false;
    }

    return true;
}

void SyncServer::addToBuffer(const QByteArray &data)
{
    //qDebug() << "------addToBuffer-------";
    if ( !m_bStopSync )
    {
        m_dataBuff.push_back(data);
        emit sigProcDbData();
    }
}

/************************************************
 * 函数名:  stopSync
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:   设置停止标识为true
 ************************************************/
void SyncServer::stopSync()
{
    m_bStopSync = true;
    m_dataBuff.clear();
    logDebug("begin to stop sync....");
}

/************************************************
 * 函数名:  getPrtcParam
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  通讯协议参数
 * 功能: 获取通讯协议参数
 ************************************************/
ProtocolParam SyncServer::getPrtcParam()
{
    return m_ptParam;
}

/************************************************
 * 函数名:  closeFile
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能: 关闭当前已打开的文件
 ************************************************/
void SyncServer::closeFile()
{
    if ( m_pLocalFile != NULL )
    {
        m_pLocalFile->close();
        delete m_pLocalFile;
        m_pLocalFile = NULL;
    }
}

#include "aeflychartdatagenerator.h"
#include "chartjsonconstants.h"
#include "configservice.h"
#include "log.h"

AeFlyChartDataGenerator::AeFlyChartDataGenerator(const AERecord& aeRecord)
    : m_aeRecord(aeRecord)
{
}

QJsonObject AeFlyChartDataGenerator::generateChartData()
{
    QJsonObject chartJson;

    chartJson.insert(ChartJsonConstants::kTitleKey, "");

    chartJson.insert(ChartJsonConstants::kTriggerKey, generateTriggerInfo());
    chartJson.insert(ChartJsonConstants::kAxisInfoKey, generateAxisInfo());
    chartJson.insert(ChartJsonConstants::kSeriesKey, generateSeriesData());

    return chartJson;
}

QJsonObject AeFlyChartDataGenerator::generateAxisInfo()
{
    QJsonObject axisInfo;

    axisInfo.insert(ChartJsonConstants::kXDes<PERSON><PERSON><PERSON>, "");

    axisInfo.insert(ChartJsonConstants::kXRangeMinKey, 0);
    if(m_aeRecord.maxIntervalTime <= 0)
    {
        axisInfo.insert(ChartJsonConstants::kXRangeMaxKey, 1);
    }
    else
    {
        axisInfo.insert(ChartJsonConstants::kXRangeMaxKey, m_aeRecord.maxIntervalTime * 1000);
    }

    axisInfo.insert(ChartJsonConstants::kXUnitKey, getPulseIntervalUnitString(m_aeRecord.ePulseIntervalUnit));

    axisInfo.insert(ChartJsonConstants::kYDescKey, ChartJsonConstants::kAeAmplitudeDesc);
    axisInfo.insert(ChartJsonConstants::kYRangeMinKey, m_aeRecord.fAmpLowerLimit);
    axisInfo.insert(ChartJsonConstants::kYRangeMaxKey, m_aeRecord.fAmpUpperLimit);
    axisInfo.insert(ChartJsonConstants::kYUnitKey, ConfigService::instance().getDataUnitString(m_aeRecord.eAmpUnit));

    return axisInfo;
}

QJsonObject AeFlyChartDataGenerator::generateTriggerInfo()
{
    QJsonObject trigger;

    trigger.insert(ChartJsonConstants::kDescKey, ChartJsonConstants::kAeTriggerDesc);
    trigger.insert(ChartJsonConstants::kValueKey, m_aeRecord.triggerAmplitude);
    trigger.insert(ChartJsonConstants::kColorKey, "");

    return trigger;
}

QJsonArray AeFlyChartDataGenerator::generateSeriesData()  
{
    QJsonArray seriesArray;
    QJsonObject seriesObject;
    QJsonArray dataList;

    const int dataSize = m_aeRecord.flyArrayData.size();
    if (dataSize % 2 != 0)
    {
        logError(QString("generateAeFlySeriesData: dataSize error: %1").arg(dataSize));
        return seriesArray;
    }

    for (int i = 0; i < dataSize; i += 2)
    {
        float time = m_aeRecord.flyArrayData[i];
        float amplitude = m_aeRecord.flyArrayData[i + 1];

        QJsonArray pointArray;
        pointArray.append(time);
        pointArray.append(amplitude);

        // logTrace(QString("generateAeFlySeriesData: pointArray: %1, %2").arg(time).arg(amplitude));
        
        dataList.append(pointArray);
    }

    seriesObject.insert(ChartJsonConstants::kDataListKey, dataList);
    seriesArray.append(seriesObject);

    return seriesArray;
}

/**
 * @brief 将脉冲间隔单位枚举转换为字符串
 * @param unit 脉冲间隔单位枚举值
 * @return 对应的单位字符串
 */
QString AeFlyChartDataGenerator::getPulseIntervalUnitString(AERecord::PulseIntervalUnit unit)
{
    switch (unit)
    {
    case AERecord::PulseIntervalUnit::PULSEINTERVALUNIT_US:
        return QStringLiteral("μs");
    case AERecord::PulseIntervalUnit::PULSEINTERVALUNIT_MS:
        return QStringLiteral("ms"); 
    case AERecord::PulseIntervalUnit::PULSEINTERVALUNIT_S:
        return QStringLiteral("s");
    case AERecord::PulseIntervalUnit::PULSEINTERVALUNIT_INVALID:
    default:
        return QString();
    }
}

#ifndef ITRENDCHART_H
#define ITRENDCHART_H
#include <QString>
#include <QJsonObject>
#include <QDateTime>

class ITrendChart
{
public:
    /**
    * @brief 构造函数
    * @param strPointID 测点ID
    * @param startTime 采样开始时间
    * @param endTime 采样结束时间
    * @param trendDataLimit 趋势图数据点数限制
    */
    ITrendChart(QString strPointID, QDateTime startTime, QDateTime endTime, int trendDataLimit = 100);

    /**
    * @brief 析构函数
    */
    virtual ~ITrendChart() {}

    /**
    * @brief 获取趋势数据
    * @return 趋势数据
    */
    QJsonObject getTrendData();

protected:
    /**
    * @brief 添加趋势图数据
    */
    virtual bool addTrendChartData() = 0;

private:
    /**
    * @brief 添加测点信息
    */
    bool addPointInfo();

    /**
    * @brief 添加传感器最后一条数据信息
    */
    virtual bool addLastSensorData() = 0;

protected:
    QJsonObject m_trendDataObj; // 待返回的趋势数据

    QString m_strPointID;       // 测点ID
    QDateTime m_sampleStartTime; // 采样开始时间
    QDateTime m_sampleEndTime;   // 采样结束时间
    int m_trendDataLimit {100}; //趋势图数据点数限制
};

#endif // ITRENDCHART_H

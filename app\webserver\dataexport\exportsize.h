/*
* Copyright (c) 2016.12，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：exportsize.h
*
* 初始版本：1.0
* 作者：xw
* 创建日期：2021年2月2日
* 摘要：计算导出数据的大小以及完成数据压缩
*
*/

#ifndef EXPORTSIZE_H
#define EXPORTSIZE_H

#include <atomic>

#include <QObject>
#include <QThread>
#include <QTimer>
#include <QSharedPointer>

class QProcess;

class ExportSize : public QObject
{
    Q_OBJECT
public:
    explicit ExportSize(QObject *parent = 0);

    ~ExportSize();

    /*************************************************
    输出参数： listDir 备份数据目录列表
    返回值： 数据大小（M）
    功能: 获取需要备份数据大小
    *************************************************************/
    int getDataSize(const QList<QString> &listDir);

    /*************************************************
    输出参数： listDir 备份数据目录列表
    功能: 压缩数据
    *************************************************************/
    void compress(const QList<QString> &listDir);

    /*************************************************
    功能: 取消获取数据大小和压缩命令
    *************************************************************/
    void cancel();

    /*************************************************
    功能: 获取压缩文件名和路径
    *************************************************************/
    inline QString getCompressFileName(){return m_strCompressFileName;}
signals:

public slots:
    void onReadData();

private:
    QSharedPointer<QProcess> m_spProcess;
    std::atomic<int> m_iDataSize; // 压缩数据大小
    std::atomic<bool>  m_bGetData;  // 是否正在获取数据大小
    std::atomic<bool>  m_bCompress; // 是否正在压缩
    std::atomic<bool> m_bCancel; // 导出操作是否被取消

private:
    QString m_strCompressFileName; // 压缩文件名和路径
};

#endif // EXPORTSIZE_H

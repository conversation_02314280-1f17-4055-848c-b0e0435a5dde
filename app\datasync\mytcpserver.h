/*
* Copyright (c) 2016.11，南京华乘电气科技有限公司
* All rights reserved.
*
* TcpServer.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年2月25日
* 摘要：TcpServer组件

* 当前版本：1.0
*/

#ifndef TCPSERVER_H
#define TCPSERVER_H
#include "abstractcomm.h"

#include <QTcpSocket>
#include <QTcpServer>

class TcpServer :
    public AbstractComm
{
    Q_OBJECT
public:
    /*************************************************
    函数名： TcpClient(QString strServerIP = "*************", quint16 usSocketPort = 12345, QObject *parent = 0)
    输入参数： strServerIP：Server地址
              usSocketPort：端口号
              parent：父指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    explicit TcpServer(QString strServerIP = "127.0.0.1", quint16 usSocketPort = 12345, QObject *parent = 0);

    /*************************************************
    功能： 析构函数
    *************************************************************/
    ~TcpServer();

    /*************************************************
    函数名： open()
    输入参数： NULL
    输出参数： NULL
    返回值： 操作结果
    功能： 打开操作
    *************************************************************/
    virtual bool open();

    /*************************************************
    函数名： close()
    输入参数： NULL
    输出参数： NULL
    返回值： 操作结果
    功能： 关闭操作
    *************************************************************/
    virtual bool close();

    /*************************************************
    函数名： read(char *pRecvBuf, unsigned int uiWantLen)
    输入参数： uiWantLen：期望读取长度，单位为字节
    输出参数： pRecvBuf：接收缓冲
    返回值： 操作结果，-1 - 操作失败，其它值 - 实际读取长度
    功能： 读取操作
    *************************************************************/
    virtual int read(char *pRecvBuf, unsigned int uiWantLen);

    /*************************************************
    函数名： read(unsigned int uiWantLen)
    输入参数： uiWantLen：期望读取长度，单位为字节
    输出参数： NULL
    返回值： 读取到的数据
    功能： 读取操作
    *************************************************************/
    virtual QByteArray read(unsigned int uiWantLen);

    /*************************************************
    函数名： write(const char *pSendBuf, unsigned int uiWantLen)
    输入参数： pSendBuf：发送缓冲
              uiWantLen：期望写入长度，单位为字节
    输出参数： NULL
    返回值： 操作结果，-1 - 操作失败，其它值 - 实际写入长度
    功能： 写入操作
    *************************************************************/
    virtual int write(const char *pSendBuf, unsigned int uiWantLen);

    /*************************************************
    函数名： write(const QByteArray &baSendData)
    输入参数： baSendData：待发送数据
    输出参数： NULL
    返回值： 操作结果，-1 - 操作失败，其它值 - 实际写入长度
    功能： 写入操作
    *************************************************************/
    virtual int write(const QByteArray &baSendData);

private slots:
    /*************************************************
    函数名： onReadyRead()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 响应Socket有可读取的数据信号
    *************************************************************/
    void onReadyRead();

    /*************************************************
    函数名： onConnected()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 响应Socket连接上信号
    *************************************************************/
    void onConnected();

    /*************************************************
    函数名： onDisconnected()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 响应Socket断开连接信号
    *************************************************************/
    void onDisconnected();

    /*************************************************
    函数名： onError(QAbstractSocket::SocketError eCode)
    输入参数： eCode：错误码
    输出参数： NULL
    返回值： NULL
    功能： 响应Socket连接错误信号
    *************************************************************/
    void onError(QAbstractSocket::SocketError eCode);

    /*************************************************
    功能： new connection 槽函数---获取建立的tcp连接
    *************************************************************/
    void onGetNewConnection();
private:
    //错误码定义
    typedef enum _ErrorCode
    {
        ERROR_NONE = 0,
        LISTEN_FAILED = -1,//监听失败
        REMOTE_HOST_CLOSED = -2,
        CONNECT_REFUSED = -3,
        HOST_NOT_FOUND = -4,
        OTHER_ERROR = -255//其它错误
    }ErrorCode;
private:
    /*************************************************
    返回值： true -- 成功
            false -- 失败
    功能： 开启监听
    *************************************************************/
    bool startListen( void );

    /****************************
    功能： 根据error code添加不同打印，方便调试
    输入参数:
        eErrorCode -- error类型
    *****************************/
    void error( ErrorCode eErrorCode );

    /*************************************************
    功能： 关闭Server
    *************************************************/
    void closeServer();

    /*************************************************
    功能： 判断是socket是否合法
    返回值：false -- 否
           true -- 是
    *************************************************/
//    bool isSocketValid( LinkState eLinkState );
private:
    QString m_strServerIP;  //Server地址
    quint16 m_usSocketPort;  //端口号
    bool m_bIsOpen;
    QTcpSocket *m_pSocket;
    QTcpServer *m_pServer;
};
#endif

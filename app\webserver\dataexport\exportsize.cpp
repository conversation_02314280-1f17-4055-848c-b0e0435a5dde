#include "exportsize.h"

#include <QProcess>
#include <QDir>
#include <QEventLoop>

#include "log.h"

ExportSize::ExportSize(QObject *parent) : QObject(parent),
    m_iDataSize(0),
    m_bGetData(false),
    m_bCompress(false),
    m_b<PERSON>ancel(false)
{
    m_strCompressFileName = "/media/backup/download/tmp/backUp.tar.gz";
    m_spProcess.reset(new QProcess(this));
    m_spProcess->setProcessChannelMode(QProcess::MergedChannels);
}

ExportSize::~ExportSize()
{

}

/*************************************************
输出参数： listDir 备份数据目录列表
返回值： 数据大小（M）
功能: 获取需要备份数据大小
*************************************************************/
int ExportSize::getDataSize(const QList<QString> &listDir)
{
    m_iDataSize = 0;
    m_bGetData = true;

    if(!listDir.size())
    {
        return m_iDataSize;
    }

    connect(m_spProcess.data(), SIGNAL(readyRead()), this, SLOT(onReadData()));
    connect(m_spProcess.data(), SIGNAL(readyReadStandardOutput()), this, SLOT(onReadData()));

    for(int i = 0; i < listDir.size(); ++i)
    {
        if(m_bCancel)
        {
            return 0;
        }
        QStringList args;
        args.append("-sh");
        args.append(listDir[i]);
        m_spProcess->start("du", args);
        m_spProcess->waitForFinished(120 * 1000); // 最大等待时长2分钟
    }
    m_bGetData = false;
    return m_iDataSize;
}

/*************************************************
输出参数： listDir 备份数据目录列表
功能: 压缩数据
*************************************************************/
void ExportSize::compress(const QList<QString> &listDir)
{
    m_bCompress = true;

    if(!listDir.size())
    {
        return ;
    }

    connect(m_spProcess.data(), SIGNAL(readyRead()), this, SLOT(onReadData()));
    connect(m_spProcess.data(), SIGNAL(readyReadStandardOutput()), this, SLOT(onReadData()));

    QStringList args;
    args.append("czvf");
    args.append(m_strCompressFileName);
    for(int i = 0; i < listDir.size(); ++i)
    {
        args.append(listDir[i]);
    }
    m_spProcess->start("tar", args);
    m_spProcess->waitForFinished(60 * 60 * 1000); // 最大等待时长1小时钟

    m_bCompress = false;
}

void ExportSize::onReadData()
{
    if(!m_bCompress && !m_bGetData)
    {
        infoLog() << "m_spProcess cloase";
        m_spProcess->close();
        return;
    }

    QByteArray bytes = m_spProcess->readAll();
    if(!bytes.size())
    {
        return;
    }
    QString msg = QString::fromLocal8Bit(bytes);

    if(m_bGetData)
    {
        int index = msg.indexOf('/');
        if(index > 0)
        {
            QString data = msg.left(index);
            infoLog() << "data = " << data;
            if(data.contains("M"))
            {
                m_iDataSize += atof(data.toLatin1().data());
            }
            else if(data.contains("G"))
            {
                m_iDataSize += (atof(data.toLatin1().data()) * 1024);
            }
            else if(data.contains("K"))
            {
                m_iDataSize += 1;  // 小文件，默认为1M
            }
            else
            {
                warningLog() << "get size fail" << msg;
            }
        }
        else
        {
            warningLog() << "get size fail" << msg;
        }
    }
    else if(m_bCompress)
    {
        warningLog() << "compressing";
    }
    else
    {
        warningLog() << "not export" << msg;
    }
}

/*************************************************
功能: 取消获取数据大小和压缩命令
*************************************************************/
void ExportSize::cancel()
{
    m_bCancel = true;
    m_spProcess->close();
}





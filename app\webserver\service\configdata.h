/*
* Copyright (c) 2017.1，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：devicetreedata.h
*
* 初始版本：1.0
* 作者：贺小强
* 创建日期：2017年1月6日
* 摘要：配置管理模块的接口，提供设备树数据,系统设置、modbus设置数据
*
*/

#ifndef DEVICETREEDATA_H
#define DEVICETREEDATA_H

#include <QJsonObject>
#include "configservice.h"
#include "commandfuns.h"
#include "websocket.h"
#include "usermanager.h"

class ConfigData: public QJsonObject
{
public:
    ConfigData();

    /************************************************
    * 函数名:  DeviceTreeData
    * 输入参数:  unDeviceID -- 设备id
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取设备树数据
    ************************************************/
    void DeviceTreeData(const QString &strDeviceID);

    /************************************************
    * 函数名:  SystemSettingData
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取系统设置数据
    ************************************************/
    void SystemSettingData(void);

    /************************************************
    * 函数名:  ModbusSettingData
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取modbus设置数据
    ************************************************/
    void ModbusSettingData(void);

    void StdModbusSettingData();

    void SF6AlarmConfigData(const int &iTaskGroup, const ADUChannelType &eChannelType);

    /************************************************
     * 输入参数：listCkData 审核数据列表
     * 功能：获取审核数据
     ************************************************/
    void getCheckList(const QList<safe::CheckData> &listCkData);

    /************************************************
     * 输入参数:  userInfo 普通用户列表信息
     * 功能:  获取普通用户列表信息
     ************************************************/
    void getNormalUser(const QList<safe::UserInfo> &listUserInfo);

    /*************************************************
    函数名： checkPassword
    输入参数： strPassword -- 用户输入的密码
            eUserName -- 用户名
    输出参数： NULL
    返回值： 密码正确与否
    功能： 验证密码
    *************************************************************/
    bool checkPassword(const QString &strPassword, const UserName &eUserName);

    /************************************************
     * 函数名:  syncData
     * 输入参数:  ADUList -- 前端列表
     *      eADUType -- 前端类型
     *      startTime -- 开始时间
     *      endTime -- 结束时间
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能: 数据同步前端
     ************************************************/
    bool syncData(const QStringList &ADUList, ADUType eADUType);

    /*************************************************
    函数名： revisePassword
    输入参数： strPassword -- 用户输入的密码
            eUserName -- 用户名
    输出参数： NULL
    返回值： 密码是否修改成功
    功能： 修改密码
    *************************************************************/
    bool revisePassword(const QString &strPassword, const UserName &eUserName);

    /************************************************
    * 函数名:  ADUVersionData
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能: 固件更新获取前端版本信息数据列表
    ************************************************/
    void ADUVersionData(void);

    /************************************************
    * 函数名:  channelList
    * 输入参数:  eChannelType -- 传感器类型
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取通道列表数据
    ************************************************/
    void getChannelList(ADUChannelType eChannelType);

    /************************************************
    * 函数名:  getStation
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取站点信息（站点名，站点SVG图路径）
    ************************************************/
    void getStation(void);

    /************************************************
    * 函数名:  getDeviceList
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取设备信息列表
    ************************************************/
    void getDeviceList(void);

    /************************************************
    * 函数名:  getDeviceNameList
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取设备名称列表
    ************************************************/
    void getDeviceNameList(void);

    /************************************************
    * 函数名:  getDeviceNameListApp
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取设备名称列表
    ************************************************/
    void getDeviceNameListApp(void);

    /************************************************
    * 函数名:  GetDeviceInfo
    * 输入参数:  ucDeviceID -- 设备ID
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取设备详情
    ************************************************/
    void GetDeviceInfo(const QString &strDeviceCode);

    /************************************************
    * 函数名:  GetADUInfo
    * 输入参数:  strADUID -- 前端id
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取前端信息
    ************************************************/
    void GetADUInfo(const QString &strADUID);

    /************************************************
    * 函数名:  getChannelInfo
    * 输入参数:  strADUID -- 前端id
    *           ucChannelID -- 通道索引
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取前端信息
    ************************************************/
    void getChannelInfo(const QString &strADUID, quint8 ucChannelID);

    /************************************************
    * 函数名:  GetADUTree
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取前端树信息
    ************************************************/
    void GetADUTree(void);

    /************************************************
     * 函数名:  GetLinkInfo
     * 输入参数:  request -- http请求
     *      response -- 响应
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:  获取某测点的关联信息
     ************************************************/
    bool GetLinkInfo(int iChannelId);

    /************************************************
     * 函数名:  GetADUVersionList
     * 输入参数:  NULL
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:  获取前端版本号信息
     ************************************************/
    QJsonArray GetADUVersionList(const QString &strADUType);

    /************************************************
     * 函数名:  getRelationInfo
     * 输入参数:  request -- http请求
     *      response -- 响应
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:  获取测点关联信息
     ************************************************/
    void getRelationInfo(const QString &strTestPointID);

    /************************************************
     * 函数名:  UpdateHostVersion
     * 输入参数:  NULL
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:  主机固件更新
     ************************************************/
    void UpdateHostVersion(void);

    /************************************************
     * 函数名:  SwitchUser
     * 输入参数:  strUserName -- 用户名
     *          strPsaaword -- 密码
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:  切换用户
     ************************************************/
    bool SwitchUser(const QString &strUserName, const QString &strPsaaword);

    /************************************************
     * 函数名:  SavaModbus
     * 输入参数:  jsonData -- 用户数据
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:  保存modbus信息参数
     ************************************************/
    void SavaModbus(const QJsonObject &jsonData);

    /************************************************
     * 函数名:  SyncPonintData
     * 输入参数:  jsonData -- 用户数据
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:  同步测点信息
     ************************************************/
    void SyncPonintData(const QJsonArray &jsonData);

    /*************************************************
    函数名： ChannelParaApplyAll
    输入参数： jsonData -- 用户数据
    输出参数： NULL
    返回值： NULL
    功能： 将单个通道参数应用于全部传感器
    *************************************************************/
    void ChannelParaApplyAll(const QJsonObject &jsonData);

    /************************************************
     * 函数名:  SystemSettingConfig
     * 输入参数:  NULL
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:  获取系统设置选项
     ************************************************/
    void SystemSettingConfig(void);

    /************************************************
     * 函数名:  ModbusSettingConfig
     * 输入参数:  NULL
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:  获取modbus信息参数
     ************************************************/
    void ModbusSettingConfig(void);

    void SF6AlarmConfigInfoPara(void);

    /************************************************
     * 函数名:  NetworkSetting
     * 输入参数:  NULL
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:  获取网络信息
     ************************************************/
    void NetworkSetting(void);

    /************************************************
     * 函数名:  NetworkSettingConfig
     * 输入参数:  NULL
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:  获取网络信息参数
     ************************************************/
    void NetworkSettingConfig(void);

    /************************************************
     * 函数名:  getChannelParaFromJson
     * 输入参数:  jsonData -- 用户数据
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:  从请求中读取通道参数
     ************************************************/
    bool getChannelParaFromJson(const QJsonObject &jsonData, Channelpara &staChannelPara);

    /************************************************
     * 函数名:  getMonitoringFormsInfo
     * 输入参数:  iPage -- 页码
     *            iSize -- 每页的条数
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:  获取检测表信息
     ************************************************/
    void getMonitoringFormsInfo(int iPage, int iSize);

    /************************************************
     * 函数名:  GetADUStateInfo
     * 输入参数:  iPage -- 页码
     *            iSize -- 每页的条数
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:  获取前端状态信息
     ************************************************/
    void GetADUStateInfo(int iPage, int iSize, QString aduID, QString isOnline);

    /************************************************
     * 函数名:  getADUConfig
     * 输入参数:  eADUType -- 前端类型
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:  获取前端参数
     ************************************************/
    void getADUConfig(ADUType eADUType);

    void getAlarmConfigList();

    /************************************************
     * 函数名:  getGroupNoRange
     * 输入参数:  iLoraFrequency -- 主机区域
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:  主机区域对应的组号范围
     ************************************************/
    void getGroupNoRange(int iLoraFrequency);

private:

    /************************************************
     * 函数名:  getChannelPara
     * 输入参数:  eChannelType -- 通道类型枚举
     * 输出参数:  NULL
     * 返回值:  NULL
     * 功能:  获取通道参数
     ************************************************/
    QJsonObject getChannelPara(ADUChannelType eChannelType, const Channelpara & stachpara);

    QStringList m_UpdateADUIDList;
    QByteArray m_UpdateADUFile;
    ADUType m_eaduType;
};

#endif // DEVICETREEDATA_H

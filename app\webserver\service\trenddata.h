/*
* Copyright (c) 2017.1，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：trenddata.h
*
* 初始版本：1.0
* 作者：贺小强
* 创建日期：2017年1月6日
* 摘要：趋势查询界面接口，提供趋势查询数据
*
*/

#ifndef TRENDDATA_H
#define TRENDDATA_H

#include <QJsonObject>
#include "devicetree.h"

#define	DEFAULT_TREND_LIMIT             (1000)                     //趋势数据限制

class TrendData : public QJsonObject
{
public:
    /************************************************
    * 函数名:  TrendData
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  构造函数
    ************************************************/
    TrendData();

    /************************************************
    * 函数名:  analyseDataType
    * 输入参数:  eChannelType -- 传感器类型
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取某类型传感器对应的趋势类型列表
    ************************************************/
    void getTrendDataInfo(ADUChannelType eChannelType);

    /************************************************
    * 函数名:  reFresh
    * 输入参数:  strChannel -- 通道名称（list）
                eType -- 趋势图类型
                timeStart -- 开始时间
                timeEnd -- 结束时间
                iLimit -- 趋势数据限制
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  根据条件刷新趋势数据
    ************************************************/
    void reFresh(const QString &strChannel, ADUChannelType eChannelType, const QDateTime &timeStart, const QDateTime &timeEnd,
                                int iLimit = DEFAULT_TREND_LIMIT);
};

#endif // TRENDDATA_H

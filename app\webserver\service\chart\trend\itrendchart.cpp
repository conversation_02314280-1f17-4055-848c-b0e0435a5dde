#include "itrendchart.h"
#include "configservice.h"
#include "webserver/commands.h"
#include "log.h"

ITrendChart::ITrendChart(QString strPointID, QDateTime startTime, QDateTime endTime, int trendDataLimit):
    m_strPointID(strPointID),
    m_sampleStartTime(startTime),
    m_sampleEndTime(endTime),
    m_trendDataLimit(trendDataLimit)
{

}

QJsonObject ITrendChart::getTrendData()
{
    if(!addPointInfo())
    {
        return QJsonObject();
    }

    if(!addLastSensorData())
    {
        return QJsonObject();
    }

    if(!addTrendChartData())
    {
        return QJsonObject();
    }

    return m_trendDataObj;
}

bool ITrendChart::addPointInfo()
{
    TestPointInfo stTestPoint;
    if(!ConfigService::instance().getTestPoint(m_strPointID, stTestPoint))
    {
        logWarnning("ITrendChart::addPointInfo getTestPoint false, pointid: ") << m_strPointID;
        return false;
    }

    m_trendDataObj.insert(STR_POINT_NAME, stTestPoint.strOutName);
    m_trendDataObj.insert(STR_SRART_DATE, m_sampleStartTime.toString(STR_DATE_TIME_QSTRING_CNA));
    m_trendDataObj.insert(STR_END_DATE, m_sampleEndTime.toString(STR_DATE_TIME_QSTRING_CNA));

    return true;
}

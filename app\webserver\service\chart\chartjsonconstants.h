#ifndef CHARTJSONCONSTANTS_H
#define CHARTJSONCONSTANTS_H

#include <QLatin1String>

/**
 * @brief 定义图表数据 JSON 中使用的常量字符串
 */
namespace ChartJsonConstants {

// --- General Keys (使用 const QLatin1String) ---
const QLatin1String kTitleKey("title");
const QLatin1String kAxisInfoKey("axisInfo");
const QLatin1String kTriggerKey("trigger");
const QLatin1String kSeriesKey("series");
const QLatin1String kDataListKey("dataList");
const QLatin1String kDescKey("desc");
const QLatin1String kValueKey("value");
const QLatin1String kColorKey("color");

// --- AxisInfo Keys
const QLatin1String kXDescKey("xDesc");
const QLatin1String kXRangeMaxKey("xRangeMax");
const QLatin1String kXRangeMinKey("xRangeMin");
const QLatin1String kXUnitKey("xUnit");
const QLatin1String kYDescKey("yDesc");
const QLatin1String kYRangeMaxKey("yRangeMax");
const QLatin1String kYRangeMinKey("yRangeMin");
const QLatin1String kYUnitKey("yUnit");

const QString kAeWaveformTitle = QStringLiteral("AEWaveform");
const QString kAeTimeDesc = QStringLiteral("Time");
const QString kAeAmplitudeDesc = QStringLiteral("Amplitude");
const QString kAeTriggerDesc = QStringLiteral("Trigger");
const QString kAeTimeUnit = QStringLiteral("T");
const QString kAePhaseDesc = QStringLiteral("Phase");
const QString kAePhaseUnit = QStringLiteral("°");


} // namespace ChartJsonConstants

#endif // CHARTJSONCONSTANTS_H

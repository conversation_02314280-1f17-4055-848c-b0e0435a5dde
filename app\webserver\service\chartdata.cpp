#include "chartdata.h"
#include <QColor>
#include "AE.h"
#include "commondefine.h"
#include "mechcompute.h"
#include "configservice.h"

#include "signalalgorithm.h"
#include "vibrationcompute.h"
#include "log.h"
#include "chart/aeflychartdatagenerator.h"
#include "chart/aewaveformchartdatagenerator.h"
#include "chart/aephasechartdatagenerator.h"

#define MECH_DATA_SHOW_DATA_COUNT 10000 //机械特性数据抽样间隔

#define ALL_DATA          "all"        //所有数据
#define NORMAL_DATA       "normal"     //正常数据
#define ALARM_DATA        "alarm"      //告警数据

namespace {
//环境趋势数据
struct TrendChartData
{
    QDateTime date;
    EnvStatus data;
};
typedef struct _arresterTrendData
{
    float fLeakageCurrent;
    float fResistiveCurrent;
}arresterTrendData;

enum class FilterType : int8_t
{
    kAllData = 0,
    kAlarmData = 1,
    kNormalData = 2,
};
static const std::map<QString, FilterType> kFilterStrMap =
{
    {ALL_DATA, FilterType::kAllData},
    {NORMAL_DATA, FilterType::kNormalData},
    {ALARM_DATA, FilterType::kAlarmData},
};
} // namespace

ChartData::ChartData()
{

}

bool ChartData::getPrpdFromPrps(const std::vector<std::vector<float> > &prps, float lowerBound, float upperBound, unsigned int ampNum, std::vector<std::vector<int> > &prpd)
{
    //PRPS数据校验
    if(prps.empty() or prps[0].empty())
    {
        prpd.clear();
        return false;
    }

    unsigned int iCycleNum = prps.size();
    unsigned int iPhaseNum = prps[0].size();

    if(((iCycleNum != 50)&&(iCycleNum != 60))
            ||(upperBound <= lowerBound)
            ||(0 == ampNum))
    {
        return false;
    }

    //每个幅值区间包含amp∈(Start,End]的脉冲，Start为起始幅值，End为截止幅值
    prpd.clear();
    prpd.resize(iPhaseNum, std::vector<int>(ampNum));

    float step = (upperBound-lowerBound)/static_cast<float>(ampNum);

    for (unsigned int i = 0; i < iCycleNum; i++)
    {
        for (unsigned int j = 0; j < iPhaseNum; j++)
        {
            if (prps[i][j] > lowerBound)
            {
                unsigned int ampIndex = static_cast<unsigned int>((prps[i][j]-lowerBound)/step);
                if (ampIndex < ampNum)
                {
                    prpd[j][ampIndex] += 1;
                }
                else
                {
                    prpd[j][ampNum-1] += 1;
                }
            }
        }
    }

    return true;

}

std::vector<std::vector<float> > ChartData::convertPrpsTo2D(std::vector<float> prps, int iTNum, int iPhaseNum)
{
    unsigned int cycleNum = static_cast<unsigned int>(iTNum);
    unsigned int phaseNum = static_cast<unsigned int>(iPhaseNum);
    std::vector<std::vector<float>> prps2D(cycleNum, std::vector<float>(phaseNum, 0.0f));

    for (unsigned int i = 0; i < cycleNum; i++)
    {
        for (unsigned int j = 0; j < phaseNum; j++)
        {
            prps2D[i][j] = static_cast<float>(prps[i * phaseNum + j]);
        }
    }

    return prps2D;
}

/************************************************
* 函数名:  getChartData
* 输入参数:  strPointId -- 测点类型
*           eChannelType -- 通道类型
*           iDataType -- 数据类型
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取指定图谱数据
************************************************/
void ChartData::getChartData(const QString &strPointId, ADUChannelType eChannelType, int iDataType, int iDataId)
{
    int iNextDataID = 0;
    int iPreDataID = 0;
    DBServer::instance().nearbyRecordId(strPointId, eChannelType, iDataId, iPreDataID, iNextDataID);
    insert("nextDataId", iNextDataID);
    insert("preDataId", iPreDataID);

    switch (eChannelType)
    {
    case CHANNEL_AE://AE参数
    {
        // getVideoData(DBServer::instance().getHkVideoRecord(strPointId, iDataId));
        getAEData(DBServer::instance().getAERecord(strPointId, iDataId));
    }
        break;
    case CHANNEL_TEV://TEV参数
    {
    }
        break;
    case CHANNEL_UHF:
    {
        if (iDataType == 0)
        {
            getPRPSData(CHANNEL_UHF, DBServer::instance().getUHFRecord(strPointId, iDataId));
        }
        else if (iDataType == 1)
        {
            getPRPDData(CHANNEL_UHF, DBServer::instance().getUHFRecord(strPointId, iDataId));
        }
    }
        break;
    case CHANNEL_TEVPRPS:
    {
        if (iDataType == 0)
        {
            getPRPSData(CHANNEL_TEVPRPS, DBServer::instance().getTEVPRPSRecord(strPointId, iDataId));
        }
        else if (iDataType == 1)
        {
            getPRPDData(CHANNEL_TEVPRPS, DBServer::instance().getTEVPRPSRecord(strPointId, iDataId));
        }
    }
        break;
    case CHANNEL_HIKVIDEO:
        videoData(DBServer::instance().getHkVideoRecord(strPointId, iDataId));
        break;
    case CHANNEL_HFCT:
    {
        if (iDataType == 0)
        {
            getPRPSData(CHANNEL_HFCT, DBServer::instance().getHFCTRecord(strPointId, iDataId));
        }
        else if (iDataType == 1)
        {
            getPRPDData(CHANNEL_HFCT, DBServer::instance().getHFCTRecord(strPointId, iDataId));
        }
        else
        {
        }
    }
        break;
    case CHANNEL_MECH:
    {
        MechDefine::MechISRecord stMechData;
        DBServer::instance().getMechRecord(strPointId, iDataId, stMechData);

        //----for test 重新计算相关参数----
        //MechCompute::computeMechPara(stMechData);
        //----

        if (iDataType == 2)
        {
            getMechLoopData( stMechData );
        }
        else if (iDataType == 3)
        {
            getMechMechData( stMechData );
        }
        else if (iDataType == 4)
        {
            getMechOriginalData( stMechData );
        }
        else
        {
        }
    }
        break;
    case CHANNEL_ARRESTER_I://避雷器
    case CHANNEL_GROUNDDINGCURRENT:
    case CHANNEL_LEAKAGECURRENT:
    case CHANNEL_ARRESTER_U://避雷器
    {
        //        getArresterParamData(DBServer::instance().getArresterRecord(strPointId, iDataId));
    }
        break;
    case CHANNEL_VIBRATION://振动
    {
        switch (iDataType)
        {
        case 1:
        case 2:
        case 3:
//            getVibrationTimeDomainData(DBServer::instance().getVibrationRecord(strPointId, iDataId), iDataType );
            getVibrationTimeDomainData(strPointId, iDataType);
            break;
        case 4:
        case 5:
        case 6:
            getVibrationFrequencyDomainData(DBServer::instance().getVibrationRecord(strPointId, iDataId), iDataType );
            break;
        default:
            break;
        }
//        packageVibrationData(strPointId);
    }
        break;
    default:
        return;
        break;
    }

}

/************************************************
* 函数名:  getChartData
* 输入参数:  strPointId -- 测点类型
*           eChannelType -- 通道类型
*           iDataType -- 数据类型
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取指定图谱数据
************************************************/
void ChartData::getChartParamData(const QString &strPointId, ADUChannelType eChannelType, int iDataType, int iDataId)
{
    Q_UNUSED(iDataType)

    int iNextDataID = 0;
    int iPreDataID = 0;Q_UNUSED(iDataType)
            DBServer::instance().nearbyRecordId(strPointId, eChannelType, iDataId, iPreDataID, iNextDataID);
    insert("nextDataId", iNextDataID);
    insert("preDataId", iPreDataID);
    switch (eChannelType)
    {
    case CHANNEL_AE://AE参数

        break;
    case CHANNEL_TEV://TEV参数

        break;
    case CHANNEL_UHF:
        break;
    case CHANNEL_HFCT:

        break;
    case CHANNEL_MECH:
        break;
    case CHANNEL_ARRESTER_I://避雷器
    case CHANNEL_GROUNDDINGCURRENT:
    case CHANNEL_LEAKAGECURRENT:
    case CHANNEL_ARRESTER_U://避雷器
        //        getArresterParamData(DBServer::instance().getArresterRecord(strPointId, iDataId) );
        break;
    case CHANNEL_VIBRATION://振动
    {
        getVibrationParamData( DBServer::instance().getVibrationRecord(strPointId, iDataId) );
    }
        break;
    default:
        return;
        break;
    }
}

/************************************************
* 函数名:  getTrendData
* 输入参数:  strPointId -- 测点类型
*           eChannelType -- 通道类型
*           iDataType -- 数据类型
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取指定趋势图谱数据
************************************************/
void ChartData::getTrendData(const QString &strPointId, ADUChannelType eChannelType, const QDateTime &startDate, const QDateTime &endDate )
{

    QVariantList dataList;
    DBServer::instance().getTrendData(strPointId, startDate, endDate, 100, &dataList); //查询数据库

    logTrace("dataList size:") << dataList.size() << 
    "startDate:" << startDate.toString(STR_DATE_TIME_QSTRING_CNA) << "endDate:" << endDate.toString(STR_DATE_TIME_QSTRING_CNA);

    auto f_insertDate = [this](const ADUChannelType chanTyp, QVariantList dataList,
            const QDateTime &startDate, const QDateTime &endDate ){
        QDateTime qdtStart;
        QDateTime qdtEnd;
        if (!dataList.empty())
        {
            switch(chanTyp)
            {
            case CHANNEL_TEV:
                qdtStart = dataList.first().value<TEVRecord>().recordTime;
                qdtEnd = dataList.last().value<TEVRecord>().recordTime;
                break;
            case CHANNEL_TEMPERATURE:
                qdtStart = dataList.first().value<TEMPRecord>().recordTime;
                qdtEnd = dataList.last().value<TEMPRecord>().recordTime;
                break;
            case CHANNEL_HUMIDITY:
                qdtStart = dataList.first().value<HumidityRecord>().recordTime;
                qdtEnd = dataList.last().value<HumidityRecord>().recordTime;
                break;
            case CHANNEL_NOISE:
                qdtStart = dataList.first().value<EnvStatusRecord>().recordTime;
                qdtEnd = dataList.last().value<EnvStatusRecord>().recordTime;
                break;
            case CHANNEL_ARRESTER_I:
            case CHANNEL_GROUNDDINGCURRENT:
            case CHANNEL_LEAKAGECURRENT:
            case CHANNEL_ARRESTER_U:
                qdtStart = dataList.first().value<ArresterRecord>().datetime;
                qdtEnd = dataList.last().value<ArresterRecord>().datetime;
                insert(STR_ADU_ID, dataList.first().value<ArresterRecord>().strADUID);
                insert(STR_POINT_NAME, dataList.first().value<ArresterRecord>().stPointArchiveInfo.strPointName);
                break;
            default:
                PDS_SYS_WARNING_LOG("f_insertDate fail %d", chanTyp);
                break;
            }
        }
        else
        {
//            qdtStart = startDate.date();
//            qdtEnd = endDate.date();
            qdtStart = startDate;
            qdtEnd = endDate;
        }
        if ( startDate.isValid() )
        {
//            qdtStart = qMin(qdtStart, startDate.date());
            qdtStart = qMin(qdtStart, startDate);
        }
        if ( endDate.isValid() )
        {
//            qdtEnd = qMax(qdtEnd, endDate.date().addDays(-1));
            qdtEnd = qMax(qdtEnd, endDate.addDays(-1));
        }

        insert(STR_SRART_DATE, qdtStart.toString(STR_DATE_TIME_QSTRING_CNA));
        insert(STR_END_DATE, qdtEnd.toString(STR_DATE_TIME_QSTRING_CNA));
    };

    QJsonObject jsonDataChart;
    QJsonArray jsonDateValues;
    QJsonArray jsonInitValues;
    QJsonArray jsonAvValues;
    QJsonArray jsonMaxValues;
    switch (eChannelType)
    {
    case CHANNEL_TEV://TEV参数
    {
        insert(STR_SENSOR_TYPE,"TEV");
        bool bRes = false;
        TEVRecord stTEVRecord = DBServer::instance().lastTEVRecord(strPointId, &bRes);
        if (bRes)
        {
            insert(STR_SAMPLE_TIME,stTEVRecord.recordTime.toString(STR_DATE_TIME_QSTRING_CNA));
            insert(STR_AMP,stTEVRecord.cMax);
        }

        if (!dataList.isEmpty())
        {
            if (!bRes)
            {
                stTEVRecord = dataList.last().value<TEVRecord>();
                insert(STR_SAMPLE_TIME, stTEVRecord.recordTime.toString(STR_DATE_TIME_QSTRING_CNA));
                insert(STR_AMP, stTEVRecord.cMax);
            }
        }
        for (int i = 0 ; i < dataList.size(); i++)
        {

            stTEVRecord  = dataList.at(i).value<TEVRecord>();
            jsonDateValues.append(stTEVRecord.recordTime.toString(STR_DATE_TIME_QSTRING_CNA));
            if (stTEVRecord.cMax < 0)
            {
                jsonInitValues.append(0);
            }
            else
            {
                jsonInitValues.append(stTEVRecord.cMax);
            }
        }

        jsonDataChart.insert(STR_X_VALUE, jsonDateValues);
        jsonDataChart.insert(STR_Y_VALUE, jsonInitValues);
        insert(STR_UNIT, "dB");
        insert(STR_DATA_CHART, jsonDataChart);
        insert(STR_ADU_ID, stTEVRecord.aduId);
        insert(STR_POINT_NAME, stTEVRecord.pointArchiveInfo.strPointName);


        f_insertDate(eChannelType, dataList, startDate, endDate);
    }
        break;
    case CHANNEL_TEMPERATURE:
    {

        insert(STR_SENSOR_TYPE,"TEMP");
        bool bRes = false;
        TEMPRecord stTEMPRecord = DBServer::instance().lastTEMPRecord(strPointId, &bRes);
        if (bRes)
        {
            insert(STR_SAMPLE_TIME, stTEMPRecord.recordTime.toString(STR_DATE_TIME_QSTRING_CNA));
        }

        if (!dataList.isEmpty())
        {
            if (bRes)
            {
                stTEMPRecord = dataList.last().value<TEMPRecord>();
                insert(STR_SAMPLE_TIME, stTEMPRecord.recordTime.toString(STR_DATE_TIME_QSTRING_CNA));
                if(!std::isinf(stTEMPRecord.temprature))
                {
                    //insert(STR_AMP, ConfigService::instance().channelDatatoString(CHANNEL_TEMPERATURE, stTEMPRecord.temprature));
                    insert(STR_AMP, stTEMPRecord.temprature);
                }
                else
                {
                    //insert(STR_AMP, ConfigService::instance().channelDatatoString(CHANNEL_TEMPERATURE, stTEMPRecord.max));
                    insert(STR_AMP, stTEMPRecord.max);
                }
            }
        }
        for (int i = 0 ; i < dataList.size(); i++)
        {
            stTEMPRecord =  dataList.at(i).value<TEMPRecord>();

            jsonDateValues.append(stTEMPRecord.recordTime.toString(STR_DATE_TIME_QSTRING_CNA));

            if(!std::isinf(stTEMPRecord.temprature))
            {
                //jsonInitValues.append(ConfigService::instance().channelDatatoString(CHANNEL_TEMPERATURE, stTEMPRecord.temprature));
                jsonInitValues.append(stTEMPRecord.temprature);
            }
            if(!std::isinf(stTEMPRecord.max))
            {
                //jsonMaxValues.append(ConfigService::instance().channelDatatoString(CHANNEL_TEMPERATURE, stTEMPRecord.max));
                jsonMaxValues.append(stTEMPRecord.max);
            }
            if(!std::isinf(stTEMPRecord.avg))
            {
                //jsonAvValues.append(ConfigService::instance().channelDatatoString(CHANNEL_TEMPERATURE, stTEMPRecord.avg));
                jsonAvValues.append(stTEMPRecord.avg);
            }
        }

        jsonDataChart.insert("chartInit", jsonInitValues);
        jsonDataChart.insert("chartMax", jsonMaxValues);
        jsonDataChart.insert("chartAvg", jsonAvValues);

        jsonDataChart.insert(STR_X_VALUE, jsonDateValues);
        insert(STR_UNIT, "℃");
        insert(STR_DATA_CHART, jsonDataChart);
        insert(STR_ADU_ID, stTEMPRecord.aduId);
        TestPointInfo stTestPoint;
        ConfigService::instance().getTestPoint(stTEMPRecord.pointArchiveInfo.strPointGUID, stTestPoint);
        insert(STR_POINT_NAME,stTestPoint.strOutName);
//        insert(STR_POINT_NAME, stTEMPRecord.pointArchiveInfo.strPointName);

        f_insertDate(eChannelType, dataList, startDate, endDate);
    }
        break;
    case CHANNEL_NOISE:
    {

        insert(STR_SENSOR_TYPE,"Noise");
        bool bRes = false;
        EnvStatusRecord stSNDRecord = DBServer::instance().lastEnvStatusRecord(strPointId, &bRes);
        if (bRes)
        {
            insert(STR_SAMPLE_TIME, stSNDRecord.recordTime.toString(STR_DATE_TIME_QSTRING_CNA));
        }

        if (!dataList.isEmpty())
        {
            if (bRes)
            {
                stSNDRecord = dataList.last().value<EnvStatusRecord>();
                insert(STR_SAMPLE_TIME, stSNDRecord.recordTime.toString(STR_DATE_TIME_QSTRING_CNA));
                if(!std::isinf(stSNDRecord.value))
                {
                    insert(STR_AMP, ConfigService::instance().channelDatatoString(CHANNEL_NOISE,
                                                                                  stSNDRecord.value
                                                                                  ));
                }
            }
        }
        for (int i = 0 ; i < dataList.size(); i++)
        {
            stSNDRecord =  dataList.at(i).value<EnvStatusRecord>();

            jsonDateValues.append(stSNDRecord.recordTime.toString(STR_DATE_TIME_QSTRING_CNA));

            if(!std::isinf(stSNDRecord.value))
            {
                jsonInitValues.append(ConfigService::instance().channelDatatoString(CHANNEL_NOISE,
                                                                                    stSNDRecord.value));
            }
        }

        jsonDataChart.insert("chartInit", jsonInitValues);

        jsonDataChart.insert(STR_X_VALUE, jsonDateValues);
        insert(STR_UNIT, "dB");
        insert(STR_DATA_CHART, jsonDataChart);
        insert(STR_ADU_ID, stSNDRecord.aduId);
        TestPointInfo stTestPoint;
        ConfigService::instance().getTestPoint(stSNDRecord.pointArchiveInfo.strPointGUID, stTestPoint);
        insert(STR_POINT_NAME,stTestPoint.strOutName);
//        insert(STR_POINT_NAME, stTEMPRecord.pointArchiveInfo.strPointName);

        f_insertDate(eChannelType, dataList, startDate, endDate);
    }
        break;
    case CHANNEL_HUMIDITY:
    {
        QJsonObject jsonDataChart;
        QJsonArray jsonXValues;
        QJsonArray jsonYValues;
        insert(STR_SENSOR_TYPE,"Humidity");
        bool bRes = false;
        HumidityRecord stHumidityRecord = DBServer::instance().lastHumidityRecord(strPointId, &bRes);
        if (bRes)
        {
            insert(STR_SAMPLE_TIME,stHumidityRecord.recordTime.toString(STR_DATE_TIME_QSTRING_CNA));
        }

        if (!dataList.isEmpty())
        {
            if (bRes)
            {
                stHumidityRecord = dataList.last().value<HumidityRecord>();
                insert(STR_SAMPLE_TIME, stHumidityRecord.recordTime.toString(STR_DATE_TIME_QSTRING_CNA));
                if(!std::isinf(stHumidityRecord.humanity))
                {
                    insert(STR_AMP, ConfigService::instance().channelDatatoString(CHANNEL_TEMPERATURE,
                                                                                  stHumidityRecord.humanity
                                                                                  ));
                }
                else
                {
                    insert(STR_AMP, ConfigService::instance().channelDatatoString(CHANNEL_TEMPERATURE,
                                                                                  stHumidityRecord.max
                                                                                  ));
                }
            }
        }
        for (int i = 0 ; i < dataList.size(); i++)
        {
            stHumidityRecord = dataList.at(i).value<HumidityRecord>();
            jsonDateValues.append(stHumidityRecord.recordTime.toString(STR_DATE_TIME_QSTRING_CNA));
            if(!std::isinf(stHumidityRecord.humanity))
            {
                jsonInitValues.append(ConfigService::instance().channelDatatoString(CHANNEL_TEMPERATURE,
                                                                                    stHumidityRecord.humanity));
                jsonDataChart.insert("chartInit", jsonInitValues);
            }
            if(!std::isinf(stHumidityRecord.max))
            {
                jsonMaxValues.append(ConfigService::instance().channelDatatoString(CHANNEL_TEMPERATURE,
                                                                                   stHumidityRecord.max));
                jsonDataChart.insert("chartMax", jsonMaxValues);
            }
            if(!std::isinf(stHumidityRecord.avg))
            {
                jsonAvValues.append(ConfigService::instance().channelDatatoString(CHANNEL_TEMPERATURE,
                                                                                  stHumidityRecord.avg));
                jsonDataChart.insert("chartAvg", jsonAvValues);
            }
        }

        jsonDataChart.insert(STR_X_VALUE, jsonDateValues);
        insert(STR_UNIT, "%RH");
        insert(STR_DATA_CHART, jsonDataChart);
        insert(STR_ADU_ID, stHumidityRecord.aduId);
        TestPointInfo stTestPoint;
        ConfigService::instance().getTestPoint(stHumidityRecord.pointArchiveInfo.strPointGUID, stTestPoint);
        insert(STR_POINT_NAME,stTestPoint.strOutName);
//        insert(STR_POINT_NAME, stHumidityRecord.pointArchiveInfo.strPointName);

        f_insertDate(eChannelType, dataList, startDate, endDate);
    }
        break;
    case CHANNEL_ARRESTER_I:
    case CHANNEL_ARRESTER_U:
    case CHANNEL_GROUNDDINGCURRENT:
    case CHANNEL_LEAKAGECURRENT:
    {
        packageArresterData(strPointId, startDate, endDate);
        f_insertDate(eChannelType, dataList, startDate, endDate);
    }
        break;
    default:
        return;
        break;
    }
}

void ChartData::getHistoryDatas(const QString &strPointId,
                                ADUChannelType eChannelType,
                                const QDateTime &startDate,
                                const QDateTime &endDate,
                                int iPageSize,
                                int iPageCount)
{
    QList<QDateTime> dateList;
    QList<float> dataList;
    QList<QString> listDataID;
    QList<int> listSensorType;
    int iDataCounts;
    QString strUnit;
    DBServer::instance().getHistoryData( strPointId,
                                         eChannelType,
                                         startDate, endDate,
                                         dateList,
                                         dataList,
                                         listDataID,
                                         listSensorType,
                                         iDataCounts,
                                         iPageSize,iPageCount);//去数据库获取数据

    TestPointInfo stTestPoint;
    if(!ConfigService::instance().getTestPoint(strPointId, stTestPoint))
    {
        PDS_SYS_WARNING_LOG("[ChartData::getHistoryDatas] point %s does not exist", strPointId.toLatin1().data());
        return;
    }

    switch (eChannelType)
    {
    case CHANNEL_FROST_RAW:
    {
        strUnit = "℃";
    }
        break;
    case CHANNEL_FROST_ATM:
    {
        strUnit = "℃";
    }
        break;
    case CHANNEL_DEW_RAW:
    {
        strUnit = "℃";
    }
        break;
    case CHANNEL_DEW_ATM:
    {
        strUnit = "℃";
    }
        break;
    case CHANNEL_MOISTURE:
    {
        strUnit = "ppm";
    }
        break;
    case CHANNEL_PRESS_ABSO:
    {
        strUnit = "bara";
    }
        break;
    case CHANNEL_PRESS_NORM:
    {
        strUnit = "bara";
    }
        break;
    case CHANNEL_DENSITY:
    {
        strUnit = "kg/m³";
    }
        break;
    case CHANNEL_OXYGEN:
    {
        strUnit = "%";
    }
        break;
    case CHANNEL_SF6:
    {
        strUnit = "%";
    }
        break;
    case CHANNEL_TEMPERATURE:
    {
        strUnit = "℃";
    }
        break;
    case CHANNEL_HUMIDITY:
    {
        strUnit = "%";
    }
        break;

    default:
        return;
        break;
    }

    QJsonArray array;
    for( int i = 0; i < dateList.size(); i++)
    {
        QJsonObject jsonDatas;
        jsonDatas.insert( STR_DATA_ID, listDataID.at(i) );
        QString strADUName = ConfigService::instance().getADUTypName(ADUType(listSensorType.at(i)));
        jsonDatas.insert( STR_SENSOR_TYPE, strADUName);
        jsonDatas.insert( STR_POINT_ID, strPointId);
        jsonDatas.insert( STR_POINT_NAME, stTestPoint.strOutName );
        jsonDatas.insert( STR_VALUE, ConfigService::instance().channelDatatoString(eChannelType, dataList.at(i)) );
        jsonDatas.insert( STR_UNIT, strUnit );
        jsonDatas.insert( STR_HISTORY_SAMPLE_DATE, dateList.at(i).toString(STR_DATE_TIME_QSTRING_CNA) );

        array.append(jsonDatas);
    }

    insert(STR_ROWS, array);
    insert(STR_TOTAL, iDataCounts);
}


/************************************************
* 函数名:  getAEData
* 输入参数:  aeRecord -- ae数据
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  转换ae波形数据
************************************************/
void ChartData::getAEData(const AERecord &aeRecord)
{
    QJsonArray jsonChartDatas;
    QJsonObject jsonRMS;
    QJsonArray jsonDataLists;
    QJsonObject jsonValue;
    QJsonObject jsonAxisInfo;
    QJsonObject jsonNoise;
    QJsonObject jsonParam;

    QString strDataUnit = "dB";
    if (aeRecord.eAmpUnit != RecordBase::DataUnit::DATA_UNIT_UNUSED)
    {
        strDataUnit = ConfigService::instance().getDataUnitString(aeRecord.eAmpUnit);
    }
    float fAmpLowerLimit = 0; //幅值下限
    float fAmpUpperLimit = 0; //幅值上限

    AE::GainType gaintype = AE::gainVal2Type(aeRecord.ucGain);//内置局放传感器 增益无效
    jsonValue.insert(STR_COLOR, "#008000");
    jsonValue.insert(STR_X, aeRecord.fRms);
    jsonNoise.insert(STR_COLOR, "#ff0000");
    if(aeRecord.fAmpUpperLimit != aeRecord.fAmpLowerLimit)
    {
        fAmpLowerLimit = aeRecord.fAmpLowerLimit;
        fAmpUpperLimit = aeRecord.fAmpUpperLimit;
    }
    else
    {
        fAmpUpperLimit = AE::AMPLITUDE_MAX_VALUES[1][gaintype][AE::COMPONENT_EFFECTIVE];
    }

    jsonNoise.insert(STR_X, fAmpLowerLimit);
    jsonDataLists.append(jsonValue);
    jsonDataLists.append(jsonNoise);

    jsonAxisInfo.insert(STR_X_RANGE_MAX, fAmpUpperLimit);
    jsonAxisInfo.insert(STR_X_RANGE_MIN, fAmpLowerLimit);
    jsonAxisInfo.insert(STR_X_DESC, STR_RMS);
    jsonAxisInfo.insert(STR_X_UNIT, strDataUnit);

    jsonRMS.insert(STR_DATA_LIST, jsonDataLists);
    jsonRMS.insert(STR_AXIS_INFO, jsonAxisInfo);
    jsonChartDatas.append(jsonRMS);

    jsonValue.insert(STR_X, aeRecord.fMax);
    jsonDataLists[0] = jsonValue;
    jsonAxisInfo.insert(STR_X_DESC, STR_MAX);
    jsonAxisInfo.insert(STR_X_RANGE_MAX, fAmpUpperLimit);
    jsonAxisInfo.insert(STR_X_RANGE_MIN, fAmpLowerLimit);
    jsonAxisInfo.insert(STR_X_UNIT, strDataUnit);
    jsonRMS.insert(STR_DATA_LIST, jsonDataLists);
    jsonRMS.insert(STR_AXIS_INFO, jsonAxisInfo);
    jsonChartDatas.append(jsonRMS);

    jsonValue.insert(STR_X, aeRecord.fFre1);
    jsonDataLists[0] = jsonValue;
    jsonAxisInfo.insert(STR_X_DESC, STR_FRE_1_VALUE);
    jsonAxisInfo.insert(STR_X_RANGE_MAX, fAmpUpperLimit);
    jsonAxisInfo.insert(STR_X_RANGE_MIN, fAmpLowerLimit);
    jsonAxisInfo.insert(STR_X_UNIT, strDataUnit);
    jsonRMS.insert(STR_DATA_LIST, jsonDataLists);
    jsonRMS.insert(STR_AXIS_INFO, jsonAxisInfo);
    jsonChartDatas.append(jsonRMS);

    jsonValue.insert(STR_X, aeRecord.fFre2);
    jsonDataLists[0] = jsonValue;
    jsonAxisInfo.insert(STR_X_DESC, STR_FRE_2_VALUE);
    jsonAxisInfo.insert(STR_X_RANGE_MAX, fAmpUpperLimit);
    jsonAxisInfo.insert(STR_X_RANGE_MIN, fAmpLowerLimit);
    jsonAxisInfo.insert(STR_X_UNIT, strDataUnit);
    jsonRMS.insert(STR_DATA_LIST, jsonDataLists);
    jsonRMS.insert(STR_AXIS_INFO, jsonAxisInfo);
    jsonChartDatas.append(jsonRMS);

    jsonParam.insert(STR_FREQUNCY, aeRecord.ucPwrFre);
    jsonParam.insert(STR_SENSOR_TYPE,"AE");
    jsonParam.insert(STR_SAMPLE_DATE,aeRecord.recordTime.date().toString(STR_DATE_QSTRING_CNA));
    jsonParam.insert(STR_SAMPLE_TIME,aeRecord.recordTime.time().toString(STR_TIME_QSTRING));
//    jsonParam.insert(STR_RMS_LOWER,QString::number((int)aeRecord.fRms) + QString("dB"));
//    jsonParam.insert(STR_SYCLE_MAX,QString::number((int)aeRecord.fMax) + QString("dB"));
//    jsonParam.insert(STR_FREQUENCY1,QString::number((int)aeRecord.fFre1) + QString("dB"));
//    jsonParam.insert(STR_FREQUENCY2,QString::number((int)aeRecord.fFre2) + QString("dB"));
//    jsonParam.insert(STR_PARA_GAIN,QString::number((int)aeRecord.ucGain) + QString("dB"));

    jsonParam.insert(STR_RMS_LOWER, QString("%1%2").arg(QString::number(aeRecord.fRms), strDataUnit));
    jsonParam.insert(STR_SYCLE_MAX, QString("%1%2").arg(QString::number(aeRecord.fMax), strDataUnit));
    jsonParam.insert(STR_FREQUENCY1, QString("%1%2").arg(QString::number(aeRecord.fFre1), strDataUnit));
    jsonParam.insert(STR_FREQUENCY2, QString("%1%2").arg(QString::number(aeRecord.fFre2), strDataUnit));
    jsonParam.insert(STR_PARA_GAIN, QString("%1%2").arg(QString::number(aeRecord.ucGain), "dB"));

    jsonParam.insert("battaryLife",QString::number(aeRecord.batteryInfo / 1000));
    jsonParam.insert("battaryPercent",QString::number(aeRecord.batteryInfo % 1000));
    jsonParam.insert(STR_ADU_ID,aeRecord.aduId);
    jsonParam.insert(STR_POINT_NAME,aeRecord.pointArchiveInfo.strPointName);

    insert(STR_PARAMS, jsonParam);

    AEWaveformChartDataGenerator aeWaveformChartDataGenerator(aeRecord);
    jsonChartDatas.append(aeWaveformChartDataGenerator.generateChartData());

    AePhaseChartDataGenerator aePhaseChartDataGenerator(aeRecord);
    jsonChartDatas.append(aePhaseChartDataGenerator.generateChartData());

    AeFlyChartDataGenerator aeFlyChartDataGenerator(aeRecord);
    jsonChartDatas.append(aeFlyChartDataGenerator.generateChartData());

   insert(STR_CHART_BODY, jsonChartDatas);
}

/************************************************
* 函数名:  getPRPSData
* 输入参数:  PrpsRecord -- prps数据
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  转换PRPS数据
************************************************/
void ChartData::getPRPSData(ADUChannelType eChannelType, const PRPSRecord &PrpsRecord)
{
    QJsonArray jsonDataLists;
    QJsonObject jsonAxisInfo;
    QJsonObject jsonParam;
    QJsonObject jsonChartData;

    float fPRPSDataMax = 0.f;
    float fPRPSDataMin = 0.f;
    float iPRPSRange = 0.f;

    QString strDataUnit = "dB";
    if (PrpsRecord.eAmpUnit != RecordBase::DataUnit::DATA_UNIT_UNUSED)
    {
        strDataUnit = ConfigService::instance().getDataUnitString(PrpsRecord.eAmpUnit);
    }


    if (eChannelType == CHANNEL_UHF)
    {
        jsonParam.insert(STR_SENSOR_TYPE,"UHF");
        iPRPSRange = 70;
        jsonParam.insert(STR_PARA_FILTER, ConfigService::instance().getBandWidthName((UHFDataFilter)PrpsRecord.ucBandwidth));//带宽
        fPRPSDataMax = 70;
        fPRPSDataMin = 0;
    }
    else if (eChannelType == CHANNEL_HFCT)
    {
        iPRPSRange = 80;
        jsonParam.insert(STR_SENSOR_TYPE,"HFCT");
        const int iZRangeMax = (PrpsRecord.ucGain == -1 ? 80 : 40 - PrpsRecord.ucGain); //增益为-1时，为无增益。最大值是80dB，最小值是0dB
        const int iZRangeMin = (PrpsRecord.ucGain == -1 ?  0 : 0 - PrpsRecord.ucGain);
        fPRPSDataMax = iZRangeMax;
        fPRPSDataMin = iZRangeMin;
    }
    else if (eChannelType == CHANNEL_TEVPRPS)
    {
        iPRPSRange = 70;
        jsonParam.insert(STR_SENSOR_TYPE,"TEVPRPS");
        fPRPSDataMax = 70;
        fPRPSDataMin = 0;
    }
    else
    {
    }

    if(PrpsRecord.fAmpUpperLimit != PrpsRecord.fAmpLowerLimit)
    {
        fPRPSDataMax = PrpsRecord.fAmpUpperLimit;
        fPRPSDataMin = PrpsRecord.fAmpLowerLimit;
    }

    jsonAxisInfo.insert(STR_Z_RANGE_MAX, fPRPSDataMax);
    jsonAxisInfo.insert(STR_Z_RANGE_MIN, fPRPSDataMin);

    int period = 0;
    int phase = 0;
    // 可能是历史版本不兼容造成 fArray.size() != (PrpsRecord.ucPeriod * PrpsRecord.ucPhase)
    if(3000 == PrpsRecord.fArray.size() && (PrpsRecord.fArray.size() != PrpsRecord.ucPeriod * PrpsRecord.ucPhase ))
    {
        period = 50;
        phase = 60;
    }
    else if(PrpsRecord.fArray.size() == PrpsRecord.ucPeriod * PrpsRecord.ucPhase)
    {
        period = PrpsRecord.ucPeriod;
        phase = PrpsRecord.ucPhase;
    }
    else if ( PrpsRecord.fArray.size() != PrpsRecord.ucPeriod * PrpsRecord.ucPhase )
    {
        PDS_SYS_WARNING_LOG("error record: %s %d", PrpsRecord.pointArchiveInfo.strPointGUID.toLatin1().data(), PrpsRecord.globalId);
    }

    for (int i = 0; i < period; ++i)
    {
        for (int j = 0; j < phase; ++j)
        {
            const float fData = PrpsRecord.fArray.at((phase*i) + j);

            QJsonObject jsonPRPS;
            jsonPRPS.insert(STR_COLOR, ""); //颜色由前端处理
            jsonPRPS.insert(STR_X, (360/phase) * j);
            jsonPRPS.insert(STR_Y, i );
            jsonPRPS.insert(STR_Z, (fData * iPRPSRange));
            jsonDataLists.append(jsonPRPS);
        }
    }


    jsonChartData.insert(STR_DATA_LIST,jsonDataLists);

    jsonParam.insert(STR_PARA_GAIN,QString("%1%2").arg(QString::number(PrpsRecord.ucGain), "dB"));

    jsonParam.insert(STR_SAMPLE_DATE,PrpsRecord.recordTime.date().toString(STR_DATE_QSTRING_CNA));
    jsonParam.insert(STR_SAMPLE_TIME,PrpsRecord.recordTime.time().toString(STR_TIME_QSTRING));
    jsonParam.insert(STR_PD_MAX,QString("%1%2").arg(QString::number(PrpsRecord.fMaxq * iPRPSRange, 'f'), strDataUnit));
    jsonParam.insert(STR_PD_MIN,QString("%1%2").arg(QString::number(PrpsRecord.fMinq * iPRPSRange, 'f'), strDataUnit));
    jsonParam.insert(STR_PD_AVG,QString("%1%2").arg(QString::number(PrpsRecord.fAvgq * iPRPSRange, 'f'), strDataUnit));
    jsonParam.insert(STR_PD_NUMBER,PrpsRecord.iPdCount);
    jsonParam.insert(STR_PRPS_PD_TYPE,PrpsRecord.iPdType);
    jsonParam.insert(STR_PRPS_IS_PD,PrpsRecord.iIsPd);

    jsonParam.insert("battaryLife",QString::number(PrpsRecord.batteryInfo / 1000));
    jsonParam.insert("battaryPercent",QString::number(PrpsRecord.batteryInfo % 1000));
    jsonParam.insert(STR_ADU_ID,PrpsRecord.aduId);
    jsonParam.insert(STR_POINT_NAME,PrpsRecord.pointArchiveInfo.strPointName);

    jsonAxisInfo.insert(STR_X_DESC, STR_PHASE);
    jsonAxisInfo.insert(STR_X_RANGE_MAX, 360);
    jsonAxisInfo.insert(STR_X_RANGE_MIN, 0);
    jsonAxisInfo.insert(STR_X_UNIT, "°");

    jsonAxisInfo.insert(STR_Y_DESC, STR_PERIOD);
    jsonAxisInfo.insert(STR_Y_RANGE_MAX, PrpsRecord.ucPeriod);
    jsonAxisInfo.insert(STR_Y_RANGE_MIN, 0);
    jsonAxisInfo.insert(STR_Y_UNIT, "T");

    jsonAxisInfo.insert(STR_Z_DESC, STR_AMP);
    jsonAxisInfo.insert(STR_Z_MAX_VALUE, QString::number(PrpsRecord.fMaxq * iPRPSRange, 'f', 1));
    jsonAxisInfo.insert(STR_Z_UNIT, strDataUnit);

    //增加周期、相位数返回
    jsonAxisInfo.insert("freqCycleNum", PrpsRecord.ucPeriod); //周期
    jsonAxisInfo.insert("phaseNum", PrpsRecord.ucPhase); //相位
    jsonChartData.insert(STR_AXIS_INFO, jsonAxisInfo);

    insert(STR_PARAMS, jsonParam);
    insert(STR_CHART_BODY, jsonChartData);
}

/************************************************
* 函数名:  getPRPDData
* 输入参数:  PrpsRecord -- prps数据
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  转换PRPD数据
************************************************/
void ChartData::getPRPDData(ADUChannelType eChannelType, const PRPSRecord &PrpsRecord)
{
    QJsonObject jsonPRPS;
    QJsonArray jsonDataLists;
    QJsonObject jsonAxisInfo;
    QJsonObject jsonParam;
    QJsonObject jsonChartData;

    float iPRPSRange = 0;
    float iYRangeMax = 0; //幅值最大值
    float iYRangeMin = 0; //幅值最小值

    QString strDataUnit = "dB";
    if (PrpsRecord.eAmpUnit != RecordBase::DataUnit::DATA_UNIT_UNUSED)
    {
        strDataUnit = ConfigService::instance().getDataUnitString(PrpsRecord.eAmpUnit);
    }

    if (eChannelType == CHANNEL_UHF)
    {
        jsonParam.insert(STR_SENSOR_TYPE,"UHF");
        iPRPSRange = 70;
        jsonParam.insert(STR_PARA_FILTER, ConfigService::instance().getBandWidthName((UHFDataFilter)PrpsRecord.ucBandwidth));//带宽
        iYRangeMax = 70;
        iYRangeMin = 0;
    }
    else if (eChannelType == CHANNEL_HFCT)
    {
        iPRPSRange = 80;
        jsonParam.insert(STR_SENSOR_TYPE,"HFCT");
        iYRangeMax = (PrpsRecord.ucGain == -1 ? 80 : 40 - PrpsRecord.ucGain); //增益为-1时，为无增益。最大值是80dB，最小值是0dB
        iYRangeMin = (PrpsRecord.ucGain == -1 ?  0 : 0 - PrpsRecord.ucGain);
    }
    else if (eChannelType == CHANNEL_TEVPRPS)
    {
        iPRPSRange = 70;
        jsonParam.insert(STR_SENSOR_TYPE, "TEVPRPS");
        iYRangeMax = 70;
        iYRangeMin = 0;        
    }
    else
    {

    }

    if(PrpsRecord.fAmpUpperLimit != PrpsRecord.fAmpLowerLimit)
    {
        iYRangeMax = PrpsRecord.fAmpUpperLimit;
        iYRangeMin = PrpsRecord.fAmpLowerLimit;
    }

    jsonAxisInfo.insert(STR_Y_RANGE_MAX, iYRangeMax);
    jsonAxisInfo.insert(STR_Y_RANGE_MIN, iYRangeMin);


    int period = 0;
    int phase = 0;
    // 可能是历史版本不兼容造成 fArray.size() != (PrpsRecord.ucPeriod * PrpsRecord.ucPhase)
    if(3000 == PrpsRecord.fArray.size() && (PrpsRecord.fArray.size() != PrpsRecord.ucPeriod * PrpsRecord.ucPhase ))
    {
        period = 50;
        phase = 60;
    }
    else if(PrpsRecord.fArray.size() == PrpsRecord.ucPeriod * PrpsRecord.ucPhase)
    {
        period = PrpsRecord.ucPeriod;
        phase = PrpsRecord.ucPhase;
    }
    else if ( PrpsRecord.fArray.size() != PrpsRecord.ucPeriod * PrpsRecord.ucPhase )
    {
        PDS_SYS_WARNING_LOG("error record: %s %d", PrpsRecord.pointArchiveInfo.strPointGUID.toLatin1().data(), PrpsRecord.globalId);
    }

    std::vector<float> originPrpsData;
    originPrpsData.reserve(PrpsRecord.fArray.size());
    std::transform(PrpsRecord.fArray.begin(), PrpsRecord.fArray.end(), std::back_inserter(originPrpsData),
                   [&iPRPSRange](float val) { return val * iPRPSRange; });

    std::vector<std::vector<int> > prpdData; //prpd数据
    const uint ampNum = 100; //幅值分区数
    float step = (iYRangeMax - iYRangeMin) / static_cast<float>(ampNum);
    if(getPrpdFromPrps(convertPrpsTo2D(originPrpsData, period, phase), iYRangeMin, iYRangeMax, ampNum, prpdData))
    {
        for(int phaseNum = 0; phaseNum < prpdData.size(); ++phaseNum)
        {
            for(int ampNumIndex = 0; ampNumIndex < ampNum; ++ampNumIndex)
            {
                if(prpdData[phaseNum][ampNumIndex] > 0)
                {
                    jsonPRPS.insert(STR_X, (360 / phase) * phaseNum);
                    jsonPRPS.insert(STR_Y, ampNumIndex * step + iYRangeMin);
                    jsonPRPS.insert(STR_Z, prpdData[phaseNum][ampNumIndex]);
                    jsonDataLists.append(jsonPRPS);
                }
            }
        }
    }
    else
    {
        logError("get Prpd From Prps failed!");
    }

    jsonChartData.insert(STR_DATA_LIST, jsonDataLists);

    jsonAxisInfo.insert(STR_X_DESC, STR_PHASE);
    jsonAxisInfo.insert(STR_X_RANGE_MAX, 360);
    jsonAxisInfo.insert(STR_X_RANGE_MIN, 0);
    jsonAxisInfo.insert(STR_X_UNIT, "°");

    jsonAxisInfo.insert(STR_Y_DESC, STR_AMP);
    jsonAxisInfo.insert(STR_Y_INTERVAL, 1.4);
    jsonAxisInfo.insert(STR_Y_UNIT, strDataUnit);

    //增加周期、相位数返回
    jsonAxisInfo.insert("freqCycleNum", PrpsRecord.ucPeriod); //周期
    jsonAxisInfo.insert("phaseNum", PrpsRecord.ucPhase); //相位
    jsonChartData.insert(STR_AXIS_INFO, jsonAxisInfo);

    jsonParam.insert(STR_PARA_GAIN,QString("%1%2").arg(QString::number(PrpsRecord.ucGain), "dB"));
    jsonParam.insert(STR_SAMPLE_DATE,PrpsRecord.recordTime.date().toString(STR_DATE_QSTRING_CNA));
    jsonParam.insert(STR_SAMPLE_TIME,PrpsRecord.recordTime.time().toString(STR_TIME_QSTRING));
    //jsonParam.insert(STR_PD_MAX,QString("%1dB").arg(QString::number(PrpsRecord.fMaxq * iPRPSRange, 'f', 1)));
    //jsonParam.insert(STR_PD_MIN,QString("%1dB").arg(int(PrpsRecord.fMinq * iPRPSRange)));
    //jsonParam.insert(STR_PD_AVG,QString("%1dB").arg(QString::number(PrpsRecord.fAvgq * iPRPSRange, 'f', 1)));
    jsonParam.insert(STR_PD_MAX,QString("%1%2").arg(QString::number(PrpsRecord.fMaxq * iPRPSRange, 'f'), strDataUnit));
    jsonParam.insert(STR_PD_AVG,QString("%1%2").arg(QString::number(PrpsRecord.fAvgq * iPRPSRange, 'f'), strDataUnit));


    jsonParam.insert(STR_PD_NUMBER,PrpsRecord.iPdCount);

    insert(STR_PARAMS, jsonParam);
    insert(STR_CHART_BODY, jsonChartData);
}

/************************************************
* 函数名:  getPRPSData
* 输入参数:  PrpsRecord -- prps数据
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  转换PRPS数据
************************************************/
void ChartData::getTEVPRPSData(ADUChannelType eChannelType, const PRPSRecord &PrpsRecord)
{
    QJsonObject jsonPRPS;
    QJsonArray jsonDataLists;
    QJsonObject jsonAxisInfo;
    QJsonObject jsonParam;
    QJsonObject jsonChartData;
    int iPRPSRange = 0;

    if (eChannelType == CHANNEL_UHF)
    {
        jsonParam.insert(STR_SENSOR_TYPE,"UHF");
        iPRPSRange = 70;
        jsonParam.insert(STR_PARA_FILTER, ConfigService::instance().getBandWidthName((UHFDataFilter)PrpsRecord.ucBandwidth));//带宽
        jsonAxisInfo.insert(STR_Z_RANGE_MAX, 70);
        jsonAxisInfo.insert(STR_Z_RANGE_MIN, 0);

    }
    else if (eChannelType == CHANNEL_HFCT)
    {
        iPRPSRange = 80;
        jsonParam.insert(STR_SENSOR_TYPE,"HFCT");
        jsonAxisInfo.insert(STR_Z_RANGE_MAX, 20 - PrpsRecord.ucGain);
        jsonAxisInfo.insert(STR_Z_RANGE_MIN, 0 - PrpsRecord.ucGain);
    }
    else if (eChannelType == CHANNEL_TEVPRPS)
    {
        iPRPSRange = 70;
        jsonParam.insert(STR_SENSOR_TYPE,"TEVPRPS");
        jsonAxisInfo.insert(STR_Z_RANGE_MAX, 70);
        jsonAxisInfo.insert(STR_Z_RANGE_MIN, 0);
    }

    int period = 0;
    int phase = 0;
    // 可能是历史版本不兼容造成 fArray.size() != (PrpsRecord.ucPeriod * PrpsRecord.ucPhase)
    if(3000 == PrpsRecord.fArray.size() && (PrpsRecord.fArray.size() != PrpsRecord.ucPeriod * PrpsRecord.ucPhase ))
    {
        period = 50;
        phase = 60;
    }
    else if(PrpsRecord.fArray.size() == PrpsRecord.ucPeriod * PrpsRecord.ucPhase)
    {
        period = PrpsRecord.ucPeriod;
        phase = PrpsRecord.ucPhase;
    }
    else if ( PrpsRecord.fArray.size() != PrpsRecord.ucPeriod * PrpsRecord.ucPhase )
    {
        PDS_SYS_WARNING_LOG("error record: %s %d", PrpsRecord.pointArchiveInfo.strPointGUID.toLatin1().data(), PrpsRecord.globalId);
    }

    float fData = 0;
    for (int i = 0; i < period; i++)
    {
        for (int j = 0; j < phase; j++)
        {
            fData = PrpsRecord.fArray.at((phase*i) + j);

            QColor color = QColor::fromHsl( ( qint32 )( 40 * fData ),
                                            ( qint32 )( 180 + 30 * fData ),
                                            ( qint32 )( 120 + 80 * fData ) );
            QByteArray charRed;
            charRed.append(quint8(color.red()));
            QByteArray charGreen;
            charGreen.append(quint8(color.green()));
            QByteArray charBlue;
            charBlue.append(quint8(color.blue()));
            jsonPRPS.insert(STR_COLOR, QString("#%1%2%3").arg((QString)(charRed.toHex().toUpper()))
                            .arg((QString)(charGreen.toHex().toUpper()))
                            .arg((QString)(charBlue.toHex().toUpper())));

            jsonPRPS.insert(STR_X, (360/phase) * j);
            jsonPRPS.insert(STR_Y, i );
            fData = fData * iPRPSRange;
            QString strData = QString :: number(fData,'f',1);
            fData = strData.toFloat();
            jsonPRPS.insert(STR_Z, ((fData)) );
            jsonDataLists.append(jsonPRPS);

        }
    }


    jsonChartData.insert(STR_DATA_LIST,jsonDataLists);

    jsonParam.insert(STR_PARA_GAIN,QString("%1dB").arg(PrpsRecord.ucGain));

    jsonParam.insert(STR_SAMPLE_DATE,PrpsRecord.recordTime.date().toString(STR_DATE_QSTRING_CNA));
    jsonParam.insert(STR_SAMPLE_TIME,PrpsRecord.recordTime.time().toString(STR_TIME_QSTRING));
    jsonParam.insert(STR_PD_MAX,QString("%1dB").arg(QString::number(PrpsRecord.fMaxq * iPRPSRange, 'f')));
    jsonParam.insert(STR_PD_MIN,QString("%1dB").arg(QString::number(PrpsRecord.fMinq * iPRPSRange, 'f')));
    jsonParam.insert(STR_PD_AVG,QString("%1dB").arg(QString::number(PrpsRecord.fAvgq * iPRPSRange, 'f')));

    jsonParam.insert(STR_PD_NUMBER,PrpsRecord.iPdCount);
    jsonParam.insert(STR_PRPS_PD_TYPE,PrpsRecord.iPdType);
    jsonParam.insert(STR_PRPS_IS_PD,PrpsRecord.iIsPd);

    jsonParam.insert("battaryLife",QString::number(PrpsRecord.batteryInfo / 1000));
    jsonParam.insert("battaryPercent",QString::number(PrpsRecord.batteryInfo % 1000));
    jsonParam.insert(STR_ADU_ID,PrpsRecord.aduId);
    jsonParam.insert(STR_POINT_NAME,PrpsRecord.pointArchiveInfo.strPointName);

    jsonAxisInfo.insert(STR_X_DESC, STR_PHASE);
    jsonAxisInfo.insert(STR_X_RANGE_MAX, 360);
    jsonAxisInfo.insert(STR_X_RANGE_MIN, 0);
    jsonAxisInfo.insert(STR_X_UNIT, "°");

    jsonAxisInfo.insert(STR_Y_DESC, STR_PERIOD);
    jsonAxisInfo.insert(STR_Y_RANGE_MAX, PrpsRecord.ucPeriod);
    jsonAxisInfo.insert(STR_Y_RANGE_MIN, 0);
    jsonAxisInfo.insert(STR_Y_UNIT, "T");

    jsonAxisInfo.insert(STR_Z_DESC, STR_AMP);
    jsonAxisInfo.insert(STR_Z_MAX_VALUE, ((PrpsRecord.fMaxq * iPRPSRange)));
    jsonAxisInfo.insert(STR_Z_UNIT, "dB");
    jsonChartData.insert(STR_AXIS_INFO, jsonAxisInfo);

    insert(STR_PARAMS, jsonParam);
    insert(STR_CHART_BODY, jsonChartData);
}

/************************************************
* 函数名:  getPRPDData
* 输入参数:  PrpsRecord -- prps数据
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  转换PRPD数据
************************************************/
void ChartData::getTEVPRPDData(ADUChannelType eChannelType, const PRPSRecord &PrpsRecord)
{
    QJsonObject jsonPRPS;
    QJsonArray jsonDataLists;
    QJsonObject jsonAxisInfo;
    QJsonObject jsonParam;
    QJsonObject jsonChartData;
    int iPRPSRange = 0;
    if (eChannelType == CHANNEL_UHF)
    {
        jsonParam.insert(STR_SENSOR_TYPE,"UHF");
        iPRPSRange = 70;
        jsonParam.insert(STR_PARA_FILTER, ConfigService::instance().getBandWidthName((UHFDataFilter)PrpsRecord.ucBandwidth));//带宽
        jsonAxisInfo.insert(STR_Y_RANGE_MAX, 70);
        jsonAxisInfo.insert(STR_Y_RANGE_MIN, 0);
    }
    else if (eChannelType == CHANNEL_HFCT)
    {
        iPRPSRange = 80;
        jsonParam.insert(STR_SENSOR_TYPE,"HFCT");
        jsonAxisInfo.insert(STR_Y_RANGE_MAX, 20 - PrpsRecord.ucGain);
        jsonAxisInfo.insert(STR_Y_RANGE_MIN, 0 - PrpsRecord.ucGain);
    }
    else if (eChannelType == CHANNEL_TEVPRPS)
    {
        jsonParam.insert(STR_SENSOR_TYPE,"TEVPRPS");
        iPRPSRange = 70;
        jsonAxisInfo.insert(STR_Y_RANGE_MAX, 70);
        jsonAxisInfo.insert(STR_Y_RANGE_MIN, 0);
    }

    int period = 0;
    int phase = 0;
    // 可能是历史版本不兼容造成 fArray.size() != (PrpsRecord.ucPeriod * PrpsRecord.ucPhase)
    if(3000 == PrpsRecord.fArray.size() && (PrpsRecord.fArray.size() != PrpsRecord.ucPeriod * PrpsRecord.ucPhase ))
    {
        period = 50;
        phase = 60;
    }
    else if(PrpsRecord.fArray.size() == PrpsRecord.ucPeriod * PrpsRecord.ucPhase)
    {
        period = PrpsRecord.ucPeriod;
        phase = PrpsRecord.ucPhase;
    }
    else if ( PrpsRecord.fArray.size() != PrpsRecord.ucPeriod * PrpsRecord.ucPhase )
    {
        PDS_SYS_WARNING_LOG("error record: %s %d", PrpsRecord.pointArchiveInfo.strPointGUID.toLatin1().data(), PrpsRecord.globalId);
    }


    for (int i = 0; i < period; i++)
    {
        for (int j = 0; j < phase; j++)
        {
            float fData = PrpsRecord.fArray.at((phase*i) + j);

            QColor color = QColor::fromHsl( ( qint32 )( 40 * fData ),
                                            ( qint32 )( 180 + 30 * fData ),
                                            ( qint32 )( 120 + 80 * fData ) );
            QByteArray charRed;
            charRed.append(quint8(color.red()));
            QByteArray charGreen;
            charGreen.append(quint8(color.green()));
            QByteArray charBlue;
            charBlue.append(quint8(color.blue()));
            jsonPRPS.insert(STR_COLOR, QString("#%1%2%3").arg((QString)(charRed.toHex().toUpper()))
                            .arg((QString)(charGreen.toHex().toUpper()))
                            .arg((QString)(charBlue.toHex().toUpper())));

            jsonPRPS.insert(STR_X, (360/phase) * j);
            fData = fData * iPRPSRange;
            QString strData = QString :: number(fData,'f',1);
            fData = strData.toFloat();
            jsonPRPS.insert(STR_Y, ((fData)) );
            jsonDataLists.append(jsonPRPS);

        }
    }


    jsonChartData.insert(STR_DATA_LIST,jsonDataLists);

    jsonAxisInfo.insert(STR_X_DESC, STR_PHASE);
    jsonAxisInfo.insert(STR_X_RANGE_MAX, 360);
    jsonAxisInfo.insert(STR_X_RANGE_MIN, 0);
    jsonAxisInfo.insert(STR_X_UNIT, "°");

    jsonAxisInfo.insert(STR_Y_DESC, STR_AMP);
    jsonAxisInfo.insert(STR_Y_INTERVAL, 1.4);
    jsonAxisInfo.insert(STR_Y_UNIT, "dB");

    jsonChartData.insert(STR_AXIS_INFO, jsonAxisInfo);
    jsonParam.insert(STR_PARA_GAIN,QString("%1dB").arg(PrpsRecord.ucGain));
    jsonParam.insert(STR_SAMPLE_DATE,PrpsRecord.recordTime.date().toString(STR_DATE_QSTRING_CNA));
    jsonParam.insert(STR_SAMPLE_TIME,PrpsRecord.recordTime.time().toString(STR_TIME_QSTRING));
    jsonParam.insert(STR_PD_MAX,QString("%1dB").arg(((PrpsRecord.fMaxq * iPRPSRange))));
    //    jsonParam.insert(STR_PD_MIN,QString("%1dB").arg(int(PrpsRecord.fMinq * iPRPSRange)));
    jsonParam.insert(STR_PD_AVG,QString("%1dB").arg(((PrpsRecord.fAvgq * iPRPSRange))));
    jsonParam.insert(STR_PD_NUMBER,PrpsRecord.iPdCount);

    insert(STR_PARAMS, jsonParam);
    insert(STR_CHART_BODY, jsonChartData);
}

/************************************************
* 函数名:  getMechLoopData
* 输入参数:  stMechRecord -- 机械特性数据
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  机械特性线圈数据
************************************************/
void ChartData::getMechLoopData(const MechDefine::MechISRecord &stMechRecord)
{
    QJsonObject jsonCoilDataBady;
    QJsonObject jsonCoilCurrent;
    QJsonObject jsonAxisInfo;
    QJsonArray jsonDataSeries;
    QJsonObject jsonDataSerie;
    QJsonObject jsonDataList;
    double dLoopValueMax = 0;
    double dXRangeMax = 0;

    QJsonArray jsonloopCloseA;
    QJsonArray jsonloopCloseB;
    QJsonArray jsonloopCloseC;
    QJsonArray jsonloopOpenA;
    QJsonArray jsonloopOpenB;
    QJsonArray jsonloopOpenC;
    QJsonArray jsonSwitchState;
    QJsonArray jsonSwitchStateA;
    QJsonArray jsonSwitchStateB;
    QJsonArray jsonSwitchStateC;
    dXRangeMax = MAX(stMechRecord.loopCloseBCurrentLst.size(), stMechRecord.loopCloseACurrentLst.size());//获取数据点数
    dXRangeMax = MAX(dXRangeMax, stMechRecord.loopCloseCCurrentLst.size());
    dXRangeMax = MAX(dXRangeMax, stMechRecord.loopOpenACurrentLst.size());
    dXRangeMax = MAX(dXRangeMax, stMechRecord.loopOpenBCurrentLst.size());
    dXRangeMax = MAX(dXRangeMax, stMechRecord.loopOpenCCurrentLst.size());

    double dCloseCurrentA = 0;
    double dCloseCurrentB = 0;
    double dCloseCurrentC = 0;
    double dOpenCurrentA = 0;
    double dOpenCurrentB = 0;
    double dOpenCurrentC = 0;

    double dMaxIndex = 0;
    double dSampleSpace = dXRangeMax/MECH_DATA_SHOW_DATA_COUNT;
    quint8 iSwitchState = 0;
    quint8 iSwitchStateA = 0;
    quint8 iSwitchStateB = 0;
    quint8 iSwitchStateC = 0;

    for (int i = 0; i < dXRangeMax; i++)
    {
        if(i < stMechRecord.loopCloseACurrentLst.size())
        {
            dCloseCurrentA = qMax(stMechRecord.loopCloseACurrentLst.at(i), dCloseCurrentA);
        }
        if(i < stMechRecord.loopCloseBCurrentLst.size())
        {
            dCloseCurrentB = qMax(stMechRecord.loopCloseBCurrentLst.at(i), dCloseCurrentB);
        }
        if(i < stMechRecord.loopCloseCCurrentLst.size())
        {
            dCloseCurrentC = qMax(stMechRecord.loopCloseCCurrentLst.at(i), dCloseCurrentC);
        }
        if(i < stMechRecord.loopOpenACurrentLst.size())
        {
            dOpenCurrentA = qMax(stMechRecord.loopOpenACurrentLst.at(i), dOpenCurrentA);
        }
        if(i < stMechRecord.loopOpenBCurrentLst.size())
        {
            dOpenCurrentB = qMax(stMechRecord.loopOpenBCurrentLst.at(i), dOpenCurrentB);
        }
        if(i < stMechRecord.loopOpenCCurrentLst.size())
        {
            dOpenCurrentC = qMax(stMechRecord.loopOpenCCurrentLst.at(i), dOpenCurrentC);
        }

        if(i < stMechRecord.SwitchStateLst.size())
        {
            iSwitchState = qMax(stMechRecord.SwitchStateLst.at(i), iSwitchState);
        }
        if(i < stMechRecord.aSwitchStateLst.size())
        {
            iSwitchStateA = qMax(stMechRecord.aSwitchStateLst.at(i), iSwitchStateA);
        }
        if(i < stMechRecord.bSwitchStateLst.size())
        {
            iSwitchStateB = qMax(stMechRecord.bSwitchStateLst.at(i), iSwitchStateB);
        }
        if(i < stMechRecord.cSwitchStateLst.size())
        {
            iSwitchStateC = qMax(stMechRecord.cSwitchStateLst.at(i), iSwitchStateC);
        }

        if (i >= dMaxIndex)
        {
            jsonDataList.insert(STR_X, QString::number(i * 0.1, 'f', 2));

            if(i < stMechRecord.loopCloseACurrentLst.size())
            {
                jsonDataList.insert(STR_Y, dCloseCurrentA);
                jsonloopCloseA.append(jsonDataList);

                dLoopValueMax = MAX(dLoopValueMax, dCloseCurrentA);
            }
            if(i < stMechRecord.loopCloseBCurrentLst.size())
            {
                jsonDataList.insert(STR_Y, dCloseCurrentB);
                jsonloopCloseB.append(jsonDataList);

                dLoopValueMax = MAX(dLoopValueMax, dCloseCurrentB);
            }
            if(i < stMechRecord.loopCloseCCurrentLst.size())
            {
                jsonDataList.insert(STR_Y, dCloseCurrentC);
                jsonloopCloseC.append(jsonDataList);

                dLoopValueMax = MAX(dLoopValueMax, dCloseCurrentC);
            }
            if(i < stMechRecord.loopOpenACurrentLst.size())
            {
                jsonDataList.insert(STR_Y, dOpenCurrentA);
                jsonloopOpenA.append(jsonDataList);

                dLoopValueMax = MAX(dLoopValueMax, dOpenCurrentA);
            }
            if(i < stMechRecord.loopOpenBCurrentLst.size())
            {
                jsonDataList.insert(STR_Y, dOpenCurrentB);
                jsonloopOpenB.append(jsonDataList);

                dLoopValueMax = MAX(dLoopValueMax, dOpenCurrentB);
            }
            if(i < stMechRecord.loopOpenCCurrentLst.size())
            {
                jsonDataList.insert(STR_Y, dOpenCurrentC);
                jsonloopOpenC.append(jsonDataList);

                dLoopValueMax = MAX(dLoopValueMax, dOpenCurrentC);
            }
            if(i < stMechRecord.SwitchStateLst.size())
            {
                jsonDataList.insert(STR_Y, iSwitchState + 7);
                jsonSwitchState.append(jsonDataList);
            }
            if(i < stMechRecord.aSwitchStateLst.size())
            {
                jsonDataList.insert(STR_Y, iSwitchStateA + 5);
                jsonSwitchStateA.append(jsonDataList);
            }
            if(i < stMechRecord.bSwitchStateLst.size())
            {
                jsonDataList.insert(STR_Y, iSwitchStateB + 3);
                jsonSwitchStateB.append(jsonDataList);
            }
            if(i < stMechRecord.cSwitchStateLst.size())
            {
                jsonDataList.insert(STR_Y, iSwitchStateC + 1);
                jsonSwitchStateC.append(jsonDataList);
            }

            dCloseCurrentA = 0;
            dCloseCurrentB = 0;
            dCloseCurrentC = 0;
            dOpenCurrentA = 0;
            dOpenCurrentB = 0;
            dOpenCurrentC = 0;

            iSwitchState = 0;
            iSwitchStateA = 0;
            iSwitchStateB = 0;
            iSwitchStateC = 0;
            dMaxIndex = dMaxIndex + dSampleSpace;
        }
    }
    jsonDataSerie.insert(STR_DATA_SERIES_NAME, "LoopJoinACurrentLst");
    jsonDataSerie.insert(STR_DATA_SERIES_COLOR, "#FFCC00");
    jsonDataSerie.insert(STR_DATA_LIST, jsonloopCloseA);
    jsonDataSeries.append(jsonDataSerie);

    jsonDataSerie.insert(STR_DATA_SERIES_NAME, "LoopJoinBCurrentLst");
    jsonDataSerie.insert(STR_DATA_SERIES_COLOR, "#00CC00");
    jsonDataSerie.insert(STR_DATA_LIST, jsonloopCloseB);
    jsonDataSeries.append(jsonDataSerie);

    jsonDataSerie.insert(STR_DATA_SERIES_NAME, "LoopJoinCCurrentLst");
    jsonDataSerie.insert(STR_DATA_SERIES_COLOR, "#FF0000");
    jsonDataSerie.insert(STR_DATA_LIST, jsonloopCloseC);
    jsonDataSeries.append(jsonDataSerie);

    jsonDataSerie.insert(STR_DATA_SERIES_NAME, "LoopDivideACurrentLst");
    jsonDataSerie.insert(STR_DATA_SERIES_COLOR, "#FFCC00");
    jsonDataSerie.insert(STR_DATA_LIST, jsonloopOpenA);
    jsonDataSeries.append(jsonDataSerie);

    jsonDataSerie.insert(STR_DATA_SERIES_NAME, "LoopDivideBCurrentLst");
    jsonDataSerie.insert(STR_DATA_SERIES_COLOR, "#00CC00");
    jsonDataSerie.insert(STR_DATA_LIST, jsonloopOpenB);
    jsonDataSeries.append(jsonDataSerie);

    jsonDataSerie.insert(STR_DATA_SERIES_NAME, "LoopDivideCCurrentLst");
    jsonDataSerie.insert(STR_DATA_SERIES_COLOR, "#FF0000");
    jsonDataSerie.insert(STR_DATA_LIST, jsonloopOpenC);
    jsonDataSeries.append(jsonDataSerie);

    jsonAxisInfo.insert(STR_X_DESC, "T");
    jsonAxisInfo.insert(STR_X_RANGE_MAX, ceil(dXRangeMax * 0.1));
    jsonAxisInfo.insert(STR_X_RANGE_MIN, 0);
    jsonAxisInfo.insert(STR_X_UNIT, "ms");

    jsonAxisInfo.insert(STR_Y_DESC, "I");
    jsonAxisInfo.insert(STR_Y_RANGE_MAX, ceil(dLoopValueMax));
    jsonAxisInfo.insert(STR_Y_RANGE_MIN, 0);
    jsonAxisInfo.insert(STR_Y_UNIT, "A");

    jsonCoilCurrent.insert(STR_AXIS_INFO, jsonAxisInfo);
    jsonCoilCurrent.insert(STR_DATA_SERIES, jsonDataSeries);

    jsonCoilDataBady.insert(STR_COIL_CURRENT, jsonCoilCurrent);

    QJsonObject jsonSwitchStatus;
    QJsonArray jsonSwitchDataSeries;

    jsonDataSerie.insert(STR_DATA_SERIES_NAME, "SwitchStateLst");
    jsonDataSerie.insert(STR_DATA_SERIES_COLOR, "#000000");
    jsonDataSerie.insert(STR_DATA_LIST, jsonSwitchState);
    jsonSwitchDataSeries.append(jsonDataSerie);

    jsonDataSerie.insert(STR_DATA_SERIES_NAME, "ASwitchStateLst");
    jsonDataSerie.insert(STR_DATA_SERIES_COLOR, "#FFCC00");
    jsonDataSerie.insert(STR_DATA_LIST, jsonSwitchStateA);
    jsonSwitchDataSeries.append(jsonDataSerie);

    jsonDataSerie.insert(STR_DATA_SERIES_NAME, "BSwitchStateLst");
    jsonDataSerie.insert(STR_DATA_SERIES_COLOR, "#00CC00");
    jsonDataSerie.insert(STR_DATA_LIST, jsonSwitchStateB);
    jsonSwitchDataSeries.append(jsonDataSerie);

    jsonDataSerie.insert(STR_DATA_SERIES_NAME, "CSwitchStateLst");
    jsonDataSerie.insert(STR_DATA_SERIES_COLOR, "#FF0000");
    jsonDataSerie.insert(STR_DATA_LIST, jsonSwitchStateC);
    jsonSwitchDataSeries.append(jsonDataSerie);

    jsonAxisInfo.insert(STR_X_DESC, "T");
    jsonAxisInfo.insert(STR_X_RANGE_MAX, ceil(dXRangeMax * 0.1));
    jsonAxisInfo.insert(STR_X_RANGE_MIN, 0);
    jsonAxisInfo.insert(STR_X_UNIT, "ms");

    jsonAxisInfo.insert(STR_Y_DESC, "");
    jsonAxisInfo.insert(STR_Y_RANGE_MAX, 9);
    jsonAxisInfo.insert(STR_Y_RANGE_MIN, 0);
    jsonAxisInfo.insert(STR_Y_UNIT, "");

    jsonSwitchStatus.insert(STR_AXIS_INFO, jsonAxisInfo);
    jsonSwitchStatus.insert(STR_DATA_SERIES, jsonSwitchDataSeries);

    jsonCoilDataBady.insert(STR_SWITCH_STATUS, jsonSwitchStatus);

    QJsonObject jsonParam;
    jsonParam.insert(STR_SENSOR_TYPE,"MP");
    jsonParam.insert(STR_SAMPLE_DATE,stMechRecord.recordTime.date().toString(STR_DATE_QSTRING_CNA));
    jsonParam.insert(STR_SAMPLE_TIME,stMechRecord.recordTime.time().toString(STR_TIME_QSTRING));
    jsonParam.insert("switchType", QString::number(stMechRecord.eSwitchState));
    jsonParam.insert("switchFunctionType", QString::number(stMechRecord.iMechFunctionType));
    jsonParam.insert("switchOperationType", QString::number(stMechRecord.iMechStorageType));
    jsonParam.insert("MechDataType", QString::number(stMechRecord.iMechDataType));

    jsonParam.insert(STR_ADU_ID,stMechRecord.aduId);
    TestPointInfo stTestPoint;
    ConfigService::instance().getTestPoint(stMechRecord.pointArchiveInfo.strPointGUID, stTestPoint);
    jsonParam.insert(STR_POINT_NAME,stTestPoint.strOutName);

    if (stMechRecord.eSwitchState == MechDefine::STATE_DIVIDE)
    {
        jsonParam.insert("a_open_time", QString::number(stMechRecord.dADividePeriod, 'f', 2));
        jsonParam.insert("b_open_time", QString::number(stMechRecord.dBDividePeriod, 'f', 2));
        jsonParam.insert("c_open_time", QString::number(stMechRecord.dCDividePeriod, 'f', 2));
        jsonParam.insert("open_sync", QString::number(stMechRecord.dDivideSyncPeriod, 'f', 2));

        jsonParam.insert("a_open_coil_charge_time", QString::number(stMechRecord.dOpenALoopUpTime, 'f', 2));
        jsonParam.insert("a_open_coil_cutout_time", QString::number(stMechRecord.dOpenALoopDownTime, 'f', 2));
        jsonParam.insert("a_open_max_current", QString::number(stMechRecord.dOpenAMaxLoopCurrent, 'f', 2));
        jsonParam.insert("a_open_hit_time", QString::number(stMechRecord.dOpenAHitTime, 'f', 2));
        jsonParam.insert("a_open_subswitch_close_time", QString::number(stMechRecord.dOpenASubSwitchCloseTime, 'f', 2));
        if(stMechRecord.iMechFunctionType == 1)
        {
            jsonParam.insert("b_open_coil_charge_time", QString::number(stMechRecord.dOpenBLoopUpTime, 'f', 2));
            jsonParam.insert("b_open_coil_cutout_time", QString::number(stMechRecord.dOpenBLoopDownTime, 'f', 2));
            jsonParam.insert("b_open_max_current", QString::number(stMechRecord.dOpenBMaxLoopCurrent, 'f', 2));
            jsonParam.insert("b_open_hit_time", QString::number(stMechRecord.dOpenBHitTime, 'f', 2));
            jsonParam.insert("b_open_subswitch_close_time", QString::number(stMechRecord.dOpenBSubSwitchCloseTime, 'f', 2));
            jsonParam.insert("c_open_coil_charge_time", QString::number(stMechRecord.dOpenCLoopUpTime, 'f', 2));
            jsonParam.insert("c_open_coil_cutout_time", QString::number(stMechRecord.dOpenCLoopDownTime, 'f', 2));
            jsonParam.insert("c_open_max_current", QString::number(stMechRecord.dOpenCMaxLoopCurrent, 'f', 2));
            jsonParam.insert("c_open_hit_time", QString::number(stMechRecord.dOpenCHitTime, 'f', 2));
            jsonParam.insert("c_open_subswitch_close_time", QString::number(stMechRecord.dOpenCSubSwitchCloseTime, 'f', 2));
        }
    }

    if (stMechRecord.eSwitchState == MechDefine::STATE_JOIN)
    {
        jsonParam.insert("a_close_time", QString::number(stMechRecord.dAJoinPeriod, 'f', 2));
        jsonParam.insert("b_close_time", QString::number(stMechRecord.dBJoinPeriod, 'f', 2));
        jsonParam.insert("c_close_time", QString::number(stMechRecord.dCJoinPeriod, 'f', 2));
        jsonParam.insert("close_sync", QString::number(stMechRecord.dJoinSyncPeriod, 'f', 2));

        jsonParam.insert("a_close_coil_charge_time", QString::number(stMechRecord.dCloseALoopUpTime, 'f', 2));
        jsonParam.insert("a_close_coil_cutout_time", QString::number(stMechRecord.dCloseALoopDownTime, 'f', 2));
        jsonParam.insert("a_close_max_current", QString::number(stMechRecord.dCloseAMaxLoopCurrent, 'f', 2));
        jsonParam.insert("a_close_hit_time", QString::number(stMechRecord.dCloseAHitTime, 'f', 2));
        jsonParam.insert("a_close_subswitch_close_time", QString::number(stMechRecord.dCloseASubSwitchCloseTime, 'f', 2));
        if(stMechRecord.iMechFunctionType == 1)
        {
            jsonParam.insert("b_close_coil_charge_time", QString::number(stMechRecord.dCloseBLoopUpTime, 'f', 2));
            jsonParam.insert("b_close_coil_cutout_time", QString::number(stMechRecord.dCloseBLoopDownTime, 'f', 2));
            jsonParam.insert("b_close_max_current", QString::number(stMechRecord.dCloseBMaxLoopCurrent, 'f', 2));
            jsonParam.insert("b_close_hit_time", QString::number(stMechRecord.dCloseBHitTime, 'f', 2));
            jsonParam.insert("b_close_subswitch_close_time", QString::number(stMechRecord.dCloseBSubSwitchCloseTime, 'f', 2));
            jsonParam.insert("c_close_coil_charge_time", QString::number(stMechRecord.dCloseCLoopUpTime, 'f', 2));
            jsonParam.insert("c_close_coil_cutout_time", QString::number(stMechRecord.dCloseCLoopDownTime, 'f', 2));
            jsonParam.insert("c_close_max_current", QString::number(stMechRecord.dCloseCMaxLoopCurrent, 'f', 2));
            jsonParam.insert("c_close_hit_time", QString::number(stMechRecord.dCloseCHitTime, 'f', 2));
            jsonParam.insert("c_close_subswitch_close_time", QString::number(stMechRecord.dCloseCSubSwitchCloseTime, 'f', 2));
        }
    }

    if ((stMechRecord.eSwitchState == MechDefine::STSTE_DIVIDE_JOIN) || (stMechRecord.eSwitchState == MechDefine::STSTE_JOIN_DIVIDE))
    {
        jsonParam.insert("a_open_time", QString::number(stMechRecord.dADividePeriod, 'f', 2));
        jsonParam.insert("b_open_time", QString::number(stMechRecord.dBDividePeriod, 'f', 2));
        jsonParam.insert("c_open_time", QString::number(stMechRecord.dCDividePeriod, 'f', 2));
        jsonParam.insert("open_sync", QString::number(stMechRecord.dDivideSyncPeriod, 'f', 2));

        jsonParam.insert("a_close_time", QString::number(stMechRecord.dAJoinPeriod, 'f', 2));
        jsonParam.insert("b_close_time", QString::number(stMechRecord.dBJoinPeriod, 'f', 2));
        jsonParam.insert("c_close_time", QString::number(stMechRecord.dCJoinPeriod, 'f', 2));
        jsonParam.insert("close_sync", QString::number(stMechRecord.dJoinSyncPeriod, 'f', 2));

        jsonParam.insert("a_open_coil_charge_time", QString::number(stMechRecord.dOpenALoopUpTime, 'f', 2));
        jsonParam.insert("a_open_coil_cutout_time", QString::number(stMechRecord.dOpenALoopDownTime, 'f', 2));
        jsonParam.insert("a_open_max_current", QString::number(stMechRecord.dOpenAMaxLoopCurrent, 'f', 2));
        jsonParam.insert("a_open_hit_time", QString::number(stMechRecord.dOpenAHitTime, 'f', 2));
        jsonParam.insert("a_open_subswitch_close_time", QString::number(stMechRecord.dOpenASubSwitchCloseTime, 'f', 2));

        jsonParam.insert("a_close_coil_charge_time", QString::number(stMechRecord.dCloseALoopUpTime, 'f', 2));
        jsonParam.insert("a_close_coil_cutout_time", QString::number(stMechRecord.dCloseALoopDownTime, 'f', 2));
        jsonParam.insert("a_close_max_current", QString::number(stMechRecord.dCloseAMaxLoopCurrent, 'f', 2));
        jsonParam.insert("a_close_hit_time", QString::number(stMechRecord.dCloseAHitTime, 'f', 2));
        jsonParam.insert("a_close_subswitch_close_time", QString::number(stMechRecord.dCloseASubSwitchCloseTime, 'f', 2));

        if(stMechRecord.iMechFunctionType == 1)
        {
            jsonParam.insert("b_open_coil_charge_time", QString::number(stMechRecord.dOpenBLoopUpTime, 'f', 2));
            jsonParam.insert("b_open_coil_cutout_time", QString::number(stMechRecord.dOpenBLoopDownTime, 'f', 2));
            jsonParam.insert("b_open_max_current", QString::number(stMechRecord.dOpenBMaxLoopCurrent, 'f', 2));
            jsonParam.insert("b_open_hit_time", QString::number(stMechRecord.dOpenBHitTime, 'f', 2));
            jsonParam.insert("b_open_subswitch_close_time", QString::number(stMechRecord.dOpenBSubSwitchCloseTime, 'f', 2));
            jsonParam.insert("c_open_coil_charge_time", QString::number(stMechRecord.dOpenCLoopUpTime, 'f', 2));
            jsonParam.insert("c_open_coil_cutout_time", QString::number(stMechRecord.dOpenCLoopDownTime, 'f', 2));
            jsonParam.insert("c_open_max_current", QString::number(stMechRecord.dOpenCMaxLoopCurrent, 'f', 2));
            jsonParam.insert("c_open_hit_time", QString::number(stMechRecord.dOpenCHitTime, 'f', 2));
            jsonParam.insert("c_open_subswitch_close_time", QString::number(stMechRecord.dOpenCSubSwitchCloseTime, 'f', 2));
            jsonParam.insert("b_close_coil_charge_time", QString::number(stMechRecord.dCloseBLoopUpTime, 'f', 2));
            jsonParam.insert("b_close_coil_cutout_time", QString::number(stMechRecord.dCloseBLoopDownTime, 'f', 2));
            jsonParam.insert("b_close_max_current", QString::number(stMechRecord.dCloseBMaxLoopCurrent, 'f', 2));
            jsonParam.insert("b_close_hit_time", QString::number(stMechRecord.dCloseBHitTime, 'f', 2));
            jsonParam.insert("b_close_subswitch_close_time", QString::number(stMechRecord.dCloseBSubSwitchCloseTime, 'f', 2));
            jsonParam.insert("c_close_coil_charge_time", QString::number(stMechRecord.dCloseCLoopUpTime, 'f', 2));
            jsonParam.insert("c_close_coil_cutout_time", QString::number(stMechRecord.dCloseCLoopDownTime, 'f', 2));
            jsonParam.insert("c_close_max_current", QString::number(stMechRecord.dCloseCMaxLoopCurrent, 'f', 2));
            jsonParam.insert("c_close_hit_time", QString::number(stMechRecord.dCloseCHitTime, 'f', 2));
            jsonParam.insert("c_close_subswitch_close_time", QString::number(stMechRecord.dCloseCSubSwitchCloseTime, 'f', 2));
        }

        jsonParam.insert("a_switch_shot_time", QString::number(stMechRecord.dASwitchShotTime, 'f', 2));
        jsonParam.insert("b_switch_shot_time", QString::number(stMechRecord.dBSwitchShotTime, 'f', 2));
        jsonParam.insert("c_switch_shot_time", QString::number(stMechRecord.dCSwitchShotTime, 'f', 2));
        jsonParam.insert("a_switch_no_current_time", QString::number(stMechRecord.dASwitchNoCurrentTime, 'f', 2));
        jsonParam.insert("b_switch_no_current_time", QString::number(stMechRecord.dBSwitchNoCurrentTime, 'f', 2));
        jsonParam.insert("c_switch_no_current_time", QString::number(stMechRecord.dCSwitchNoCurrentTime, 'f', 2));
    }

    if (stMechRecord.eSwitchState == MechDefine::STSTE_RECLOSURE)
    {
        jsonParam.insert("a_open_time", QString::number(stMechRecord.dADividePeriod, 'f', 2));
        jsonParam.insert("b_open_time", QString::number(stMechRecord.dBDividePeriod, 'f', 2));
        jsonParam.insert("c_open_time", QString::number(stMechRecord.dCDividePeriod, 'f', 2));
        jsonParam.insert("open_sync", QString::number(stMechRecord.dDivideSyncPeriod, 'f', 2));

        jsonParam.insert("a_close_time", QString::number(stMechRecord.dAJoinPeriod, 'f', 2));
        jsonParam.insert("b_close_time", QString::number(stMechRecord.dBJoinPeriod, 'f', 2));
        jsonParam.insert("c_close_time", QString::number(stMechRecord.dCJoinPeriod, 'f', 2));
        jsonParam.insert("close_sync", QString::number(stMechRecord.dJoinSyncPeriod, 'f', 2));

        jsonParam.insert("a_twice_open_time", QString::number(stMechRecord.dTwiceADividePeriod, 'f', 2));
        jsonParam.insert("b_twice_open_time", QString::number(stMechRecord.dTwiceBDividePeriod, 'f', 2));
        jsonParam.insert("c_twice_open_time", QString::number(stMechRecord.dTwiceCDividePeriod, 'f', 2));
        jsonParam.insert("twice_open_sync", QString::number(stMechRecord.dTwiceDivideSyncPeriod, 'f', 2));

        jsonParam.insert("a_twice_open_coil_charge_time", QString::number(stMechRecord.dTwiceOpenALoopUpTime, 'f', 2));
        jsonParam.insert("a_twice_open_coil_cutout_time", QString::number(stMechRecord.dTwiceOpenALoopDownTime, 'f', 2));
        jsonParam.insert("a_twice_open_max_current", QString::number(stMechRecord.dTwiceOpenAMaxLoopCurrent, 'f', 2));
        jsonParam.insert("a_twice_open_hit_time", QString::number(stMechRecord.dTwiceOpenAHitTime, 'f', 2));
        jsonParam.insert("a_twice_open_subswitch_close_time", QString::number(stMechRecord.dTwiceOpenASubSwitchCloseTime, 'f', 2));

        if(stMechRecord.iMechFunctionType == 1)
        {
            jsonParam.insert("b_open_coil_charge_time", QString::number(stMechRecord.dOpenBLoopUpTime, 'f', 2));
            jsonParam.insert("b_open_coil_cutout_time", QString::number(stMechRecord.dOpenBLoopDownTime, 'f', 2));
            jsonParam.insert("b_open_max_current", QString::number(stMechRecord.dOpenBMaxLoopCurrent, 'f', 2));
            jsonParam.insert("b_open_hit_time", QString::number(stMechRecord.dOpenBHitTime, 'f', 2));
            jsonParam.insert("b_open_subswitch_close_time", QString::number(stMechRecord.dOpenBSubSwitchCloseTime, 'f', 2));
            jsonParam.insert("c_open_coil_charge_time", QString::number(stMechRecord.dOpenCLoopUpTime, 'f', 2));
            jsonParam.insert("c_open_coil_cutout_time", QString::number(stMechRecord.dOpenCLoopDownTime, 'f', 2));
            jsonParam.insert("c_open_max_current", QString::number(stMechRecord.dOpenCMaxLoopCurrent, 'f', 2));
            jsonParam.insert("c_open_hit_time", QString::number(stMechRecord.dOpenCHitTime, 'f', 2));
            jsonParam.insert("c_open_subswitch_close_time", QString::number(stMechRecord.dOpenCSubSwitchCloseTime, 'f', 2));
            jsonParam.insert("b_close_coil_charge_time", QString::number(stMechRecord.dCloseBLoopUpTime, 'f', 2));
            jsonParam.insert("b_close_coil_cutout_time", QString::number(stMechRecord.dCloseBLoopDownTime, 'f', 2));
            jsonParam.insert("b_close_max_current", QString::number(stMechRecord.dCloseBMaxLoopCurrent, 'f', 2));
            jsonParam.insert("b_close_hit_time", QString::number(stMechRecord.dCloseBHitTime, 'f', 2));
            jsonParam.insert("b_close_subswitch_close_time", QString::number(stMechRecord.dCloseBSubSwitchCloseTime, 'f', 2));
            jsonParam.insert("c_close_coil_charge_time", QString::number(stMechRecord.dCloseCLoopUpTime, 'f', 2));
            jsonParam.insert("c_close_coil_cutout_time", QString::number(stMechRecord.dCloseCLoopDownTime, 'f', 2));
            jsonParam.insert("c_close_max_current", QString::number(stMechRecord.dCloseCMaxLoopCurrent, 'f', 2));
            jsonParam.insert("c_close_hit_time", QString::number(stMechRecord.dCloseCHitTime, 'f', 2));
            jsonParam.insert("c_close_subswitch_close_time", QString::number(stMechRecord.dCloseCSubSwitchCloseTime, 'f', 2));

            jsonParam.insert("b_twice_open_coil_charge_time", QString::number(stMechRecord.dTwiceOpenBLoopUpTime, 'f', 2));
            jsonParam.insert("b_twice_open_coil_cutout_time", QString::number(stMechRecord.dTwiceOpenBLoopDownTime, 'f', 2));
            jsonParam.insert("b_twice_open_max_current", QString::number(stMechRecord.dTwiceOpenBMaxLoopCurrent, 'f', 2));
            jsonParam.insert("b_twice_open_hit_time", QString::number(stMechRecord.dTwiceOpenBHitTime, 'f', 2));
            jsonParam.insert("b_twice_open_subswitch_close_time", QString::number(stMechRecord.dTwiceOpenBSubSwitchCloseTime, 'f', 2));

            jsonParam.insert("c_twice_open_coil_charge_time", QString::number(stMechRecord.dTwiceOpenCLoopUpTime, 'f', 2));
            jsonParam.insert("c_twice_open_coil_cutout_time", QString::number(stMechRecord.dTwiceOpenCLoopDownTime, 'f', 2));
            jsonParam.insert("c_twice_open_max_current", QString::number(stMechRecord.dTwiceOpenCMaxLoopCurrent, 'f', 2));
            jsonParam.insert("c_twice_open_hit_time", QString::number(stMechRecord.dTwiceOpenCHitTime, 'f', 2));
            jsonParam.insert("c_twice_open_subswitch_close_time", QString::number(stMechRecord.dTwiceOpenCSubSwitchCloseTime, 'f', 2));
        }
        jsonParam.insert("a_switch_shot_time", QString::number(stMechRecord.dASwitchShotTime, 'f', 2));
        jsonParam.insert("b_switch_shot_time", QString::number(stMechRecord.dBSwitchShotTime, 'f', 2));
        jsonParam.insert("c_switch_shot_time", QString::number(stMechRecord.dCSwitchShotTime, 'f', 2));
        jsonParam.insert("a_switch_no_current_time", QString::number(stMechRecord.dASwitchNoCurrentTime, 'f', 2));
        jsonParam.insert("b_switch_no_current_time", QString::number(stMechRecord.dBSwitchNoCurrentTime, 'f', 2));
        jsonParam.insert("c_switch_no_current_time", QString::number(stMechRecord.dCSwitchNoCurrentTime, 'f', 2));
    }

    insert(STR_COIL_DATA_BADY, jsonCoilDataBady);
    insert(STR_PARAMS, jsonParam);
}

/************************************************
* 函数名:  getMechLoopData
* 输入参数:  stMechRecord -- 机械特性数据
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  机械特性线圈数据
************************************************/
void ChartData::getMechOriginalData(const MechDefine::MechISRecord &stMechRecord)
{
    QJsonObject jsonCoilDataBady;
    QJsonObject jsonAxisInfo;
    QJsonObject jsonDataSerie;
    QJsonObject jsonDataList;
    double dChanValueMax = 0;//图谱为震荡图谱，最大值和最小值得为大于等于1的相反数，不然曲线的位置会发生偏移
    double dXRangeMax = 0;

    QJsonArray jsonChanCurrentA;
    QJsonArray jsonChanCurrentB;
    QJsonArray jsonChanCurrentC;
    dXRangeMax = MAX(stMechRecord.chanACurrentLst.size(), stMechRecord.chanBCurrentLst.size());//获取数据点数
    dXRangeMax = MAX(dXRangeMax, stMechRecord.chanCCurrentLst.size());

    for (int i = 0; i < dXRangeMax; i++)
    {
        jsonDataList.insert(STR_X, QString::number(i * 0.1, 'f', 2));
        if(i < stMechRecord.chanACurrentLst.size())
        {
            jsonDataList.insert(STR_Y, stMechRecord.chanACurrentLst.at(i));
            jsonChanCurrentA.append(jsonDataList);
            dChanValueMax = MAX(dChanValueMax, qAbs(stMechRecord.chanACurrentLst.at(i)));
        }
        if(i < stMechRecord.chanBCurrentLst.size())
        {
            jsonDataList.insert(STR_Y, stMechRecord.chanBCurrentLst.at(i));
            jsonChanCurrentB.append(jsonDataList);
            dChanValueMax = MAX(dChanValueMax, qAbs(stMechRecord.chanBCurrentLst.at(i)));
        }
        if(i < stMechRecord.chanCCurrentLst.size())
        {
            jsonDataList.insert(STR_Y, stMechRecord.chanCCurrentLst.at(i));
            jsonChanCurrentC.append(jsonDataList);
            dChanValueMax = MAX(dChanValueMax, qAbs(stMechRecord.chanCCurrentLst.at(i)));
        }
    }

    QJsonObject jsonOrig;
    QJsonArray jsonOrigDataSeries;

    jsonDataSerie.insert(STR_DATA_SERIES_NAME, "ChanACurrentLst");
    jsonDataSerie.insert(STR_DATA_SERIES_COLOR, "#FFCC00");
    jsonDataSerie.insert(STR_DATA_LIST, jsonChanCurrentA);
    jsonOrigDataSeries.append(jsonDataSerie);

    jsonDataSerie.insert(STR_DATA_SERIES_NAME, "ChanBCurrentLst");
    jsonDataSerie.insert(STR_DATA_SERIES_COLOR, "#00CC00");
    jsonDataSerie.insert(STR_DATA_LIST, jsonChanCurrentB);
    jsonOrigDataSeries.append(jsonDataSerie);

    jsonDataSerie.insert(STR_DATA_SERIES_NAME, "ChanCCurrentLst");
    jsonDataSerie.insert(STR_DATA_SERIES_COLOR, "#FF0000");
    jsonDataSerie.insert(STR_DATA_LIST, jsonChanCurrentC);
    jsonOrigDataSeries.append(jsonDataSerie);

    jsonAxisInfo.insert(STR_X_DESC, "T");
    jsonAxisInfo.insert(STR_X_RANGE_MAX, ceil(dXRangeMax * 0.1));
    jsonAxisInfo.insert(STR_X_RANGE_MIN, 0);
    jsonAxisInfo.insert(STR_X_UNIT, "ms");

    jsonAxisInfo.insert(STR_Y_DESC, "I");
    jsonAxisInfo.insert(STR_Y_RANGE_MAX, dChanValueMax);
    jsonAxisInfo.insert(STR_Y_RANGE_MIN, -dChanValueMax);
    jsonAxisInfo.insert(STR_Y_UNIT, "A");

    jsonOrig.insert(STR_AXIS_INFO, jsonAxisInfo);
    jsonOrig.insert(STR_DATA_SERIES, jsonOrigDataSeries);

    jsonCoilDataBady.insert(STR_ORIG, jsonOrig);
    QJsonObject jsonParam;
    jsonParam.insert(STR_SENSOR_TYPE,"MP");
    jsonParam.insert(STR_SAMPLE_DATE,stMechRecord.recordTime.date().toString(STR_DATE_QSTRING_CNA));
    jsonParam.insert(STR_SAMPLE_TIME,stMechRecord.recordTime.time().toString(STR_TIME_QSTRING));
    jsonParam.insert("switchFunctionType", QString::number(stMechRecord.iMechFunctionType));
    jsonParam.insert("switchOperationType", QString::number(stMechRecord.iMechStorageType));

    insert(STR_COIL_DATA_BADY, jsonCoilDataBady);
    insert(STR_PARAMS, jsonParam);

}

/************************************************
* 函数名:  getMechMechData
* 输入参数:  stMechRecord -- 机械特性数据
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  机械特性电机数据
************************************************/
void ChartData::getMechMechData(const MechDefine::MechISRecord &stMechRecord)
{
    QJsonObject jsonCoilDataBady;
    QJsonObject jsonCoilCurrent;
    QJsonObject jsonAxisInfo;
    QJsonArray jsonDataSeries;
    QJsonObject jsonDataSerie;
    QJsonObject jsonDataList;
    double dMontorValueMax = 0;
    double dXRangeMax = 0;

    QJsonArray jsonChanCurrentA;
    QJsonArray jsonChanCurrentB;
    QJsonArray jsonChanCurrentC;

    dXRangeMax = MAX(stMechRecord.mechChanACurrentLst.size(), stMechRecord.mechChanBCurrentLst.size());//获取数据点数
    dXRangeMax = MAX(dXRangeMax, stMechRecord.mechChanCCurrentLst.size());

    for (int i = 0; i < dXRangeMax; i++)
    {
        jsonDataList.insert(STR_X, QString::number(i * 0.02, 'f', 2));

        if(i < stMechRecord.mechChanACurrentLst.size())
        {
            jsonDataList.insert(STR_Y, stMechRecord.mechChanACurrentLst.at(i));
            jsonChanCurrentA.append(jsonDataList);
            dMontorValueMax = MAX(dMontorValueMax, stMechRecord.mechChanACurrentLst.at(i));
        }
        if(i < stMechRecord.mechChanBCurrentLst.size())
        {
            jsonDataList.insert(STR_Y, stMechRecord.mechChanBCurrentLst.at(i));
            jsonChanCurrentB.append(jsonDataList);
            dMontorValueMax = MAX(dMontorValueMax, stMechRecord.mechChanBCurrentLst.at(i));
        }
        if(i < stMechRecord.mechChanCCurrentLst.size())
        {
            jsonDataList.insert(STR_Y, stMechRecord.mechChanCCurrentLst.at(i));
            jsonChanCurrentC.append(jsonDataList);
            dMontorValueMax = MAX(dMontorValueMax, stMechRecord.mechChanCCurrentLst.at(i));
        }
    }
    jsonDataSerie.insert(STR_DATA_SERIES_NAME, "MechChanACurrentLst");
    jsonDataSerie.insert(STR_DATA_SERIES_COLOR, "#FFCC00");
    jsonDataSerie.insert(STR_DATA_LIST, jsonChanCurrentA);
    jsonDataSeries.append(jsonDataSerie);

    jsonDataSerie.insert(STR_DATA_SERIES_NAME, "MechChanBCurrentLst");
    jsonDataSerie.insert(STR_DATA_SERIES_COLOR, "#00CC00");
    jsonDataSerie.insert(STR_DATA_LIST, jsonChanCurrentB);
    jsonDataSeries.append(jsonDataSerie);

    jsonDataSerie.insert(STR_DATA_SERIES_NAME, "MechChanCCurrentLst");
    jsonDataSerie.insert(STR_DATA_SERIES_COLOR, "#FF0000");
    jsonDataSerie.insert(STR_DATA_LIST, jsonChanCurrentC);
    jsonDataSeries.append(jsonDataSerie);

    jsonAxisInfo.insert(STR_X_DESC, "T");
    jsonAxisInfo.insert(STR_X_RANGE_MAX, ceil(dXRangeMax * 0.02));
    jsonAxisInfo.insert(STR_X_RANGE_MIN, 0);
    jsonAxisInfo.insert(STR_X_UNIT, "s");

    jsonAxisInfo.insert(STR_Y_DESC, "I");
    jsonAxisInfo.insert(STR_Y_RANGE_MAX, ceil(dMontorValueMax));
    jsonAxisInfo.insert(STR_Y_RANGE_MIN, 0);
    jsonAxisInfo.insert(STR_Y_UNIT, "A");

    jsonCoilCurrent.insert(STR_AXIS_INFO, jsonAxisInfo);
    jsonCoilCurrent.insert(STR_DATA_SERIES, jsonDataSeries);

    jsonCoilDataBady.insert(STR_MONTOR_CURRENT, jsonCoilCurrent);
    insert(STR_MONTOR_DATA_BADY, jsonCoilDataBady);

    QJsonObject jsonParam;
    jsonParam.insert(STR_SENSOR_TYPE,"MP");
    jsonParam.insert(STR_SAMPLE_DATE,stMechRecord.recordTime.date().toString(STR_DATE_QSTRING_CNA));
    jsonParam.insert(STR_SAMPLE_TIME,stMechRecord.recordTime.time().toString(STR_TIME_QSTRING));
    jsonParam.insert("switchFunctionType", QString::number(stMechRecord.iMechFunctionType));
    jsonParam.insert("switchOperationType", QString::number(stMechRecord.iMechStorageType));

    if (!stMechRecord.mechChanACurrentLst.isEmpty())
    {
        jsonParam.insert("Chan_A_motor_start_current",QString::number(stMechRecord.dAMechStartCurrent, 'f', 2));
        jsonParam.insert("Chan_A_motor_max_current",QString::number(stMechRecord.dAMaxMechCurrent, 'f', 2));
        jsonParam.insert("Chan_A_storage_time",QString::number(stMechRecord.dAMechStoragePeriod, 'f', 2));
    }
    if (!stMechRecord.mechChanBCurrentLst.isEmpty())
    {
        jsonParam.insert("Chan_B_motor_start_current",QString::number(stMechRecord.dBMechStartCurrent, 'f', 2));
        jsonParam.insert("Chan_B_motor_max_current",QString::number(stMechRecord.dBMaxMechCurrent, 'f', 2));
        jsonParam.insert("Chan_B_storage_time",QString::number(stMechRecord.dBMechStoragePeriod, 'f', 2));
    }

    if (!stMechRecord.mechChanCCurrentLst.isEmpty())
    {
        jsonParam.insert("Chan_C_motor_start_current",QString::number(stMechRecord.dCMechStartCurrent, 'f', 2));
        jsonParam.insert("Chan_C_motor_max_current",QString::number(stMechRecord.dCMaxMechCurrent, 'f', 2));
        jsonParam.insert("Chan_C_storage_time",QString::number(stMechRecord.dCMechStoragePeriod, 'f', 2));
    }

    insert(STR_PARAMS, jsonParam);
}

/************************************************
* 函数名:  getArresterData
* 输入参数:  stArresterRecord -- 避雷器数据
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  避雷器数据
************************************************/
void ChartData::getArresterData(const ArresterRecord &stArresterRecord)
{
    Q_UNUSED(stArresterRecord)
}

/************************************************
* 函数名:  getArresterData
* 输入参数:  stArresterData -- 机械特性数据
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  避雷器数据
************************************************/
void ChartData::getArresterParamData(const ArresterRecord &stArresterRecord, QJsonObject &jsonData)
{
    QString strChannelPhase = "C";
    if ( CHANNEL_PHASE_A == stArresterRecord.eChannelPhase )
    {
        strChannelPhase = "A";
    }
    else if ( CHANNEL_PHASE_B == stArresterRecord.eChannelPhase )
    {
        strChannelPhase = "B";
    }
    if (!stArresterRecord.vecCurrentDataChannel.isEmpty())
    {
        jsonData.insert( "leakageCurrent" + strChannelPhase, QString::number(stArresterRecord.vecCurrentDataChannel.at(0),'f', 3) );
    }
    if (!stArresterRecord.vecVoltageDataChannel.isEmpty())
    {
        jsonData.insert( "referenceVoltage" + strChannelPhase, QString::number(stArresterRecord.vecVoltageDataChannel.at(0),'f', 3) );
    }
    if (!stArresterRecord.vecResistiveCurrentData.isEmpty())
    {
        jsonData.insert( "resistiveCurrent" + strChannelPhase, QString::number(stArresterRecord.vecResistiveCurrentData.at(0),'f', 3) );
    }

    for ( int i = 1; i < stArresterRecord.vecResistiveCurrentData.size(); i++ )
    {
        jsonData.insert( QString("resistiveCurrent%1%2").arg(strChannelPhase).arg(i), QString::number(stArresterRecord.vecResistiveCurrentData.at(i),'f', 1) );
    }

    if (stArresterRecord.datetime.isValid())
    {
        jsonData.insert(STR_SAMPLE_DATE,stArresterRecord.datetime.date().toString(STR_DATE_QSTRING_CNA));
        jsonData.insert(STR_SAMPLE_TIME,stArresterRecord.datetime.time().toString(STR_TIME_QSTRING));
    }

}

/************************************************
* 函数名:  getVibrationData
* 输入参数:  getVibrationData -- 振动数据
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  振动数据
************************************************/
void ChartData::getVibrationTimeDomainData(const VibrationRecord &stVibrationRecord, int iDataType)
{
    //封装json
    QJsonArray dataList;
    QJsonArray xValue;
    QJsonArray arrayLegendData;
    QJsonArray ChartDatas;
    QJsonArray arrayDatas;
    QJsonObject jsondata;
    QVector<double>  vecArray;             //数组
    QString strDataName;
    if ( 1 == iDataType )
    {
        vecArray = stVibrationRecord.stVibrationDataX.vecArray;
        strDataName = "TimeDomainDataX";
    }
    else if ( 2 == iDataType )
    {
        vecArray = stVibrationRecord.stVibrationDataY.vecArray;
        strDataName = "TimeDomainDataY";
    }
    else if ( 3 == iDataType )
    {
        vecArray = stVibrationRecord.stVibrationDataZ.vecArray;
        strDataName = "TimeDomainDataZ";
    }
    else
    {}

    double dTime = 1.0 / ( double )stVibrationRecord.usSampleCountCycle / ( double )stVibrationRecord.ucFrequency * 1000;
    for ( int i = 0; i < vecArray.size(); i++ )
    {
        xValue.append( QString::number(dTime * i,'f', 2) );
        arrayDatas.append( QString::number(vecArray.at(i),'f', 3) );
    }

    jsondata.insert( STR_RECORD_DATA, arrayDatas );
    jsondata.insert( STR_NAME, strDataName );
    jsondata.insert( STR_TYPE, "line" );

    dataList.append( jsondata );

    arrayLegendData.append( strDataName );

    QJsonObject chartData;
    chartData.insert( STR_X_VALUE, xValue );
    chartData.insert( STR_DATA_LIST, dataList );
    chartData.insert( STR_LEGEND_DATA, arrayLegendData );
    chartData.insert( STR_CHART_NAME, strDataName );
    chartData.insert( STR_X_UNIT, "ms" );
    chartData.insert( STR_Y_UNIT, "g" );

    ChartDatas.append( chartData );

    insert("charts", ChartDatas);
}

/************************************************
* 函数名:  getVibrationData
* 输入参数:  getVibrationData -- 振动数据
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  振动数据
************************************************/
void ChartData::getVibrationTimeDomainData(const QString &strPointId, int iDataType)
{

    QVariantList dataList;
    QDateTime startDate;
    QDateTime endDate;
    DBServer::instance().getTrendData(strPointId, startDate, endDate, 100, &dataList);//去数据库获取数据

    //封装json
    QJsonArray recalldataList;
    QJsonArray xValue;
    QJsonArray arrayLegendData;
    QJsonArray ChartDatas;
    QJsonArray arrayDatas;
    QJsonObject jsondata;
    QString strDataName;


    for (int i = 0; i < dataList.size(); i++)
    {
        VibrationRecord stVibrationRecord =  dataList.at(i).value<VibrationRecord>();

        QDateTime dateTime = stVibrationRecord.datetime;
        xValue.append(dateTime.toString(STR_DATE_TIME_QSTRING_CNA));
        if ( 1 == iDataType )
        {
            arrayDatas.append(QString::number((double)stVibrationRecord.stVibrationDataX.dAccelerationAverage, 'f', 3));
            strDataName = "TimeDomainDataX";
        }
        else if ( 2 == iDataType )
        {
            arrayDatas.append(QString::number((double)stVibrationRecord.stVibrationDataY.dAccelerationAverage, 'f', 3));
            strDataName = "TimeDomainDataY";
        }
        else if ( 3 == iDataType )
        {
            arrayDatas.append(QString::number((double)stVibrationRecord.stVibrationDataZ.dAccelerationAverage, 'f', 3));
            strDataName = "TimeDomainDataZ";
        }
        else
        {}
    }

    jsondata.insert( STR_RECORD_DATA, arrayDatas );
    jsondata.insert( STR_NAME, strDataName );
    jsondata.insert( STR_TYPE, "line" );

    recalldataList.append( jsondata );

    arrayLegendData.append( strDataName );

    QJsonObject chartData;
    chartData.insert( STR_X_VALUE, xValue );
    chartData.insert( STR_DATA_LIST, recalldataList );
    chartData.insert( STR_LEGEND_DATA, arrayLegendData );
    chartData.insert( STR_CHART_NAME, strDataName );
    chartData.insert( STR_X_UNIT, "" );
    chartData.insert( STR_Y_UNIT, "mm/s" );

    ChartDatas.append( chartData );

    insert("charts", ChartDatas);
}

/************************************************
* 函数名:  getVibrationData
* 输入参数:  getVibrationData -- 振动数据
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  振动数据
************************************************/
void ChartData::getVibrationFrequencyDomainData(const VibrationRecord &stVibrationRecord, int iDataType)
{
    //封装json
    QJsonArray dataList;
    QJsonArray xValue;
    QJsonArray arrayLegendData;
    QJsonArray ChartDatas;
    QJsonArray arrayDatas;
    QJsonObject jsondata;
    QVector<double>  vecArray;             //数组
    QVector<double>  vecFrequencyDomainArray;             //数组
    QString strDataName;

    QVector<double>  vecComputeArray;             //数组

    if ( 4 == iDataType )
    {
        vecComputeArray = stVibrationRecord.stVibrationDataX.vecArray;
        strDataName = "FrequencyDomainDataX";
    }
    else if ( 5 == iDataType )
    {
        vecComputeArray = stVibrationRecord.stVibrationDataY.vecArray;
        strDataName = "FrequencyDomainDataY";
    }
    else if ( 6 == iDataType )
    {
        vecComputeArray = stVibrationRecord.stVibrationDataZ.vecArray;
        strDataName = "FrequencyDomainDataZ";
    }
    else
    {}

    SignalAlgorithm::FFT( vecComputeArray, stVibrationRecord.ucFrequency * stVibrationRecord.usSampleCountCycle, vecFrequencyDomainArray, vecArray );

    for ( int i = 0; i < vecArray.size(); i++ )
    {
        xValue.append( QString::number(vecFrequencyDomainArray.at(i), 'f', 3) );
        arrayDatas.append( QString::number(vecArray.at(i),'f', 3) );
    }

    jsondata.insert( STR_RECORD_DATA, arrayDatas );
    jsondata.insert( STR_NAME, strDataName );
    jsondata.insert( STR_TYPE, "line" );

    dataList.append( jsondata );

    arrayLegendData.append( strDataName );

    QJsonObject chartData;
    chartData.insert( STR_X_VALUE, xValue );
    chartData.insert( STR_DATA_LIST, dataList );
    chartData.insert( STR_LEGEND_DATA, arrayLegendData );
    chartData.insert( STR_CHART_NAME, strDataName );
    chartData.insert( STR_X_UNIT, "Hz" );
    chartData.insert( STR_Y_UNIT, "g" );

    ChartDatas.append( chartData );
    insert("charts", ChartDatas);
}

/************************************************
* 函数名:  getVibrationData
* 输入参数:  getVibrationData -- 振动数据
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  振动数据
************************************************/
void ChartData::getVibrationParamData(const VibrationRecord &stVibrationRecord)
{    
//    insert( STR_AMP_AVG_X, stVibrationRecord.stVibrationDataX.iAmplitudeAverage );
//    insert( STR_AMP_MAX_X, stVibrationRecord.stVibrationDataX.iAmplitudeMax );
    insert( STR_ACC_AVG_X, QString::number(stVibrationRecord.stVibrationDataX.dAccelerationAverage,'f', 3) );
//    insert( STR_ACC_MAX_X, QString::number(stVibrationRecord.stVibrationDataX.dAccelerationMax,'f', 3) );
    QJsonArray arrayMaxFreqX;
    for (int i = 0; i < stVibrationRecord.stVibrationDataX.vecMaxFreqs.size(); i++)
    {
        arrayMaxFreqX.append(QString::number(stVibrationRecord.stVibrationDataX.vecMaxFreqs.at(i),'f', 1));
    }
    insert( STR_MAX_FREQ_X, arrayMaxFreqX );

//    insert( STR_AMP_AVG_Y, stVibrationRecord.stVibrationDataY.iAmplitudeAverage );
//    insert( STR_AMP_MAX_Y, stVibrationRecord.stVibrationDataY.iAmplitudeMax );
    insert( STR_ACC_AVG_Y, QString::number(stVibrationRecord.stVibrationDataY.dAccelerationAverage,'f', 3) );
//    insert( STR_ACC_MAX_Y, QString::number(stVibrationRecord.stVibrationDataY.dAccelerationMax,'f', 3) );
    QJsonArray arrayMaxFreqY;
    for (int i = 0; i < stVibrationRecord.stVibrationDataY.vecMaxFreqs.size(); i++)
    {
        arrayMaxFreqY.append(QString::number(stVibrationRecord.stVibrationDataY.vecMaxFreqs.at(i),'f', 1));
    }
    insert( STR_MAX_FREQ_Y, arrayMaxFreqY );

//    insert( STR_AMP_AVG_Z, stVibrationRecord.stVibrationDataZ.iAmplitudeAverage );
//    insert( STR_AMP_MAX_Z, stVibrationRecord.stVibrationDataZ.iAmplitudeMax );
    insert( STR_ACC_AVG_Z, QString::number(stVibrationRecord.stVibrationDataZ.dAccelerationAverage,'f', 3) );
//    insert( STR_ACC_MAX_Z, QString::number(stVibrationRecord.stVibrationDataZ.dAccelerationMax,'f', 3) );
    QJsonArray arrayMaxFreqZ;
    for (int i = 0; i < stVibrationRecord.stVibrationDataZ.vecMaxFreqs.size(); i++)
    {
        arrayMaxFreqZ.append(QString::number(stVibrationRecord.stVibrationDataZ.vecMaxFreqs.at(i),'f', 1));
    }
    insert( STR_MAX_FREQ_Z, arrayMaxFreqZ );

    insert(STR_SENSOR_TYPE,"Vibration");
    insert(STR_SAMPLE_DATE,stVibrationRecord.datetime.date().toString(STR_DATE_QSTRING_CNA));
    insert(STR_SAMPLE_TIME,stVibrationRecord.datetime.time().toString(STR_TIME_QSTRING));

    insert(STR_ADU_ID, stVibrationRecord.strADUID);
    TestPointInfo stTestPoint;
    ConfigService::instance().getTestPoint(stVibrationRecord.stPointArchiveInfo.strPointGUID, stTestPoint);
    insert(STR_POINT_NAME,stTestPoint.strOutName);
}

/************************************************
* 函数名:  packageVibrationData
* 输入参数:  stArresterRecord -- 避雷器数据
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  避雷器数据
************************************************/
void ChartData::packageVibrationData(const QString &strPointId)
{
    QVariantList dataList;
    QDateTime startDate;
    QDateTime endDate;
    DBServer::instance().getTrendData(strPointId, startDate, endDate, 100, &dataList);//去数据库获取数据

    QJsonArray jsonXValues;
    QJsonArray jsonVbrspdValuesx;
    QJsonArray jsonVbrspdValuesy;
    QJsonArray jsonVbrspdValuesz;


    for (int i = 0; i < dataList.size(); i++)
    {
        VibrationRecord stVibrationRecord =  dataList.at(i).value<VibrationRecord>();

        QDateTime dateTime = stVibrationRecord.datetime;
        jsonXValues.append(dateTime.toString(STR_DATE_TIME_QSTRING_CNA));
        jsonVbrspdValuesx.append(QString::number((double)stVibrationRecord.stVibrationDataX.dAccelerationAverage, 'f', 3));
        jsonVbrspdValuesy.append(QString::number((double)stVibrationRecord.stVibrationDataY.dAccelerationAverage, 'f', 3));
        jsonVbrspdValuesz.append(QString::number((double)stVibrationRecord.stVibrationDataZ.dAccelerationAverage, 'f', 3));
    }

    if(dataList.size())
    {
        insert(STR_SRART_DATE, dataList.first().value<VibrationRecord>().datetime.toString(STR_DATE_TIME_QSTRING_CNA));
        insert(STR_END_DATE, dataList.last().value<VibrationRecord>().datetime.toString(STR_DATE_TIME_QSTRING_CNA));
    }

    QJsonArray jsondatas;
    QJsonObject jsondata;
    jsondata.insert( "data", jsonVbrspdValuesx );
    jsondata.insert( "channelId", 1 );
    jsondata.insert( "name",  "Vbrspdx");
    jsondata.insert( "type", "line" );
    jsondatas.append( jsondata );

    jsondata.insert( "data", jsonVbrspdValuesy );
    jsondata.insert( "channelId", 2 );
    jsondata.insert( "name",  "Vbrspdy");
    jsondata.insert( "type", "line" );
    jsondatas.append( jsondata );

    jsondata.insert( "data", jsonVbrspdValuesz );
    jsondata.insert( "channelId", 3 );
    jsondata.insert( "name",  "Vbrspdz");
    jsondata.insert( "type", "line" );
    jsondatas.append( jsondata );

    QJsonArray arrayLegendData;
    arrayLegendData.append("Vbrspdx");
    arrayLegendData.append("Vbrspdy");
    arrayLegendData.append("Vbrspdz");

    QJsonObject jsonResult;
    QJsonArray jsonResults;
    jsonResult.insert( STR_X_VALUE, jsonXValues );
    jsonResult.insert( STR_DATA_LIST, jsondatas );
    jsonResult.insert( STR_LEGEND_DATA, arrayLegendData );
    jsonResult.insert( STR_CHART_NAME, "VibrationSpeed" );
    jsonResult.insert( STR_X_UNIT, "" );
    jsonResult.insert( STR_Y_UNIT, "mm/s" );

    jsonResults.append( jsonResult );
    insert("data", jsonResults);
}

/************************************************
* 函数名:  getArresterData
* 输入参数:  stArresterRecord -- 避雷器数据
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  避雷器数据
************************************************/
void ChartData::packageArresterData(const QString &strPointId, const QDateTime &startDate, const QDateTime &endDate)
{
    QList<QDateTime> listChannelADateTime;
    QList<QDateTime> listChannelBDateTime;
    QList<QDateTime> listChannelCDateTime;
    QList<float> listChannelAData;
    QList<float> listChannelBData;
    QList<float> listChannelCData;
    DBServer::instance().getArresterTrendData(strPointId, CHANNEL_PHASE_A, startDate, endDate, listChannelADateTime, listChannelAData );
    DBServer::instance().getArresterTrendData(strPointId, CHANNEL_PHASE_B, startDate, endDate, listChannelBDateTime, listChannelBData );
    DBServer::instance().getArresterTrendData(strPointId, CHANNEL_PHASE_C, startDate, endDate, listChannelCDateTime, listChannelCData );

    QMap<QDateTime, arresterTrendData> mapChannelAData;
    QMap<QDateTime,int>mapDateData;
    for (int i = 0; i < listChannelADateTime.size(); i++ )
    {
        arresterTrendData starresterTrendData;
        starresterTrendData.fLeakageCurrent = listChannelAData.at(i * 2);
        starresterTrendData.fResistiveCurrent = listChannelAData.at(i * 2 + 1);
        mapChannelAData.insert(listChannelADateTime.at(i), starresterTrendData);
        mapDateData.insert(listChannelADateTime.at(i), i);
    }
    QMap<QDateTime, arresterTrendData> mapChannelBData;
    for (int i = 0; i < listChannelBDateTime.size(); i++ )
    {
        arresterTrendData starresterTrendData;
        starresterTrendData.fLeakageCurrent = listChannelBData.at(i * 2);
        starresterTrendData.fResistiveCurrent = listChannelBData.at(i * 2 + 1);
        mapChannelBData.insert(listChannelBDateTime.at(i),starresterTrendData);
        mapDateData.insert(listChannelBDateTime.at(i), i);
    }

    QMap<QDateTime, arresterTrendData> mapChannelCData;
    for (int i = 0; i < listChannelCDateTime.size(); i++ )
    {
        arresterTrendData starresterTrendData;
        starresterTrendData.fLeakageCurrent = listChannelCData.at(i * 2);
        starresterTrendData.fResistiveCurrent = listChannelCData.at(i * 2 + 1);
        mapChannelCData.insert(listChannelCDateTime.at(i),starresterTrendData);
        mapDateData.insert(listChannelCDateTime.at(i), i);
    }

    QJsonArray jsonXValues;
    QJsonArray jsonYACValues;
    QJsonArray jsonYBCValues;
    QJsonArray jsonYCCValues;
    QJsonArray jsonYAVValues;
    QJsonArray jsonYBVValues;
    QJsonArray jsonYCVValues;

    if(mapDateData.size())
    {
        insert(STR_SRART_DATE, mapDateData.firstKey().toString(STR_DATE_TIME_QSTRING_CNA));
        insert(STR_END_DATE, mapDateData.lastKey().toString(STR_DATE_TIME_QSTRING_CNA));
    }

    for (int i = 0; i < mapDateData.size(); i++)
    {
        QDateTime dateTime = mapDateData.keys().at(i);
        jsonXValues.append(dateTime.toString(STR_DATE_TIME_QSTRING_CNA));

        jsonYACValues.append(QString::number((double)mapChannelAData.value(dateTime).fLeakageCurrent, 'f', 3));
        jsonYBCValues.append(QString::number((double)mapChannelBData.value(dateTime).fLeakageCurrent, 'f', 3));
        jsonYCCValues.append(QString::number((double)mapChannelCData.value(dateTime).fLeakageCurrent, 'f', 3));


        jsonYAVValues.append(QString::number((double)mapChannelAData.value(dateTime).fResistiveCurrent, 'f', 3));
        jsonYBVValues.append(QString::number((double)mapChannelBData.value(dateTime).fResistiveCurrent, 'f', 3));
        jsonYCVValues.append(QString::number((double)mapChannelCData.value(dateTime).fResistiveCurrent, 'f', 3));
    }
    QJsonArray jsondatas;
    QJsonObject jsondata;
    jsondata.insert( "data", jsonYACValues );
    jsondata.insert( "channelId", 0 );
    jsondata.insert( "name",  "ResistiveCurrentA");
    jsondata.insert( "type", "line" );
    jsondatas.append( jsondata );

    jsondata.insert( "data", jsonYBCValues );
    jsondata.insert( "channelId", 1 );
    jsondata.insert( "name",  "ResistiveCurrentB");
    jsondata.insert( "type", "line" );
    jsondatas.append( jsondata );

    jsondata.insert( "data", jsonYCCValues );
    jsondata.insert( "channelId", 2 );
    jsondata.insert( "name",  "ResistiveCurrentC");
    jsondata.insert( "type", "line" );
    jsondatas.append( jsondata );

    QJsonArray arrayLegendData;
    arrayLegendData.append("ResistiveCurrentA");
    arrayLegendData.append("ResistiveCurrentB");
    arrayLegendData.append("ResistiveCurrentC");

    QJsonObject jsonResult;
    QJsonArray jsonResults;
    jsonResult.insert( STR_X_VALUE, jsonXValues );
    jsonResult.insert( STR_DATA_LIST, jsondatas );
    jsonResult.insert( STR_LEGEND_DATA, arrayLegendData );
    jsonResult.insert( STR_CHART_NAME, "ResistiveCurrent" );
    jsonResult.insert( STR_X_UNIT, "ms" );
    jsonResult.insert( STR_Y_UNIT, "mA" );

    QJsonArray jsonResistiveDatas;

    jsonResults.append( jsonResult );
    jsondata.insert( "data", jsonYAVValues );
    jsondata.insert( "channelId", 0 );
    jsondata.insert( "name",  "leakageCurrentA");
    jsondata.insert( "type", "line" );
    jsonResistiveDatas.append( jsondata );

    jsondata.insert( "data", jsonYBVValues );
    jsondata.insert( "channelId", 1 );
    jsondata.insert( "name",  "leakageCurrentB");
    jsondata.insert( "type", "line" );
    jsonResistiveDatas.append( jsondata );

    jsondata.insert( "data", jsonYCVValues );
    jsondata.insert( "channelId", 2 );
    jsondata.insert( "name",  "leakageCurrentC");
    jsondata.insert( "type", "line" );
    jsonResistiveDatas.append( jsondata );

    QJsonArray LegendDatas;
    LegendDatas.append("leakageCurrentA");
    LegendDatas.append("leakageCurrentB");
    LegendDatas.append("leakageCurrentC");

    jsonResult.insert( STR_X_VALUE, jsonXValues );
    jsonResult.insert( STR_DATA_LIST, jsonResistiveDatas );
    jsonResult.insert( STR_LEGEND_DATA, LegendDatas );
    jsonResult.insert( STR_CHART_NAME, "leakageCurrent" );
    jsonResult.insert( STR_X_UNIT, "ms" );
    jsonResult.insert( STR_Y_UNIT, "mA" );
 //   jsonParam.insert(STR_ADU_ID, stHumidityRecord.aduId);
  //  jsonParam.insert(STR_POINT_NAME, stHumidityRecord.pointArchiveInfo.strPointName);



    jsonResults.append( jsonResult );
    insert("data", jsonResults);
}


/************************************************
* 函数名:  videoData
* 输入参数:  hkRecord -- 视频数据
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  视频数据
************************************************/
void ChartData::videoData(const HkVideoRecord& hkRecord)
{
    QJsonObject jsonVideo;
    jsonVideo.insert(MAXTEMP, hkRecord.maxTemperature);
    jsonVideo.insert(POSX, hkRecord.maxTempraturePostionX);
    jsonVideo.insert(POSY, hkRecord.maxTempraturePostionY);
    if(hkRecord.imagePath.size() >= 1)
    {
        jsonVideo.insert(PIC0, PIC_PATH +hkRecord.imagePath[0]);
    }

    if(hkRecord.imagePath.size() >= 2)
    {
        jsonVideo.insert(PIC1, PIC_PATH +hkRecord.imagePath[1]);
    }
    jsonVideo.insert(CAPTURETYPE, hkRecord.captureType);
    jsonVideo.insert(STR_SAMPLE_DATE, hkRecord.recordTime.date().toString(STR_DATE_QSTRING_CNA));
    jsonVideo.insert(STR_SAMPLE_TIME, hkRecord.recordTime.time().toString(STR_TIME_QSTRING));
    jsonVideo.insert(STR_ADU_ID,hkRecord.aduId);
    jsonVideo.insert(STR_POINT_NAME,hkRecord.pointArchiveInfo.strPointName);
    jsonVideo.insert(REGION_INFO,hkRecord.strRegionInfo);
    insert(STR_PARAMS, jsonVideo);
}

/************************************************
* 函数名:  getTrendData
* 输入参数:   strPointId -- 测点类型
*           eChannelType  通道类型
*           strState 数据状态
*           startDate 开始时间
*           endDate 结束时间
*           page  页面
*           size  每页个数
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取指定趋势图谱数据
************************************************/
void ChartData::getTrendData(const QString& strPointId, ADUChannelType eChannelType, const QString& strState,
                             const QDateTime& startDate, const QDateTime& endDate,
                             const int page, const int size)
{
    //获取数据库趋势数据
    QList<QDateTime> dateList;   //日期列表
    QList<float> dataList;       //数据列表
    if(!DBServer::instance().getTrendData(strPointId, eChannelType, startDate, endDate, dateList, dataList, SHRT_MIN))
    {
        PDS_SYS_WARNING_LOG("getTrendData fail");
        return;
    }
    if(dateList.size() != dataList.size())
    {
        PDS_SYS_WARNING_LOG("dateList.size() != dataList.size()");
        return;
    }

    //选择需要数据
    QList<TrendChartData> listData;
    for(int i = 0; i < dataList.size(); ++i)
    {
        TrendChartData data;
        data.data = (EnvStatus)dataList[i];
        data.date = dateList[i];
        if(ALL_DATA == strState)
        {
            listData.append(data);
        }
        else if(ALARM_DATA == strState && EnvStatus::ALARM == data.data)
        {
            listData.append(data);
        }
        else if(NORMAL_DATA == strState && EnvStatus::NORMAL == data.data)
        {
            listData.append(data);
        }
        else
        {
            PDS_SYS_WARNING_LOG("unknow state %s", strState.toLatin1().data());
        }
    }

    //排序
    std::sort(listData.begin(), listData.end(),
              [](const TrendChartData &lhs, const TrendChartData &rhs){return lhs.date >= rhs.date;});

    QJsonArray jsonDateValues;
    QJsonArray jsonDataValues;
    int curIndex = (page - 1)* size;
    int maxIndexj = curIndex + size;
    for (; curIndex < maxIndexj; ++curIndex)
    {
        if(curIndex >= listData.size())
        {
            break;
        }
        jsonDateValues.append(listData.at(curIndex).date.toString(STR_DATE_TIME_QSTRING_CNA));
        if(EnvStatus::NORMAL == listData.at(curIndex).data)
        {
            jsonDataValues.append(NORMAL_DATA);
        }
        else
        {
            jsonDataValues.append(ALARM_DATA);
        }
    }
    QJsonObject jsonDataChart;
    insert(STR_DATA_CHART, jsonDataChart);
    insert(STR_PAGE, page);
    insert(STR_TOTAL, listData.size());
    insert(STR_STATETYPE, strState);
    jsonDataChart.insert(STR_VALUE, jsonDataValues);
    jsonDataChart.insert(STR_DATE_DATE, jsonDateValues);
    insert(STR_DATA_CHART, jsonDataChart);
}

/*
* Copyright (c) 2017.7，南京华乘电气科技有限公司
* All rights reserved.
*
* processdata.h
*
* 初始版本：1.0
* 作者：wujun
* 创建日期：2017年07月17日
* 摘要：解析处理数据

* 当前版本：1.0
*/
#ifndef __PROCESSDATA_H_
#define __PROCESSDATA_H_

#include <QObject>
#include "syncdefine.h"
#include "testdatastruct.h"
#include "patroldefines.h"
using namespace DataSync;


class DataProcesser: public QObject
{
    Q_OBJECT

public:
    // 获取命令字类型
    CommandType getCmdType(const QByteArray &data);
    // 解析连接请求
    QString parseConnReq(const QByteArray &data);
    // 连接应答
    //void packConnRep(bool bAgree, QByteArray &data);

    /*************************************************
    函数名： packConnRep
    输入参数：reply--连接应答内容
    输出参数：data--序列化后的数据
    返回值： NULL
    功能： 组包连接应答
    *****************************************************/
    void packConnRep(QByteArray &data, const ConnReply &reply);

    /*************************************************
    函数名： packConfigRep
    输入参数：cNode--配置文件应答内容
    输出参数：data--序列化后的数据
    返回值： NULL
    功能： 组包配置文件应答
    *****************************************************/
    void packConfigRep(QByteArray &data, const ConfigNode &cNode);

    void packConfigRep(QByteArray &data, const ConfigNode &cNode, const QByteArray &baConfig);

    /*************************************************
    函数名： parseConfigRep
    输入参数： data--數據內容
    输出参数： cNode--反序列化后的結果
    返回值： NULL
    功能： 解析配置內容應答
    *****************************************************/
    void parseConfigRep(const QByteArray &data, ConfigNode &cNode);

    /*************************************************
    函数名： parseConfigReq
    输入参数： data--數據內容
    输出参数： NULL
    返回值： 站点id
    功能： 解析配置内容请求
    *****************************************************/
    QString parseConfigReq(const QByteArray &data);

    /*************************************************
    函数名： parseSyncInfoReq
    输入参数：data--序列化后的数据
    输出参数：param--同步参数
    返回值： NULL
    功能： 解析同步数据信息请求
    *****************************************************/
    void parseSyncInfoReq(const QByteArray &data, PatrolServiceNS::SyncDataParam &param);

    /*************************************************
    函数名： parseSyncInfoReq
    输入参数：data--序列化后的数据
    输出参数： info--测点通道信息；startTime--起始时间；endTime--终止时间；
    返回值： NULL
    功能： 解析同步数据信息请求
    *****************************************************/
    void parseSyncInfoReq(const QByteArray &data, QMap<QString, QList<ADUChannelType> > &info, QDateTime &statTime, QDateTime &endTime );

    /*************************************************
    函数名： packSyncInfoRep
    输入参数：param--同步参数
    输出参数：data--序列化后的数据
    返回值： NULL
    功能： 组包同步数据信息应答
    *****************************************************/
    void packSyncInfoRep(QByteArray &data, const QList<SyncInfoReply> &reply);

    /*************************************************
    函数名： packSyncInfoRep
    输入参数： reply--数据信息应答
    输出参数： data--序列化后的数据
    返回值： NULL
    功能： 组包同步数据信息应答
    *****************************************************/
    void packSyncInfoRep(QByteArray &data, QMap<QString, QMap<ADUChannelType, QList<int> > > reply);

    void packSyncFileContent(QByteArray &data, const QByteArray &content);

    /*************************************************
    函数名： parseSyncDataReq
    输入参数：data--序列化后的数据
    输出参数：vecGID--globalID列表
    返回值： NULL
    功能： 解析取数据请求
    *****************************************************/
    void parseSyncDataReq(const QByteArray &data, QVector<quint64> vecGID);

    /*************************************************
    函数名： parseSyncDataReq
    输入参数：data--序列化后的数据
    输出参数：pointId--测点id；
             eType--通道类型；
             ids--自增序号列表
    返回值： NULL
    功能： 解析取数据请求
    *****************************************************/
    void parseSyncDataReq(const QByteArray &data, QString &pointId, ADUChannelType &eType, QList<int> &ids);

    /*************************************************
    函数名： packSyncDataRep
    输入参数：data--序列化后的数据
    输出参数：reply--单条数据记录
    返回值： NULL
    功能： 组包取数据应答(单条数据)
    *****************************************************/
    void packSyncDataRep(QByteArray &data, const TbData &reply);

    /*************************************************
    函数名： parseDbDataRep
    输入参数： data--序列化后的数据内容
    输出参数： reply--反序列化后的結果
    返回值： NULL
    功能： 解析数据库记录内容（测试）
    *****************************************************/
    void parseDbDataRep(const QByteArray &data, TbData &reply);

    /*************************************************
    函数名： parseSyncDataReq
    输入参数：data--序列化后的数据
    输出参数：param--同步参数
    返回值： NULL
    功能： 解析取数据请求
    *****************************************************/
    //void parseSyncDataReq(const QByteArray &data, SyncDataParam &param);

};

#endif

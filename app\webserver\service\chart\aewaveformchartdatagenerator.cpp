#include "aewaveformchartdatagenerator.h"
#include "dbrecord.h"
#include "chartjsonconstants.h"
#include "configservice.h"
#include "log.h"

AEWaveformChartDataGenerator::AEWaveformChartDataGenerator(const AERecord& aeRecord)
    : m_aeRecord(aeRecord)
{
}

QJsonObject AEWaveformChartDataGenerator::generateChartData()
{
    QJsonObject chartJson;

    chartJson.insert(ChartJsonConstants::kTitle<PERSON>ey, ChartJsonConstants::kAeWaveformTitle);

    chartJson.insert(ChartJsonConstants::kTriggerKey, generateTriggerInfo());
    chartJson.insert(ChartJsonConstants::kAxisInfoKey, generateAxisInfo());
    chartJson.insert(ChartJsonConstants::kSeriesKey, generateSeriesData());


    return chartJson;
}

QJsonObject AEWaveformChartDataGenerator::generateAxisInfo()
{
    QJsonObject axisInfo;

    if(m_aeRecord.sampleRate <= 0)
    {
        logError("AEWaveformChartDataGenerator::generateAxisInfo error,Invalid sample rate: ") << m_aeRecord.sampleRate;
        return axisInfo;
    }
   
    axisInfo.insert(ChartJsonConstants::kXDescKey, ChartJsonConstants::kAeTimeDesc);

    axisInfo.insert(ChartJsonConstants::kXRangeMinKey, 0);

    const int dataCount = m_aeRecord.waveformData.size(); // 采样数据点数
    const double xMax = 1.0 / m_aeRecord.sampleRate * dataCount * m_aeRecord.ucPwrFre; // X轴最大时间
    //x轴上限向上取整
    axisInfo.insert(ChartJsonConstants::kXRangeMaxKey, std::ceil(xMax));
    axisInfo.insert(ChartJsonConstants::kXUnitKey, ChartJsonConstants::kAeTimeUnit);

    axisInfo.insert(ChartJsonConstants::kYDescKey, ChartJsonConstants::kAeAmplitudeDesc);
    axisInfo.insert(ChartJsonConstants::kYRangeMinKey, m_aeRecord.fAmpLowerLimit);
    axisInfo.insert(ChartJsonConstants::kYRangeMaxKey, m_aeRecord.fAmpUpperLimit);

    axisInfo.insert(ChartJsonConstants::kYUnitKey, ConfigService::instance().getDataUnitString(m_aeRecord.eAmpUnit));

    return axisInfo;
}

QJsonObject AEWaveformChartDataGenerator::generateTriggerInfo()
{
    QJsonObject trigger;

    trigger.insert(ChartJsonConstants::kDescKey, ChartJsonConstants::kAeTriggerDesc);
    trigger.insert(ChartJsonConstants::kValueKey, 0); //波形没有触发幅值
    trigger.insert(ChartJsonConstants::kColorKey, "");

    return trigger;
}

QJsonArray AEWaveformChartDataGenerator::generateSeriesData()
{
    QJsonArray seriesArray;
    QJsonObject seriesObject;
    QJsonArray dataList;

    const int dataCount = m_aeRecord.waveformData.size(); // 采样数据点数
    const double xMax = 1.0 / m_aeRecord.sampleRate * dataCount * m_aeRecord.ucPwrFre; // X轴最大时间

    float xInterval = static_cast<float>(xMax) / dataCount;

    for (int i = 0; i < dataCount; ++i) {
        QJsonArray point;
        float currentTime = i * xInterval;
        point.append(currentTime); // X: 时间 (T)
        point.append(m_aeRecord.waveformData.at(i));  // Y: 幅值
        dataList.append(point);
    }

    seriesObject.insert(ChartJsonConstants::kDataListKey, dataList);
    seriesArray.append(seriesObject);
    return seriesArray;
}

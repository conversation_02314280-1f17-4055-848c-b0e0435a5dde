/*
* Copyright (c) 2017.7，南京华乘电气科技有限公司
* All rights reserved.
*
* syncmanager.h
*
* 初始版本：1.0
* 作者：wujun
* 创建日期：2017年07月17日
* 摘要：数据同步管理器，包含同步业务处理流程

* 当前版本：1.0
*/
#ifndef __SYNCMANAGER_H_
#define __SYNCMANAGER_H_

#include <QObject>
#include <QMap>
#include <QList>
#include <QVariant>
#include <QMutex>
#include <QThread>
#include <QDebug>
#include <QSharedPointer>
#include "syncdefine.h"
#include "protocoldefine.h"
#include "processdata.h"



class AbstractComm;
class IProtocolBean;
class SyncServer;
class CMComm;

#define SYNCMANAGER_MSG
// 打印日志
inline void outputMsg(QString msg)
{
#ifdef SYNCMANAGER_MSG
    qDebug() << "syncmanager: " << msg;
#endif
}

class SyncManager: public QObject
{
    Q_OBJECT

public:

    /*************************************************
    函数名： initInstance
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 初始化对象实例
    *************************************************************/
    static void initInstance(void);

    /*************************************************
    函数名： instance
    输入参数： NULL
    输出参数： NULL
    返回值： 管理器实例
    功能： 获取对象实例
    *************************************************************/
    static SyncManager &instance();

    /*************************************************
    函数名： ~MonitorManager
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 析构对象
    *************************************************************/
    ~SyncManager();

    /*************************************************
    函数名： sendData
    输入参数：data 需要发送的数据
             stParam 协议参数
    输出参数： NULL
    返回值： true-发送成功 false-发送失败
    功能： 阻塞调用，发送数据
    *************************************************************/
    bool sendData(const QByteArray &data, const ProtocolParam &stParam);

private:

    /*************************************************
    函数名： createCommunicateLink
    输入参数： linkType--链路方式
    输出参数： NULL
    返回值： 结果状态--true表示成功
    功能： 建立通讯链路
    *************************************************************/
    bool createCommunicateLink(const DataSync::LinkType &linkType);

signals:
    /*************************************************
    参数： sExcp 异常信息
    功能： 发送异常信号
    *************************************************/
    void sigException(SyncException sExcp);

    //更改站点MD5，发送信号到数据库
    void sigStationMD5(const QString& newMD5);


public slots:
    /*************************************************
    函数名： onDataToBusinessLayer
    输入参数： stParam--协议参数
             data--接收到的数据
    输出参数： NULL
    返回值： NULL
    功能： 收到数据并处理
    *************************************************************/
    void onDataToBusinessLayer(ProtocolParam stParam, const QByteArray &data);


    //test receive file
    //void onDataRead( void );
private:

    /*************************************************
    函数名： SyncManager
    功能： 构造对象
    *************************************************************/
    SyncManager();

private:

    QSharedPointer<IProtocolBean> m_spProtocolBean;                 // 通讯协议组件
    QSharedPointer<AbstractComm> m_pAbstractComm;   // 通讯链路组件
    DataSync::LinkType m_linkType;      // 链路方式

    DataProcesser m_dataProcesser;

    bool m_bLegal;                  // 是否通过密码验证

    QMutex m_mutex;                 // 发送数据互斥锁

    SyncServer  *m_pSyncServer;     // 同步服务端

    QThread* m_pThread;

    // 本机通讯地址
    static quint32 m_uSrcAddress;
    // 目标通讯地址
    static quint32 m_uDestAddress;

    int m_iReadCnt;

    // 用于同步配置文件
    QSharedPointer<CMComm> m_pCM;
};

#endif

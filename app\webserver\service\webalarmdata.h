/*
* Copyright (c) 2017.1，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：alarmdata.h
*
* 初始版本：1.0
* 作者：贺小强
* 创建日期：2017年1月6日
* 摘要：报警信息页面接口，提供报警信息数据,提供报警信息确认功能
*
*/

#ifndef WEBALARMDATA_H
#define WEBALARMDATA_H

#include <QJsonObject>
#include "devicetree.h"
#include "configservice.h"
#include "qdatetime.h"
#include "commandfuns.h"

class WebAlarmData : public QJsonObject
{
public:
    /************************************************
    * 函数名:  AlarmData
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  构造函数
    ************************************************/
    WebAlarmData();

    /************************************************
    * 函数名:  reFreshHistoryData
    * 输入参数:  eChannelType -- 通道类型
                ulPage -- 展现的页数
                ulCountPerPage -- 每页的数据条目数
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  根据条件刷新历史报警数据
    ************************************************/
    void reFreshHistoryData(ADUChannelType eChannelType, quint32 ulPage, quint32 ulCountPerPage);

    /************************************************
    * 函数名:  reFreshcurrentData
    * 输入参数:  eChannelType -- 通道类型
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  根据条件刷新当前报警数据
    ************************************************/
    void reFreshCurrentData(ADUChannelType eChannelType);

    /************************************************
    * 函数名:  reFreshDeviceSVGData
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  刷新设备svg图
    ************************************************/
    void reFreshDeviceSVGData(quint8 ucDeviceID);

    /************************************************
    * 函数名:  reFreshStationSVGData
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  刷新站点svg图
    ************************************************/
    void reFreshStationSVGData(void);

    /************************************************
    * 函数名:  reFreshStationCountData
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  刷新站点统计信息
    ************************************************/
    void reFreshStationCountData(const QDate &startDate, const QDate &endDate);



private:
    /************************************************
    * 函数名:  getDeviceAlarmCount
    * 输入参数:  NULL
    * 输出参数:  NULL
    * 返回值:  NULL
    * 功能:  获取设备的报警条数
    ************************************************/
    int getDeviceAlarmCount(const DeviceNode &staDevice);



};

#endif // ALARMDATA_H

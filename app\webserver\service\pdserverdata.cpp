#include <QJsonDocument>
#include "pdserverdata.h"
#include "configdata.h"
#include "pdmonitor.h"
#include "gzip.h"
#include <QCryptographicHash>
#include "systemsettings.h"
#include "statemonitor.h"
#include "signalalgorithm.h"
#include "monitorservice.h"
#include "systeminfo.h"
#include "log.h"

PDServerData::PDServerData()
{

}

PDServerData::~PDServerData()
{

}

/************************************************
 * 函数名:  getDataIndex
 * 输入参数:  lADUChannelType -- 通道类型列表
 * 输出参数:  data -- 数据
 * 返回值:  NULL
 * 功能: 获取数据序号
 ************************************************/
void PDServerData::getDataIndex(const QList<ADUChannelType> &lADUChannelType, QJsonArray& data)
{
    for (int i = 0; i < lADUChannelType.size(); i++)
    {
        quint64 startNum = 0;
        quint64 endNum = 0;

        QJsonObject jsonNum;
        jsonNum.insert(STR_CHANNEL_TYPE,lADUChannelType.at(i));

        ADUChannelType aduType = lADUChannelType.at(i);

        if ( DBServer::instance().getRecordIndex(aduType, startNum, endNum) )
        {
            jsonNum.insert(STR_START_NUM,QString::number(startNum));
            jsonNum.insert(STR_END_NUM,QString::number(endNum));

            if ( endNum < startNum )
            {
                PDS_SYS_ERR_LOG("RecordIndex error: %d %d %d", lADUChannelType[i], startNum, endNum);
            }
        }
        else
        {
            PDS_SYS_WARNING_LOG("fail to getRecordIndex %d", lADUChannelType[i]);
        }
        data.append(jsonNum);
    }
}


/************************************************
 * 函数名:  setCodeInfo
 * 输入参数:  eSetCodeType --  设置对应code的类型
 * 输出参数:  data -- 数据
 * 返回值:  NULL
 * 功能: 设置映射编号
 ************************************************/
void PDServerData::setCodeInfo(SetCodeType eSetCodeType, QJsonObject& data)
{
    Q_UNUSED(eSetCodeType);

    bool bIsSetSuccess = true;
    QString strStationPMS = data.value(STR_STATION_PMS).toString();
    ConfigService::instance().setStationPMS(strStationPMS);
    data.insert(STR_RESULT, bIsSetSuccess);

    QJsonArray jsonDevices = data.value(STR_DEVICE_ITEMS).toArray();
    for (int i = 0; i < jsonDevices.size(); i++)
    {
        bIsSetSuccess = false;
        QJsonObject jsonDevice = jsonDevices.at(i).toObject();
        QString strDevicePMS = jsonDevice.value(STR_DEVICE_PMS).toString();
        QString strDeviceID = jsonDevice.value(STR_DEVICE_ID).toString();
        if (ConfigService::instance().setDevicePMS(strDeviceID, strDevicePMS))
        {
            bIsSetSuccess = true;
        }
        jsonDevice.insert(STR_RESULT, bIsSetSuccess);
        jsonDevices[i] = jsonDevice;
    }
    data[STR_DEVICE_ITEMS] = jsonDevices;
}

/************************************************
 * 输入参数:  NULL
 * 输出参数:  data -- 数据
 * 返回值:  NULL
 * 功能: 获取主机状态参数
 ************************************************/
void PDServerData::getMonitotState(QJsonObject& data)
{
    int iADUNUM = 0;
    ConfigService &configService = ConfigService::instance();
    QList<test::ADUUpdateStateInfo> listStateInfo;
    ConfigService::instance().getAduBusStates(listStateInfo);

    QJsonArray jsonStates;

    WebSetting stWebSetting = configService.webSetting();

    // 4G信号质量
    data.insert(Str_GprsSignal, configService.getGPRSSignal() );
    // 4G流量情况
    qint64 llNetFlowCount;
    QDateTime beginDateTime;
    if ( SystemInfo::getNetFlowCount(STR_NETWORK_PPP, llNetFlowCount, beginDateTime) )
    {
        int nNetFlowKb = (int)(llNetFlowCount / 1024);
        int nBeginTime = (int)beginDateTime.toTime_t();
        data.insert(Str_GprsNetworkFlow, nNetFlowKb);
        data.insert(Str_GprsStartTime, nBeginTime);
    }
    // 是否掉电
    bool bPowerSt = !ConfigService::instance().isMonitorSupplyByBattery();
    data.insert("monitorPowerState", bPowerSt);
    //----
    // Ip地址
    data.insert("ip", stWebSetting.strIP);

    //todo: 低功耗主机电量  暂无底层接口，待后续实现
    //data.insert("monitorBattery", 50);

    data.insert("vpnIp", configService.getGPRSIP());
    data.insert("vpnKeyName", getVpnKeyName() );

    data.insert("usedNetType", stWebSetting.eWebType);
    SystemSetting stSystemSetting = configService.systemSettings();
    data.insert("MonitorType", stSystemSetting.eMonitorHostType );

    for (int j = 0; j < listStateInfo.size(); j++)
    {
        const test::ADUUpdateStateInfo &adu = listStateInfo.at(j);
        ADUUnitInfo aduInfo;
        ConfigService::instance().getADU(adu.strAduId, aduInfo);
        iADUNUM++;
        QJsonObject jsonState;
        jsonState.insert("aduId",adu.strAduId);
        jsonState.insert("RSSI",adu.sSignalLevel);
        //前端通讯信号质量阈值
        jsonState.insert("RSSIThred",adu.sNormalSignalLevel);
        //前端通讯信号质量告警状态
        jsonState.insert("RSSIAlarmState",adu.bLevelAlarm);
        //前端通讯方式
        jsonState.insert("connectionType",aduInfo.stADUParam.eLinkGroup);
        //前端通讯信噪比
        jsonState.insert("SNR",adu.sNSR);
        jsonState.insert("SNRThred",adu.sNormalNSR);
        jsonState.insert("SNRAlarmState",adu.bNSRAlarm);
        //电池电量
        jsonState.insert("battery",adu.sCharge);
        jsonState.insert("batteryLife",adu.sChargeLife);
        jsonState.insert("batteryThred",adu.sNormalCharge);
        //电池电量告警状态
        jsonState.insert("batteryAlarmState",adu.bChargeAlarm);
        //上次通讯状态
        bool lastConnectState = true;

        if(adu.isOnLine())
        {
            lastConnectState = true;
        }
        else
        {
            lastConnectState = false;
        }
        jsonState.insert("LastConnectionState", lastConnectState);

        //失联告警状态
        jsonState.insert("disconnectionAlarmState", adu.bLostConnect);
        jsonState.insert("LastConnectionTime",adu.businessStateChangeTime.toString("yyyy-MM-dd hh:mm:ss"));
        jsonStates.append(jsonState);
    }
    configService.setIsUpdateState(false);

    data.insert("aduStateItems", jsonStates);
    data.insert("aduStateCount", iADUNUM);
}

/************************************************
 * 函数名:  GetMonitorInfo
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  MonitorInfo -- 检测设备信息结构体
 * 功能: 获取检测设备信息
 ************************************************/
MonitorInfo PDServerData::getMonitorInfo(void)
{
    MonitorInfo monitorInfo;
    ConfigService &configService = ConfigService::instance();
    const StationNode &deviceTree = configService.stationNode();
    SystemSetting staSystemSetting = configService.systemSettings();
    monitorInfo.strMonitorType = "S1010";
    monitorInfo.strStationID = deviceTree.strSiteGUID;
    monitorInfo.strStationName = deviceTree.strName;
    monitorInfo.strStationPMS = deviceTree.strPMS;
    monitorInfo.strCompany = deviceTree.strCompany;
    monitorInfo.iUploadInterval = staSystemSetting.iUploadInterval;
    monitorInfo.iFrequency = staSystemSetting.iFreq;
    monitorInfo.iTotal = configService.ADUCount();
    monitorInfo.strMonitorID = staSystemSetting.strMonitorID;
    if (monitorInfo.iTotal == 0)
    {
        monitorInfo.bIncludeSubDev = false;
    }
    else
    {
        monitorInfo.bIncludeSubDev = true;
    }

    //monitorInfo.strSN = "monitorSN";
    monitorInfo.strSN = monitorInfo.strMonitorID;
    //monitorInfo.ProductDate = QDateTime::currentDateTime();
    monitorInfo.strHardwearVer = "monitorVer";
    monitorInfo.strSoftwearVer = APP_VERSION;
    monitorInfo.strWebViewVerr = WEB_VIEW_VERSION;
    monitorInfo.strDiagestAlgorithm = "MD5";

    QByteArray baFile;
    QFile XMLFile ("config.xml");
    if ( XMLFile.open(QIODevice::ReadOnly) )
    {
        baFile = XMLFile.readAll();
        XMLFile.close();
    }
    else
    {
        PDS_SYS_ERR_LOG("fail to open config.xml");
    }
    monitorInfo.strSettingDigest = QCryptographicHash::hash(baFile, QCryptographicHash::Md5).toHex();
    monitorInfo.iSettingVer = 5;
    monitorInfo.strDiagramDigest = "DiagramDigest";
    monitorInfo.iDiagramVer = 3;
    PDS_SYS_INFO_LOG("getMonitorInfo %s", monitorInfo.strSettingDigest.toLatin1().data());
    return monitorInfo;
}

/************************************************
 * 函数名:  getADUInfo
 * 输入参数:  iPage -- 页码
 *          iCountPerPage -- 每页数量
 * 输出参数:  data -- 数据
 * 返回值:  所有前端的数量
 * 功能: 获取前端信息
 ************************************************/
int PDServerData::getADUInfo(QJsonArray& data)
{
    int iADUNUM = 0;
    ConfigService &configService = ConfigService::instance();
    const QList<ADUUnitInfo> &ADUList = configService.ADUList();

    for (int j = 0; j < ADUList.size(); j++)
    {
        iADUNUM++;
        const ADUUnitInfo &adu = ADUList.at(j);
        QJsonObject jsonADU;
        jsonADU.insert(STR_ADU_VERSION,adu.strVersion);

        bool bOnline;
        test::ADUUpdateStateInfo stateInfo;
        bool result = ConfigService::instance().getAduBusState(adu.strID, stateInfo);
        if(result)
        {
            if(stateInfo.isOnLine())
            {
                bOnline = true;
            }
        }
        else
        {
            bOnline = false;
        }
        jsonADU.insert(STR_ADU_CONNECTION, bOnline);
        jsonADU.insert(STR_ADU_ID,adu.strID);
        jsonADU.insert(STR_ADU_NAME,adu.strName);
        QString strName = ConfigService::instance().getADUTypName(adu.eType);
        jsonADU.insert(STR_ADU_TYPE, strName);
        jsonADU.insert(STR_ADU_MODEL,"");
        jsonADU.insert("SN","");
        jsonADU.insert(STR_POINT_COUNT,adu.Channels.size());
        jsonADU.insert(STR_ADU_GROUP_ID,adu.stADUParam.ucWorkGroup);
        jsonADU.insert(STR_ADU_GROUP_NUM,adu.stADUParam.usNumInGroup);
        jsonADU.insert(STR_ADU_COMMUNICATION_CHANNEL,adu.stADUParam.ucConnectionLoad);
        jsonADU.insert(STR_ADU_COMMUNICATION_RATE,adu.stADUParam.ucConnectionSpeed);
        jsonADU.insert(STR_ADU_SLEEP_INTERVAL,static_cast<int>(adu.stADUParam.uiSleepTime));
        jsonADU.insert(STR_ADU_SAMPEL_INTERVAL,static_cast<int>(adu.stADUParam.uiSampleSpace));
        jsonADU.insert(STR_ADU_SAMPEL_START_TIMR,adu.stADUParam.usStartSampleTime);
        jsonADU.insert(STR_SF6_ALARM_TASK_GROUP, adu.iTaskGroup);
        jsonADU.insert(STR_ADU_RS485_COM_PORT, adu.strRS485ComPort);

        data.append(jsonADU);
    }

    return iADUNUM;
}

/************************************************
 * 函数名:  setADUSampleInterval
 * 输入参数: eADUType -- 前端类型
 * 输出参数:  NULL
 * 返回值:  操作结果
 * 功能: 设置前端采集间隔
 ************************************************/
bool PDServerData::setADUSampleInterval(const QString &strADUType, int iSampleInterval)
{
    Q_UNUSED(strADUType)
    Q_UNUSED(iSampleInterval)
    return true;
}



/************************************************
 * 函数名:  getChartData
 * 输入参数:  eChannelType -- 通道类型
 *           lIndex -- 数据序号
 * 输出参数:  NULL
 * 返回值:  true -- 成功
 *         false -- 失败
 * 功能: 获取图谱数据
 ************************************************/
bool PDServerData::getChartData( ADUChannelType eADUChannelType,qint64 lIndex, QJsonObject& data)
{
    DBServer &dbServer = DBServer::instance();
    bool bret = false;
    switch (eADUChannelType)
    {
    case CHANNEL_AE://AE参数
    {
        AERecord AEDataInfo = dbServer.getAERecord(lIndex);

        QJsonObject recordObject;
        recordObject.insert(STR_PARA_GAIN, AEDataInfo.ucGain);
        recordObject.insert(STR_DATE_TIMR, AEDataInfo.recordTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_FREQUNCY, AEDataInfo.ucPwrFre);
        recordObject.insert(STR_BATTERY, AEDataInfo.batteryInfo);
        recordObject.insert(STR_SYNC_TYPE, AEDataInfo.ucSyncType);
        recordObject.insert(STR_SYNC_FLAG, AEDataInfo.ucSyncFlag);
        recordObject.insert(STR_MAX, AEDataInfo.fMax);
        recordObject.insert(STR_RMS, AEDataInfo.fRms);
        recordObject.insert(STR_FRE_1_VALUE, AEDataInfo.fFre1);
        recordObject.insert(STR_FRE_2_VALUE, AEDataInfo.fFre2);
        recordObject.insert(STR_SAMPLE_COUNT, AEDataInfo.usSampleCountCycle);
        recordObject.insert(STR_SAMPLE_RATE, AEDataInfo.ucSampleCycleCount);
        recordObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));
        recordObject.insert(STR_DATA_UNIT, DATA_UNIT_DB);

        data.insert(STR_STATION_ID, AEDataInfo.pointArchiveInfo.strStationGUID);
        data.insert(STR_STATION_PMS, AEDataInfo.pointArchiveInfo.strStationPMS);
        data.insert(STR_DEVICE_ID, AEDataInfo.pointArchiveInfo.strDeviceGUID);
        data.insert(STR_DEVICE_PMS, AEDataInfo.pointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, AEDataInfo.pointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, AEDataInfo.aduId);
        data.insert(STR_CHANNEL_INDEX, AEDataInfo.channelId);
        data.insert(STR_CHANNEL_ID, "");
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, AEDataInfo.recordStringId);
        data.insert(STR_RECORD_DATA, recordObject);
    }
        break;
    case CHANNEL_TEV://TEV参数
    {
        TEVRecord TEVDataInfo = dbServer.getTEVRecord(lIndex);

        QJsonObject recordObject;
        recordObject.insert(STR_DATE_TIMR, TEVDataInfo.recordTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_BATTERY, TEVDataInfo.batteryInfo);
        recordObject.insert(STR_MAX, TEVDataInfo.cMax);
        recordObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));
        recordObject.insert(STR_DATA_UNIT, DATA_UNIT_DB);

        data.insert(STR_STATION_ID, TEVDataInfo.pointArchiveInfo.strStationGUID);
        data.insert(STR_STATION_PMS, TEVDataInfo.pointArchiveInfo.strStationPMS);
        data.insert(STR_DEVICE_ID, TEVDataInfo.pointArchiveInfo.strDeviceGUID);
        data.insert(STR_DEVICE_PMS, TEVDataInfo.pointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, TEVDataInfo.pointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, TEVDataInfo.aduId);
        data.insert(STR_CHANNEL_INDEX, TEVDataInfo.channelId);
        data.insert(STR_CHANNEL_ID, "");
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, TEVDataInfo.recordStringId);
        data.insert(STR_RECORD_DATA, recordObject);
    }
        break;
    case CHANNEL_UHF:
    {
        PRPSRecord UHFDataInfo = dbServer.getUHFRecord(lIndex);

        QJsonObject recordObject;

        recordObject.insert(STR_DATE_TIMR, UHFDataInfo.recordTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_FREQUNCY, UHFDataInfo.ucPwrFre);
        recordObject.insert(STR_PARA_GAIN, UHFDataInfo.ucGain);
        recordObject.insert(STR_PARA_BAND_WIDTH, UHFDataInfo.ucBandwidth);
        recordObject.insert(STR_SYNC_TYPE, UHFDataInfo.ucSyncType);
        recordObject.insert(STR_SYNC_FLAG, UHFDataInfo.ucSyncFlag);
        recordObject.insert(STR_PERIOD, UHFDataInfo.ucPeriod);
        recordObject.insert(STR_PHASE, UHFDataInfo.ucPhase);
        recordObject.insert(STR_MAX_Q, UHFDataInfo.fMaxq);
        recordObject.insert(STR_MIN_Q, UHFDataInfo.fMinq);
        recordObject.insert(STR_AVG_Q, UHFDataInfo.fAvgq);
        recordObject.insert(STR_PD_COUNT, UHFDataInfo.iPdCount);
        recordObject.insert(STR_PD_PHASE, UHFDataInfo.fPdPhase);
        recordObject.insert(STR_IS_PD, UHFDataInfo.iIsPd);
        recordObject.insert(STR_DATA_UNIT, DATA_UNIT_PERCENT);
        recordObject.insert(STR_VALUE_RANGE, 70);
        recordObject.insert(STR_PD_TYPE, UHFDataInfo.iPdType);
        QJsonArray arrayData;
        for (int i = 0; i < UHFDataInfo.fArray.size(); i++)
        {
            arrayData.append(UHFDataInfo.fArray.at(i));
        }

        recordObject.insert(STR_DATA_ARRAY, arrayData);

        QJsonObject dataObject;
        QJsonDocument document;
        document.setObject(recordObject);
        QByteArray compressingData = document.toJson();
        QByteArray compressedData;
        if (compress(compressingData, compressedData))
        {
            // 压缩成功
            dataObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_DO));
            dataObject.insert(STR_COMPRESS_DATA, QString::fromUtf8(compressedData.toBase64()));
        }
        else
        {
            // 压缩失败，则不使用压缩上传
            dataObject = recordObject;
            dataObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));
        }

        data.insert(STR_STATION_ID, UHFDataInfo.pointArchiveInfo.strStationGUID);
        data.insert(STR_STATION_PMS, UHFDataInfo.pointArchiveInfo.strStationPMS);
        data.insert(STR_DEVICE_ID, UHFDataInfo.pointArchiveInfo.strDeviceGUID);
        data.insert(STR_DEVICE_PMS, UHFDataInfo.pointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, UHFDataInfo.pointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, UHFDataInfo.aduId);
        data.insert(STR_CHANNEL_INDEX, UHFDataInfo.channelId);
        data.insert(STR_CHANNEL_ID, "");
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, UHFDataInfo.recordStringId);
        data.insert(STR_RECORD_DATA, dataObject);
    }
        break;
    case CHANNEL_HFCT:
    {
        PRPSRecord HFCTDataInfo = dbServer.getHFCTRecord(lIndex);

        QJsonObject recordObject;

        recordObject.insert(STR_DATE_TIMR, HFCTDataInfo.recordTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_FREQUNCY, HFCTDataInfo.ucPwrFre);
        recordObject.insert(STR_PARA_GAIN, HFCTDataInfo.ucGain);
        recordObject.insert(STR_SYNC_TYPE, HFCTDataInfo.ucSyncType);
        recordObject.insert(STR_SYNC_FLAG, HFCTDataInfo.ucSyncFlag);
        recordObject.insert(STR_PERIOD, HFCTDataInfo.ucPeriod);
        recordObject.insert(STR_PHASE, HFCTDataInfo.ucPhase);
        recordObject.insert(STR_MAX_Q, HFCTDataInfo.fMaxq);
        recordObject.insert(STR_MIN_Q, HFCTDataInfo.fMinq);
        recordObject.insert(STR_AVG_Q, HFCTDataInfo.fAvgq);
        recordObject.insert(STR_PD_COUNT, HFCTDataInfo.iPdCount);
        recordObject.insert(STR_PD_PHASE, HFCTDataInfo.fPdPhase);
        recordObject.insert(STR_IS_PD, HFCTDataInfo.iIsPd);
        recordObject.insert(STR_DATA_UNIT, DATA_UNIT_PERCENT);
        recordObject.insert(STR_VALUE_RANGE, 80);
        recordObject.insert(STR_PD_TYPE, HFCTDataInfo.iPdType);
        QJsonArray arrayData;
        for (int i = 0; i < HFCTDataInfo.fArray.size(); i++)
        {
            arrayData.append(HFCTDataInfo.fArray.at(i));
        }

        recordObject.insert(STR_DATA_ARRAY, arrayData);

        QJsonObject dataObject;
        QJsonDocument document;
        document.setObject(recordObject);
        QByteArray compressingData = document.toJson();
        QByteArray compressedData;
        if (compress(compressingData, compressedData))
        {
            // 压缩成功
            dataObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_DO));
            dataObject.insert(STR_COMPRESS_DATA, QString::fromUtf8(compressedData.toBase64()));
        }
        else
        {
            // 压缩失败，则不使用压缩上传
            dataObject = recordObject;
            dataObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));
        }

        data.insert(STR_STATION_ID, HFCTDataInfo.pointArchiveInfo.strStationGUID);
        data.insert(STR_STATION_PMS, HFCTDataInfo.pointArchiveInfo.strStationPMS);
        data.insert(STR_DEVICE_ID, HFCTDataInfo.pointArchiveInfo.strDeviceGUID);
        data.insert(STR_DEVICE_PMS, HFCTDataInfo.pointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, HFCTDataInfo.pointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, HFCTDataInfo.aduId);
        data.insert(STR_CHANNEL_INDEX, HFCTDataInfo.channelId);
        data.insert(STR_CHANNEL_ID, "");
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, HFCTDataInfo.recordStringId);
        data.insert(STR_RECORD_DATA, dataObject);
    }
        break;
    case CHANNEL_MECH:
    {
        MechDefine::MechISRecord stMechRecord = dbServer.getMECHRecord(lIndex);

        QJsonObject recordObject;
        recordObject.insert(STR_DATE_TIMR, stMechRecord.recordTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_MECH_DATA_TYPE, stMechRecord.iMechDataType);
        recordObject.insert(STR_MECH_FUNCTION_TYPE, stMechRecord.iMechFunctionType);
        recordObject.insert(STR_MECH_STORAGE_TYPE, stMechRecord.iMechStorageType);
        recordObject.insert(STR_LOOP_THRESHOLD, stMechRecord.dLoopThreshold);
        recordObject.insert(STR_LOOP_SAMPLE_RATE, stMechRecord.iLoopSampleRate);
        recordObject.insert(STR_MECH_SAMPLE_RATE, stMechRecord.iMechSampleRate);
        recordObject.insert(STR_LOOP_TRIGGER_TIME, stMechRecord.loopTiggerTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_DATA_UNIT, DATA_UNIT_A);
        QJsonArray switchStateLst;
        for (int index = 0; index < stMechRecord.SwitchStateLst.size(); ++index)
        {
            switchStateLst.append(QJsonValue(int(stMechRecord.SwitchStateLst.at(index))));
        }
        recordObject.insert(STR_SWITCH_STATE_LST, switchStateLst);
        QJsonArray chanACurrentLst;
        for (int index = 0; index < stMechRecord.chanACurrentLst.size(); ++index)
        {
            chanACurrentLst.append(QJsonValue(stMechRecord.chanACurrentLst.at(index)));
        }
        recordObject.insert(STR_CHAN_A_CURRENT_LST, chanACurrentLst);
        QJsonArray chanBCurrentLst;
        for (int index = 0; index < stMechRecord.chanBCurrentLst.size(); ++index)
        {
            chanBCurrentLst.append(QJsonValue(stMechRecord.chanBCurrentLst.at(index)));
        }
        recordObject.insert(STR_CHAN_B_CURRENT_LST, chanBCurrentLst);
        QJsonArray chanCCurrentLst;
        for (int index = 0; index < stMechRecord.chanCCurrentLst.size(); ++index)
        {
            chanCCurrentLst.append(QJsonValue(stMechRecord.chanCCurrentLst.at(index)));
        }
        recordObject.insert(STR_CHAN_C_CURRENT_LST, chanCCurrentLst);
        QJsonArray loopJoinACurrentLst;
        for (int index = 0; index < stMechRecord.loopCloseACurrentLst.size(); ++index)
        {
            loopJoinACurrentLst.append(QJsonValue(stMechRecord.loopCloseACurrentLst.at(index)));
        }
        recordObject.insert(STR_LOOP_JOIN_A_CURRENT_LST, loopJoinACurrentLst);
        QJsonArray loopJoinBCurrentLst;
        for (int index = 0; index < stMechRecord.loopCloseBCurrentLst.size(); ++index)
        {
            loopJoinBCurrentLst.append(QJsonValue(stMechRecord.loopCloseBCurrentLst.at(index)));
        }
        recordObject.insert(STR_LOOP_JOIN_B_CURRENT_LST, loopJoinBCurrentLst);
        QJsonArray loopJoinCCurrentLst;
        for (int index = 0; index < stMechRecord.loopCloseCCurrentLst.size(); ++index)
        {
            loopJoinCCurrentLst.append(QJsonValue(stMechRecord.loopCloseCCurrentLst.at(index)));
        }
        recordObject.insert(STR_LOOP_JOIN_C_CURRENT_LST, loopJoinCCurrentLst);
        QJsonArray loopDivideACurrentLst;
        for (int index = 0; index < stMechRecord.loopOpenACurrentLst.size(); ++index)
        {
            loopDivideACurrentLst.append(QJsonValue(stMechRecord.loopOpenACurrentLst.at(index)));
        }
        recordObject.insert(STR_LOOP_DIVIDE_A_CURRENT_LST, loopDivideACurrentLst);
        QJsonArray loopDivideBCurrentLst;
        for (int index = 0; index < stMechRecord.loopOpenBCurrentLst.size(); ++index)
        {
            loopDivideBCurrentLst.append(QJsonValue(stMechRecord.loopOpenBCurrentLst.at(index)));
        }
        recordObject.insert(STR_LOOP_DIVIDE_B_CURRENT_LST, loopDivideBCurrentLst);
        QJsonArray loopDivideCCurrentLst;
        for (int index = 0; index < stMechRecord.loopOpenCCurrentLst.size(); ++index)
        {
            loopDivideCCurrentLst.append(QJsonValue(stMechRecord.loopOpenCCurrentLst.at(index)));
        }
        recordObject.insert(STR_LOOP_DIVIDE_C_CURRENT_LST, loopDivideCCurrentLst);
        recordObject.insert(STR_MECH_THRESHOLD, stMechRecord.dMechThreshold);
        recordObject.insert(STR_MECH_TRIGGER_TIME, stMechRecord.mechTiggerTime.toString("yyyy-MM-dd hh:mm:ss"));
        QJsonArray mechChanACurrentLst;
        for (int index = 0; index < stMechRecord.mechChanACurrentLst.size(); ++index)
        {
            mechChanACurrentLst.append(QJsonValue(stMechRecord.mechChanACurrentLst.at(index)));
        }
        recordObject.insert(STR_MECH_CHAN_A_CURRENT_lST, mechChanACurrentLst);
        QJsonArray mechChanBCurrentLst;
        for (int index = 0; index < stMechRecord.mechChanBCurrentLst.size(); ++index)
        {
            mechChanBCurrentLst.append(QJsonValue(stMechRecord.mechChanBCurrentLst.at(index)));
        }
        recordObject.insert(STR_MECH_CHAN_B_CURRENT_LST, mechChanBCurrentLst);
        QJsonArray mechChanCCurrentLst;
        for (int index = 0; index < stMechRecord.mechChanCCurrentLst.size(); ++index)
        {
            mechChanCCurrentLst.append(QJsonValue(stMechRecord.mechChanCCurrentLst.at(index)));
        }
        recordObject.insert(STR_MECH_CHAN_C_CURRENT_LST, mechChanCCurrentLst);
        recordObject.insert(STR_SWITCH_STATE, int(stMechRecord.eSwitchState));
        recordObject.insert(STR_DIVIDE_PERIOD, stMechRecord.dDividePeriod);
        recordObject.insert(STR_JOIN_PERIOD, stMechRecord.dJoinPeriod);
        recordObject.insert(STR_A_DIVIDE_PERIOD, stMechRecord.dADividePeriod);
        recordObject.insert(STR_B_DIVIDE_PERIOD, stMechRecord.dBDividePeriod);
        recordObject.insert(STR_C_DIVIDE_PERIOD, stMechRecord.dCDividePeriod);
        recordObject.insert(STR_A_JOIN_PERIOD, stMechRecord.dAJoinPeriod);
        recordObject.insert(STR_B_JOIN_PERIOD, stMechRecord.dBJoinPeriod);
        recordObject.insert(STR_C_JOIN_PERIOD, stMechRecord.dCJoinPeriod);
        recordObject.insert(STR_DIVIDE_SYNC_PERIOD, stMechRecord.dDivideSyncPeriod);
        recordObject.insert(STR_JOIN_SYNC_PERIOD, stMechRecord.dJoinSyncPeriod);
        QJsonArray ASwitchStateLst;
        for (int index = 0; index < stMechRecord.aSwitchStateLst.size(); ++index)
        {
            ASwitchStateLst.append(QJsonValue(int(stMechRecord.aSwitchStateLst.at(index))));
        }
        recordObject.insert(STR_A_SWITCH_STATE_LST, ASwitchStateLst);
        QJsonArray BSwitchStateLst;
        for (int index = 0; index < stMechRecord.bSwitchStateLst.size(); ++index)
        {
            BSwitchStateLst.append(QJsonValue(int(stMechRecord.bSwitchStateLst.at(index))));
        }
        recordObject.insert(STR_B_SWITCH_STATE_LST, BSwitchStateLst);
        QJsonArray CSwitchStateLst;
        for (int index = 0; index < stMechRecord.cSwitchStateLst.size(); ++index)
        {
            CSwitchStateLst.append(QJsonValue(int(stMechRecord.cSwitchStateLst.at(index))));
        }
        recordObject.insert(STR_C_SWITCH_STATE_LST, CSwitchStateLst);
        recordObject.insert(STR_TWICE_DIVIDE_TIME, stMechRecord.dTwiceDivideTime);
        recordObject.insert(STR_TWICE_A_DIVIDE_PERIOD, stMechRecord.dTwiceADividePeriod);
        recordObject.insert(STR_TWICE_B_DIVIDE_PERIOD, stMechRecord.dTwiceBDividePeriod);
        recordObject.insert(STR_TWICE_C_DIVIDE_PERIOD, stMechRecord.dTwiceCDividePeriod);
        recordObject.insert(STR_TWICE_DIVIDE_SYNC_PERIOD, stMechRecord.dTwiceDivideSyncPeriod);
        recordObject.insert(STR_CHAN_A_SWITCH_SHOT_TIME, stMechRecord.dASwitchShotTime);
        recordObject.insert(STR_CHAN_A_SWITCH_NO_CURRENT_TIME, stMechRecord.dASwitchNoCurrentTime);
        recordObject.insert(STR_CHAN_B_SWITCH_SHOT_TIME, stMechRecord.dBSwitchShotTime);
        recordObject.insert(STR_CHAN_B_SWITCH_NO_CURRENT_TIME, stMechRecord.dBSwitchNoCurrentTime);
        recordObject.insert(STR_CHAN_C_SWITCH_SHOT_TIME, stMechRecord.dCSwitchShotTime);
        recordObject.insert(STR_CHAN_C_SWITCH_NO_CURRENT_TIME, stMechRecord.dCSwitchNoCurrentTime);
        recordObject.insert(STR_JOIN_A_LOOP_UP_TIME, stMechRecord.dCloseALoopUpTime);
        recordObject.insert(STR_JOIN_A_HIT_TIME, stMechRecord.dCloseAHitTime);
        recordObject.insert(STR_JOIN_A_SUB_SWITCH_CLOSE_TIME, stMechRecord.dCloseASubSwitchCloseTime);
        recordObject.insert(STR_JOIN_A_LOOP_DOWN_TIME, stMechRecord.dCloseALoopDownTime);
        recordObject.insert(STR_JOIN_A_MAX_LOOP_CURRENT, stMechRecord.dCloseAMaxLoopCurrent);
        recordObject.insert(STR_JOIN_B_LOOP_UP_TIME, stMechRecord.dCloseBLoopUpTime);
        recordObject.insert(STR_JOIN_B_HIT_TIME, stMechRecord.dCloseBHitTime);
        recordObject.insert(STR_JOIN_B_SUB_SWITCH_CLOSE_TIME, stMechRecord.dCloseBSubSwitchCloseTime);
        recordObject.insert(STR_JOIN_B_LOOP_DOWN_TIME, stMechRecord.dCloseBLoopDownTime);
        recordObject.insert(STR_JOIN_B_MAX_LOOP_CURRENT, stMechRecord.dCloseBMaxLoopCurrent);
        recordObject.insert(STR_JOIN_C_LOOP_UP_TIME, stMechRecord.dCloseCLoopUpTime);
        recordObject.insert(STR_JOIN_C_HIT_TIME, stMechRecord.dCloseCHitTime);
        recordObject.insert(STR_JOIN_C_SUB_SWITCH_CLOSE_TIME, stMechRecord.dCloseCSubSwitchCloseTime);
        recordObject.insert(STR_JOIN_C_LOOP_DOWN_TIME, stMechRecord.dCloseCLoopDownTime);
        recordObject.insert(STR_JOIN_C_MAX_LOOP_CURRENT, stMechRecord.dCloseCMaxLoopCurrent);
        recordObject.insert(STR_DIVIDE_A_LOOP_UP_TIME, stMechRecord.dOpenALoopUpTime);
        recordObject.insert(STR_DIVIDE_A_HIT_TIME, stMechRecord.dOpenAHitTime);
        recordObject.insert(STR_DIVIDE_A_SUB_SWITCH_CLOSE_TIME, stMechRecord.dOpenASubSwitchCloseTime);
        recordObject.insert(STR_DIVIDE_A_LOOP_DOWN_TIME, stMechRecord.dOpenALoopDownTime);
        recordObject.insert(STR_DIVIDE_A_MAX_LOOP_CURRENT, stMechRecord.dOpenAMaxLoopCurrent);
        recordObject.insert(STR_DIVIDE_B_LOOP_UP_TIME, stMechRecord.dOpenBLoopUpTime);
        recordObject.insert(STR_DIVIDE_B_HIT_TIME, stMechRecord.dOpenBHitTime);
        recordObject.insert(STR_DIVIDE_B_SUB_SWITCH_CLOSE_TIME, stMechRecord.dOpenBSubSwitchCloseTime);
        recordObject.insert(STR_DIVIDE_B_LOOP_DOWN_TIME, stMechRecord.dOpenBLoopDownTime);
        recordObject.insert(STR_DIVIDE_B_MAX_LOOP_CURRENT, stMechRecord.dOpenBMaxLoopCurrent);
        recordObject.insert(STR_DIVIDE_C_LOOP_UP_TIME, stMechRecord.dOpenCLoopUpTime);
        recordObject.insert(STR_DIVIDE_C_HIT_TIME, stMechRecord.dOpenCHitTime);
        recordObject.insert(STR_DIVIDE_C_SUB_SWITCH_CLOSE_TIME, stMechRecord.dOpenCSubSwitchCloseTime);
        recordObject.insert(STR_DIVIDE_C_LOOP_DOWN_TIME, stMechRecord.dOpenCLoopDownTime);
        recordObject.insert(STR_DIVIDE_C_MAX_LOOP_CURRENT, stMechRecord.dOpenCMaxLoopCurrent);
        recordObject.insert(STR_TWICE_DIVIDE_A_LOOP_UP_TIME, stMechRecord.dTwiceOpenALoopUpTime);
        recordObject.insert(STR_TWICE_DIVIDE_A_HIT_TIME, stMechRecord.dTwiceOpenAHitTime);
        recordObject.insert(STR_TWICE_DIVIDE_A_SUB_SWITCH_CLOSE_TIME, stMechRecord.dTwiceOpenASubSwitchCloseTime);
        recordObject.insert(STR_TWICE_DIVIDE_A_LOOP_DOWN_TIME, stMechRecord.dTwiceOpenALoopDownTime);
        recordObject.insert(STR_TWICE_DIVIDE_A_MAX_LOOP_CURRENT, stMechRecord.dTwiceOpenAMaxLoopCurrent);
        recordObject.insert(STR_TWICE_DIVIDE_B_LOOP_UP_TIME, stMechRecord.dTwiceOpenBLoopUpTime);
        recordObject.insert(STR_TWICE_DIVIDE_B_HIT_TIME, stMechRecord.dTwiceOpenBHitTime);
        recordObject.insert(STR_TWICE_DIVIDE_B_SUB_SWITCH_CLOSE_TIME, stMechRecord.dTwiceOpenBSubSwitchCloseTime);
        recordObject.insert(STR_TWICE_DIVIDE_B_LOOP_DOWN_TIME, stMechRecord.dTwiceOpenBLoopDownTime);
        recordObject.insert(STR_TWICE_DIVIDE_B_MAX_LOOP_CURRENT, stMechRecord.dTwiceOpenBMaxLoopCurrent);
        recordObject.insert(STR_TWICE_DIVIDE_C_LOOP_UP_TIME, stMechRecord.dTwiceOpenCLoopUpTime);
        recordObject.insert(STR_TWICE_DIVIDE_C_HIT_TIME, stMechRecord.dTwiceOpenCHitTime);
        recordObject.insert(STR_TWICE_DIVIDE_C_SUB_SWITCH_CLOSE_TIME, stMechRecord.dTwiceOpenCSubSwitchCloseTime);
        recordObject.insert(STR_TWICE_DIVIDE_C_LOOP_DOWN_TIME, stMechRecord.dTwiceOpenCLoopDownTime);
        recordObject.insert(STR_TWICE_DIVIDE_C_MAX_LOOP_CURRENT, stMechRecord.dTwiceOpenCMaxLoopCurrent);
        recordObject.insert(STR_MECH_A_START_CURRENT, stMechRecord.dAMechStartCurrent);
        recordObject.insert(STR_MECH_A_MAX_CURRENT, stMechRecord.dAMaxMechCurrent);
        recordObject.insert(STR_MECH_A_STORAGE_PERIOD, stMechRecord.dAMechStoragePeriod);
        recordObject.insert(STR_MECH_B_START_CURRENT, stMechRecord.dBMechStartCurrent);
        recordObject.insert(STR_MECH_B_MAX_CURRENT, stMechRecord.dBMaxMechCurrent);
        recordObject.insert(STR_MECH_B_STORAGE_PERIOD, stMechRecord.dBMechStoragePeriod);
        recordObject.insert(STR_MECH_C_START_CURRENT, stMechRecord.dCMechStartCurrent);
        recordObject.insert(STR_MECH_C_MAX_CURRENT, stMechRecord.dCMaxMechCurrent);
        recordObject.insert(STR_MECH_C_STORAGE_PERIOD, stMechRecord.dCMechStoragePeriod);

        QJsonObject dataObject;
        QJsonDocument document;
        document.setObject(recordObject);
        QByteArray compressingData = document.toJson(QJsonDocument::Compact);
        QByteArray compressedData;
        if (compress(compressingData, compressedData))
        {
            // 压缩成功
            dataObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_DO));
            dataObject.insert(STR_COMPRESS_DATA, QString::fromUtf8(compressedData.toBase64()));
        }
        else
        {
            // 压缩失败，则不使用压缩上传
            dataObject = recordObject;
            dataObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));
        }

        data.insert(STR_STATION_ID, stMechRecord.pointArchiveInfo.strStationGUID);
        data.insert(STR_STATION_PMS, stMechRecord.pointArchiveInfo.strStationPMS);
        data.insert(STR_DEVICE_ID, stMechRecord.pointArchiveInfo.strDeviceGUID);
        data.insert(STR_DEVICE_PMS, stMechRecord.pointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, stMechRecord.pointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, stMechRecord.aduId);
        data.insert(STR_CHANNEL_INDEX, stMechRecord.channelId);
        data.insert(STR_CHANNEL_ID, "");
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, stMechRecord.recordStringId);
        data.insert(STR_RECORD_DATA, dataObject);
    }
        break;
    case CHANNEL_TEMPERATURE:
    {
        TEMPRecord stTemperatureRecord = dbServer.getTEMPRecord(lIndex);

        QJsonObject recordObject;
        recordObject.insert(STR_DATE_TIMR, stTemperatureRecord.recordTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_TEMP_TEMPERATURE, double(stTemperatureRecord.temprature));
        recordObject.insert(STR_DATA_UNIT, DATA_UNIT_C);
        recordObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));

        data.insert(STR_STATION_ID, stTemperatureRecord.pointArchiveInfo.strStationGUID);
        data.insert(STR_STATION_PMS, stTemperatureRecord.pointArchiveInfo.strStationPMS);
        data.insert(STR_DEVICE_ID, stTemperatureRecord.pointArchiveInfo.strDeviceGUID);
        data.insert(STR_DEVICE_PMS, stTemperatureRecord.pointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, stTemperatureRecord.pointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, stTemperatureRecord.aduId);
        data.insert(STR_CHANNEL_INDEX, stTemperatureRecord.channelId);
        data.insert(STR_CHANNEL_ID, "");
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, stTemperatureRecord.recordStringId);
        data.insert(STR_RECORD_DATA, recordObject);
    }
        break;
    case CHANNEL_HUMIDITY:
    {
        HumidityRecord stHumidity = dbServer.getHumidityRecord(lIndex);

        QJsonObject recordObject;
        recordObject.insert(STR_DATE_TIMR, stHumidity.recordTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_HUMI_HUMIDITY, double(stHumidity.humanity));
        recordObject.insert(STR_DATA_UNIT, DATA_UNIT_PERCENT);
        recordObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));

        data.insert(STR_STATION_ID, stHumidity.pointArchiveInfo.strStationGUID);
        data.insert(STR_STATION_PMS, stHumidity.pointArchiveInfo.strStationPMS);
        data.insert(STR_DEVICE_ID, stHumidity.pointArchiveInfo.strDeviceGUID);
        data.insert(STR_DEVICE_PMS, stHumidity.pointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, stHumidity.pointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, stHumidity.aduId);
        data.insert(STR_CHANNEL_INDEX, stHumidity.channelId);
        data.insert(STR_CHANNEL_ID, "");
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, stHumidity.recordStringId);
        data.insert(STR_RECORD_DATA, recordObject);
    }
        break;
    case CHANNEL_FROST_RAW:
    {
        FrostRawRecord stFrostRaw = dbServer.getFrostRawRecord(lIndex);

        QJsonObject recordObject;
        recordObject.insert(STR_DATE_TIMR, stFrostRaw.recordTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_FROST_POINT_RAW, double(stFrostRaw.fFrostPointRaw));
        recordObject.insert(STR_DATA_UNIT, DATA_UNIT_C);
        recordObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));

        data.insert(STR_STATION_ID, stFrostRaw.pointArchiveInfo.strStationGUID);
        data.insert(STR_STATION_PMS, stFrostRaw.pointArchiveInfo.strStationPMS);
        data.insert(STR_DEVICE_ID, stFrostRaw.pointArchiveInfo.strDeviceGUID);
        data.insert(STR_DEVICE_PMS, stFrostRaw.pointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, stFrostRaw.pointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, stFrostRaw.aduId);
        data.insert(STR_CHANNEL_INDEX, stFrostRaw.channelId);
        data.insert(STR_CHANNEL_ID, "");
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, stFrostRaw.recordStringId);
        data.insert(STR_RECORD_DATA, recordObject);
    }
        break;
    case CHANNEL_FROST_ATM:
    {
        FrostAtmRecord stFrostAtm = dbServer.getFrostAtmRecord(lIndex);

        QJsonObject recordObject;
        recordObject.insert(STR_DATE_TIMR, stFrostAtm.recordTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_FROST_POINT_ATM, double(stFrostAtm.fFrostPointAtm));
        recordObject.insert(STR_DATA_UNIT, DATA_UNIT_C);
        recordObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));

        data.insert(STR_STATION_ID, stFrostAtm.pointArchiveInfo.strStationGUID);
        data.insert(STR_STATION_PMS, stFrostAtm.pointArchiveInfo.strStationPMS);
        data.insert(STR_DEVICE_ID, stFrostAtm.pointArchiveInfo.strDeviceGUID);
        data.insert(STR_DEVICE_PMS, stFrostAtm.pointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, stFrostAtm.pointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, stFrostAtm.aduId);
        data.insert(STR_CHANNEL_INDEX, stFrostAtm.channelId);
        data.insert(STR_CHANNEL_ID, "");
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, stFrostAtm.recordStringId);
        data.insert(STR_RECORD_DATA, recordObject);
    }
        break;
    case CHANNEL_DEW_RAW:
    {
        DewRawRecord stDewRaw = dbServer.getDewRawRecord(lIndex);

        QJsonObject recordObject;
        recordObject.insert(STR_DATE_TIMR, stDewRaw.recordTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_DEW_POINT_RAW, double(stDewRaw.fDewPointRaw));
        recordObject.insert(STR_DATA_UNIT, DATA_UNIT_C);
        recordObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));

        data.insert(STR_STATION_ID, stDewRaw.pointArchiveInfo.strStationGUID);
        data.insert(STR_STATION_PMS, stDewRaw.pointArchiveInfo.strStationPMS);
        data.insert(STR_DEVICE_ID, stDewRaw.pointArchiveInfo.strDeviceGUID);
        data.insert(STR_DEVICE_PMS, stDewRaw.pointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, stDewRaw.pointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, stDewRaw.aduId);
        data.insert(STR_CHANNEL_INDEX, stDewRaw.channelId);
        data.insert(STR_CHANNEL_ID, "");
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, stDewRaw.recordStringId);
        data.insert(STR_RECORD_DATA, recordObject);
    }
        break;
    case CHANNEL_DEW_ATM:
    {
        DewAtmRecord stDewAtm = dbServer.getDewAtmRecord(lIndex);

        QJsonObject recordObject;
        recordObject.insert(STR_DATE_TIMR, stDewAtm.recordTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_DEW_POINT_ATM, double(stDewAtm.fDewPointAtm));
        recordObject.insert(STR_DATA_UNIT, DATA_UNIT_C);
        recordObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));

        data.insert(STR_STATION_ID, stDewAtm.pointArchiveInfo.strStationGUID);
        data.insert(STR_STATION_PMS, stDewAtm.pointArchiveInfo.strStationPMS);
        data.insert(STR_DEVICE_ID, stDewAtm.pointArchiveInfo.strDeviceGUID);
        data.insert(STR_DEVICE_PMS, stDewAtm.pointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, stDewAtm.pointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, stDewAtm.aduId);
        data.insert(STR_CHANNEL_INDEX, stDewAtm.channelId);
        data.insert(STR_CHANNEL_ID, "");
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, stDewAtm.recordStringId);
        data.insert(STR_RECORD_DATA, recordObject);
    }
        break;
    case CHANNEL_MOISTURE:
    {
        MoistureRecord stMoisture = dbServer.getMoistureRecord(lIndex);

        QJsonObject recordObject;
        recordObject.insert(STR_DATE_TIMR, stMoisture.recordTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_MOIS_MOISTURE, double(stMoisture.fMoisture));
        recordObject.insert(STR_DATA_UNIT, DATA_UNIT_PPM);
        recordObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));

        data.insert(STR_STATION_ID, stMoisture.pointArchiveInfo.strStationGUID);
        data.insert(STR_STATION_PMS, stMoisture.pointArchiveInfo.strStationPMS);
        data.insert(STR_DEVICE_ID, stMoisture.pointArchiveInfo.strDeviceGUID);
        data.insert(STR_DEVICE_PMS, stMoisture.pointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, stMoisture.pointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, stMoisture.aduId);
        data.insert(STR_CHANNEL_INDEX, stMoisture.channelId);
        data.insert(STR_CHANNEL_ID, "");
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, stMoisture.recordStringId);
        data.insert(STR_RECORD_DATA, recordObject);
    }
        break;
    case CHANNEL_PRESS_ABSO:
    {
        PressAbsoRecord stAbsolutePressure = dbServer.getPressAbsoRecord(lIndex);

        QJsonObject recordObject;
        recordObject.insert(STR_DATE_TIMR, stAbsolutePressure.recordTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_CMD_PRESS_ABSO, double(stAbsolutePressure.fPressAbsolute));
        recordObject.insert(STR_DATA_UNIT, DATA_UNIT_BARA);
        recordObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));

        data.insert(STR_STATION_ID, stAbsolutePressure.pointArchiveInfo.strStationGUID);
        data.insert(STR_STATION_PMS, stAbsolutePressure.pointArchiveInfo.strStationPMS);
        data.insert(STR_DEVICE_ID, stAbsolutePressure.pointArchiveInfo.strDeviceGUID);
        data.insert(STR_DEVICE_PMS, stAbsolutePressure.pointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, stAbsolutePressure.pointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, stAbsolutePressure.aduId);
        data.insert(STR_CHANNEL_INDEX, stAbsolutePressure.channelId);
        data.insert(STR_CHANNEL_ID, "");
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, stAbsolutePressure.recordStringId);
        data.insert(STR_RECORD_DATA, recordObject);
    }
        break;
    case CHANNEL_PRESS_NORM:
    {
        PressNormRecord stNormalPressure = dbServer.getPressNormRecord(lIndex);

        QJsonObject recordObject;
        recordObject.insert(STR_DATE_TIMR, stNormalPressure.recordTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_CMD_PRESS_NORM, double(stNormalPressure.fPressNormal));
        recordObject.insert(STR_DATA_UNIT, DATA_UNIT_BARA);
        recordObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));

        data.insert(STR_STATION_ID, stNormalPressure.pointArchiveInfo.strStationGUID);
        data.insert(STR_STATION_PMS, stNormalPressure.pointArchiveInfo.strStationPMS);
        data.insert(STR_DEVICE_ID, stNormalPressure.pointArchiveInfo.strDeviceGUID);
        data.insert(STR_DEVICE_PMS, stNormalPressure.pointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, stNormalPressure.pointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, stNormalPressure.aduId);
        data.insert(STR_CHANNEL_INDEX, stNormalPressure.channelId);
        data.insert(STR_CHANNEL_ID, "");
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, stNormalPressure.recordStringId);
        data.insert(STR_RECORD_DATA, recordObject);
    }
        break;
    case CHANNEL_DENSITY:
    {
        DensityRecord stDensity = dbServer.getDensityRecord(lIndex);

        QJsonObject recordObject;
        recordObject.insert(STR_DATE_TIMR, stDensity.recordTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_DENS_DENSITY, double(stDensity.fDensity));
        recordObject.insert(STR_DATA_UNIT, DATA_UNIT_KG_PER_M3);
        recordObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));

        data.insert(STR_STATION_ID, stDensity.pointArchiveInfo.strStationGUID);
        data.insert(STR_STATION_PMS, stDensity.pointArchiveInfo.strStationPMS);
        data.insert(STR_DEVICE_ID, stDensity.pointArchiveInfo.strDeviceGUID);
        data.insert(STR_DEVICE_PMS, stDensity.pointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, stDensity.pointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, stDensity.aduId);
        data.insert(STR_CHANNEL_INDEX, stDensity.channelId);
        data.insert(STR_CHANNEL_ID, "");
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, stDensity.recordStringId);
        data.insert(STR_RECORD_DATA, recordObject);
    }
        break;
    case CHANNEL_OXYGEN:
    {
        OxygenRecord stOxygen = dbServer.getOxygenRecord(lIndex);

        QJsonObject recordObject;
        recordObject.insert(STR_DATE_TIMR, stOxygen.recordTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_OXY_OXYGEN, double(stOxygen.fOxygen));
        recordObject.insert(STR_DATA_UNIT, DATA_UNIT_PERCENT);
        recordObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));

        data.insert(STR_STATION_ID, stOxygen.pointArchiveInfo.strStationGUID);
        data.insert(STR_STATION_PMS, stOxygen.pointArchiveInfo.strStationPMS);
        data.insert(STR_DEVICE_ID, stOxygen.pointArchiveInfo.strDeviceGUID);
        data.insert(STR_DEVICE_PMS, stOxygen.pointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, stOxygen.pointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, stOxygen.aduId);
        data.insert(STR_CHANNEL_INDEX, stOxygen.channelId);
        data.insert(STR_CHANNEL_ID, "");
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, stOxygen.recordStringId);
        data.insert(STR_RECORD_DATA, recordObject);
    }
        break;
    case CHANNEL_SF6:
    {
        SF6Record stSF6 = dbServer.getSF6Record(lIndex);

        QJsonObject recordObject;
        recordObject.insert(STR_DATE_TIMR, stSF6.recordTime.toString("yyyy-MM-dd hh:mm:ss"));
        recordObject.insert(STR_SF6_SF6, double(stSF6.fSF6));
        recordObject.insert(STR_DATA_UNIT, DATA_UNIT_PERCENT);
        recordObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));

        data.insert(STR_STATION_ID, stSF6.pointArchiveInfo.strStationGUID);
        data.insert(STR_STATION_PMS, stSF6.pointArchiveInfo.strStationPMS);
        data.insert(STR_DEVICE_ID, stSF6.pointArchiveInfo.strDeviceGUID);
        data.insert(STR_DEVICE_PMS, stSF6.pointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, stSF6.pointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, stSF6.aduId);
        data.insert(STR_CHANNEL_INDEX, stSF6.channelId);
        data.insert(STR_CHANNEL_ID, "");
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, stSF6.recordStringId);
        data.insert(STR_RECORD_DATA, recordObject);
    }
        break;
    case CHANNEL_ARRESTER_I:        //避雷器电流数据
    case CHANNEL_ARRESTER_U:        //避雷器电压数据
    case CHANNEL_GROUNDDINGCURRENT:
    case CHANNEL_LEAKAGECURRENT:
        //case CHANNEL_ARRESTER:
    {

        ArresterRecord dataRecord = dbServer.getArresterRecord(static_cast<quint64>(lIndex));

        QJsonObject recordObject;
        recordObject.insert(STR_FREQUNCY, dataRecord.ucFrequency);
        recordObject.insert("ePhase", (int)dataRecord.eChannelPhase + 1);
        recordObject.insert(STR_DATE_TIMR, dataRecord.datetime.toString("yyyy-MM-dd hh:mm:ss"));
        if ( dataRecord.vecCurrentDataChannel.size() == 10 )
        {
            recordObject.insert("harmonicTotalI", dataRecord.vecCurrentDataChannel.at(0));
            recordObject.insert("harmonicI1", dataRecord.vecCurrentDataChannel.at(2));
            recordObject.insert("harmonicI3", dataRecord.vecCurrentDataChannel.at(4));
            recordObject.insert("harmonicI5", dataRecord.vecCurrentDataChannel.at(6));
            recordObject.insert("harmonicI7", dataRecord.vecCurrentDataChannel.at(8));
        }
        if ( dataRecord.vecResistiveCurrentData.size() == 5 )
        {
            recordObject.insert("impdHarmonicTotalI", dataRecord.vecResistiveCurrentData.at(0));
            recordObject.insert("impdHarmonicI1", dataRecord.vecResistiveCurrentData.at(1));
            recordObject.insert("impdHarmonicI3", dataRecord.vecResistiveCurrentData.at(2));
            recordObject.insert("impdHarmonicI5", dataRecord.vecResistiveCurrentData.at(3));
            recordObject.insert("impdHarmonicI7", dataRecord.vecResistiveCurrentData.at(4));
        }

        // 站点PMS
        data.insert(STR_STATION_PMS, dataRecord.stPointArchiveInfo.strStationPMS);
        // 监测站ID
        data.insert(STR_STATION_ID, dataRecord.stPointArchiveInfo.strStationGUID);
        // 一次设备ID
        data.insert(STR_DEVICE_ID, dataRecord.stPointArchiveInfo.strDeviceGUID);
        // 一次设备PMS
        data.insert(STR_DEVICE_PMS, dataRecord.stPointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, dataRecord.stPointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, dataRecord.strADUID);
        // 通道索引
        data.insert(STR_CHANNEL_INDEX, dataRecord.ucChannelID);
        // 通道序号
        data.insert(STR_CHANNEL_ID, dataRecord.ucChannelID);
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, dataRecord.strRecordID);
        // 压缩标识
        recordObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));
        data.insert(STR_RECORD_DATA, recordObject);
    }
        break;
    case CHANNEL_VIBRATION:     // 振动
    {
        VibrationRecord vibData;
        bool result = dbServer.getVibrationRecord(static_cast<quint64>(lIndex), vibData);
        if(!result)
        {
            PDS_SYS_ERR_LOG("vibration data record has error");
            break;
        }

        QJsonObject recordObject;
        recordObject.insert("fTemperature", QString::number(static_cast<double>(vibData.fTemperature), 'f', 2));
        recordObject.insert("fHumidity", QString::number(static_cast<double>(vibData.fHumidity), 'f', 2));
        recordObject.insert("ucSampleCycleCount", vibData.ucSampleCycleCount);
        recordObject.insert("usSampleCountCycle", vibData.usSampleCountCycle);
        // X轴数据
        recordObject.insert("iAmplitudeMaxX", vibData.stVibrationDataX.iAmplitudeMax);
        recordObject.insert("iAmplitudeAverageX", vibData.stVibrationDataX.iAmplitudeAverage);
        recordObject.insert("dAccelerationMaxX", vibData.stVibrationDataX.dAccelerationMax);
        recordObject.insert("dAccelerationAverageX", QString::number(vibData.stVibrationDataX.dAccelerationAverage, 'f', 2));
        if ( vibData.stVibrationDataX.vecMaxFreqs.size() == 3 )
        {
            recordObject.insert("dMaxFreqs1X", QString::number(vibData.stVibrationDataX.vecMaxFreqs.at(0), 'f', 2));
            recordObject.insert("dMaxFreqs2X", QString::number(vibData.stVibrationDataX.vecMaxFreqs.at(1), 'f', 2));
            recordObject.insert("dMaxFreqs3X", QString::number(vibData.stVibrationDataX.vecMaxFreqs.at(2), 'f', 2));
        }
        // 时域数据和时间序列
        QJsonArray arrayTmX, arrayTmAisX;
        double dTime = 0;
        if ( vibData.usSampleCountCycle != 0 && vibData.ucFrequency != 0 )
        {
            dTime = 1.0 / vibData.usSampleCountCycle / vibData.ucFrequency * 1000;
        }
        else
        {
            PDS_SYS_ERR_LOG("vibration data record has error 2.");
            break;
        }

        for ( int i=0; i<vibData.stVibrationDataX.vecArray.size(); i++ )
        {
            arrayTmX.append( QString::number(vibData.stVibrationDataX.vecArray.at(i), 'f', 3) );
            arrayTmAisX.append( QString::number(dTime*i, 'f', 2) );
        }
        recordObject.insert("dTimeDomainArrayX", arrayTmX);
        recordObject.insert("dTimeArrayX", arrayTmAisX);
        // 计算频域数据
        QVector<double> vecFreqVal, vecAmpVal;
        QJsonArray arrayFqX, arrayFqAisX;
        SignalAlgorithm::FFT(vibData.stVibrationDataX.vecArray, vibData.ucFrequency * vibData.usSampleCountCycle, vecFreqVal, vecAmpVal);
        for ( int i=0; i<vecFreqVal.size(); i++ )
        {
            arrayFqX.append(QString::number(vecAmpVal.at(i), 'f', 3));
            arrayFqAisX.append(QString::number(vecFreqVal.at(i), 'f', 2));
        }
        recordObject.insert("dFreqDomainArrayX", arrayFqX);
        recordObject.insert("dFreqArrayX", arrayFqAisX);

        // Y轴数据
        recordObject.insert("iAmplitudeMaxY", vibData.stVibrationDataY.iAmplitudeMax);
        recordObject.insert("iAmplitudeAverageY", vibData.stVibrationDataY.iAmplitudeAverage);
        recordObject.insert("dAccelerationMaxY", vibData.stVibrationDataY.dAccelerationMax);
        recordObject.insert("dAccelerationAverageY", QString::number(vibData.stVibrationDataY.dAccelerationAverage, 'f', 2));
        if ( vibData.stVibrationDataY.vecMaxFreqs.size() == 3 )
        {
            recordObject.insert("dMaxFreqs1Y", QString::number(vibData.stVibrationDataY.vecMaxFreqs.at(0), 'f', 2));
            recordObject.insert("dMaxFreqs2Y", QString::number(vibData.stVibrationDataY.vecMaxFreqs.at(1), 'f', 2));
            recordObject.insert("dMaxFreqs3Y", QString::number(vibData.stVibrationDataY.vecMaxFreqs.at(2), 'f', 2));
        }
        QJsonArray arrayTmY, arrayTmAisY;
        for ( int i=0; i<vibData.stVibrationDataY.vecArray.size(); i++ )
        {
            arrayTmY.append(QString::number(vibData.stVibrationDataY.vecArray.at(i), 'f', 3));
            arrayTmAisY.append( QString::number(dTime*i, 'f', 2) );
        }
        recordObject.insert("dTimeDomainArrayY", arrayTmY);
        recordObject.insert("dTimeArrayY", arrayTmAisY);
        // 计算时域数据
        vecFreqVal.clear();
        vecAmpVal.clear();
        QJsonArray arrayFqY, arrayFqAisY;
        SignalAlgorithm::FFT(vibData.stVibrationDataY.vecArray, vibData.ucFrequency * vibData.usSampleCountCycle, vecFreqVal, vecAmpVal);
        for ( int i=0; i<vecFreqVal.size(); i++ )
        {
            arrayFqY.append(QString::number(vecAmpVal.at(i), 'f', 3));
            arrayFqAisY.append(QString::number(vecFreqVal.at(i), 'f', 2));
        }
        recordObject.insert("dFreqDomainArrayY", arrayFqY);
        recordObject.insert("dFreqArrayY", arrayFqAisY);

        // Z轴数据
        recordObject.insert("iAmplitudeMaxZ", vibData.stVibrationDataZ.iAmplitudeMax);
        recordObject.insert("iAmplitudeAverageZ", vibData.stVibrationDataZ.iAmplitudeAverage);
        recordObject.insert("dAccelerationMaxZ", vibData.stVibrationDataZ.dAccelerationMax);
        recordObject.insert("dAccelerationAverageZ", QString::number(vibData.stVibrationDataZ.dAccelerationAverage, 'f', 2));
        if ( vibData.stVibrationDataZ.vecMaxFreqs.size() == 3 )
        {
            recordObject.insert("dMaxFreqs1Z", QString::number(vibData.stVibrationDataZ.vecMaxFreqs.at(0), 'f', 2));
            recordObject.insert("dMaxFreqs2Z", QString::number(vibData.stVibrationDataZ.vecMaxFreqs.at(1), 'f', 2));
            recordObject.insert("dMaxFreqs3Z", QString::number(vibData.stVibrationDataZ.vecMaxFreqs.at(2), 'f', 2));
        }
        QJsonArray arrayTmZ, arrayTmAisZ;
        for ( int i=0; i<vibData.stVibrationDataZ.vecArray.size(); i++ )
        {
            arrayTmZ.append( QString::number(vibData.stVibrationDataZ.vecArray.at(i), 'f', 3) );
            arrayTmAisZ.append( QString::number(dTime*i, 'f', 2) );
        }
        recordObject.insert("dTimeDomainArrayZ", arrayTmZ);
        recordObject.insert("dTimeArrayZ", arrayTmAisZ);
        // 计算时域数据
        vecFreqVal.clear();
        vecAmpVal.clear();
        QJsonArray arrayFqZ, arrayFqAisZ;
        SignalAlgorithm::FFT(vibData.stVibrationDataZ.vecArray, vibData.ucFrequency * vibData.usSampleCountCycle, vecFreqVal, vecAmpVal);
        for ( int i=0; i<vecFreqVal.size(); i++ )
        {
            arrayFqZ.append(QString::number(vecAmpVal.at(i), 'f', 3));
            arrayFqAisZ.append(QString::number(vecFreqVal.at(i), 'f', 3));
        }
        recordObject.insert("dFreqDomainArrayZ", arrayFqZ);
        recordObject.insert("dFreqArrayZ", arrayFqAisZ);
        // 数据时间
        recordObject.insert(STR_DATE_TIMR, vibData.datetime.toString("yyyy-MM-dd hh:mm:ss"));

        QJsonObject dataObject;
        QString strData;
        if (compressData(recordObject, strData))
        {
            // 压缩成功
            dataObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_DO));
            dataObject.insert(STR_COMPRESS_DATA, strData);
        }
        else
        {
            // 压缩失败
            dataObject = recordObject;
            dataObject.insert(STR_COMPRESS_FLAG, qint32(COMPRESS_FLAG_NOT));
        }

        // 站点PMS
        data.insert(STR_STATION_PMS, vibData.stPointArchiveInfo.strStationPMS);
        // 监测站ID
        data.insert(STR_STATION_ID, vibData.stPointArchiveInfo.strStationGUID);
        // 一次设备ID
        data.insert(STR_DEVICE_ID, vibData.stPointArchiveInfo.strDeviceGUID);
        // 一次设备PMS
        data.insert(STR_DEVICE_PMS, vibData.stPointArchiveInfo.strDevicePMS);
        data.insert(STR_POINT_ID, vibData.stPointArchiveInfo.strPointGUID);
        data.insert(STR_ADU_ID, vibData.strADUID);
        // 通道索引
        data.insert(STR_CHANNEL_INDEX, vibData.ucChannelID);
        // 通道序号
        data.insert(STR_CHANNEL_ID, vibData.ucChannelID);
        data.insert(STR_CHANNEL_TYPE, eADUChannelType);
        data.insert(STR_DATA_NUM, lIndex);
        data.insert(STR_DATA_ID, vibData.strRecordID);
        // 数据部分
        data.insert(STR_RECORD_DATA, dataObject);
    }
        break;
    default:
        break;
    }

    return bret;
}

/************************************************
 * 函数名:  compress
 * 输入参数: data 待压缩数据
 * 输出参数:  compressedData 压缩后的数据
 * 返回值:  true-压缩成功 false-压缩失败
 * 功能: 数据压缩
 ************************************************/
bool PDServerData::compress(const QByteArray &data, QByteArray &compressedData)
{
    compressedData = GZip::gzipCompress(data);
    return !compressedData.isEmpty();
}

/************************************************
 * 输入参数: jsObj -- 待压缩的Json对象
 * 输出参数: strData -- 压缩后的数据
 * 返回值:  true-压缩成功 false-压缩失败
 * 功能: 数据压缩
 ************************************************/
bool PDServerData::compressData(const QJsonObject &jsObj, QString &strData)
{
    bool bRes = false;
    QJsonDocument document;
    document.setObject(jsObj);
    QByteArray compressingData = document.toJson();
    QByteArray compressedData = GZip::gzipCompress(compressingData);
    if ( !compressedData.isEmpty() )
    {
        bRes = true;
        strData = QString::fromUtf8(compressedData.toBase64());
    }

    return bRes;
}

/************************************************
 * 函数名:  setStationInfo
 * 输入参数:  strStationName -- 站点名
 *          strVoltage -- 站点电压等级
 * 输出参数:  NULL
 * 返回值:  操作结果
 * 功能: 设置站点信息（站点名，电压等级）
 ************************************************/
bool PDServerData::setStationInfo(const QString &strStationName, VoltageLevel eVoltage)
{
    ConfigService &configService = ConfigService::instance();
    return configService.saveStationVoltage(strStationName, eVoltage);
}

/************************************************
 * 函数名:  getStationInfo
 * 输入参数:  iPage -- 页码
 *          iCountPerPage -- 每页个数
 * 输出参数:  jsonStationInfo -- 站点信息
 * 返回值:  NULL
 * 功能: 获取站点信息
 ************************************************/
void PDServerData::getStationInfo( int iPage, int iCountPerPage, QJsonObject &jsonStationInfo )
{
    ConfigService &configService = ConfigService::instance();
    const StationNode &deviceTree = configService.stationNode();
    jsonStationInfo.insert(STR_STATION_NAME, deviceTree.strName);
    jsonStationInfo.insert(STR_STATION_ID, deviceTree.strSiteGUID);
    jsonStationInfo.insert(STR_STATION_PMS, deviceTree.strPMS);
    QJsonArray jsondevice;
    jsonStationInfo.insert(STR_DEVICE_COUNT, getDeviceInfo(iPage, iCountPerPage, jsondevice));
    jsonStationInfo.insert(STR_DEVICE_ITEMS, jsondevice);
}

/************************************************
 * 函数名:  isMonitorRight
 * 输入参数: strMonitorID -- 检测设备ID
 * 输出参数:  NULL
 * 返回值:  操作结果
 * 功能: 验证是否为本设备
 ************************************************/
bool PDServerData::isMonitorRight(const QString &strMonitorID)
{
    bool bRet = false;
    if (strMonitorID == monitorID())
    {
        bRet = true;
    }

    return bRet;
}

/************************************************
 * 函数名:  getChannelInfo
 * 输入参数:  strADUID -- 前端编号
 *          iPage -- 页码
 *          iCountPerPage -- 每页数量
 * 输出参数:  data -- 数据
 * 返回值:  所有通道的数量
 * 功能: 获取指定前端下的通道信息
 ************************************************/
int PDServerData::getChannelInfo(const QString &strADUID,int ipage, int ipageSize, QJsonArray& data)
{
    Q_UNUSED(ipage);
    Q_UNUSED(ipageSize);

    int iChannelNUM = 0;
    ConfigService &configService = ConfigService::instance();
    const QList<ADUUnitInfo> &ADUList = configService.ADUList();
    for (int j = 0; j < ADUList.size(); j++)
    {
        if (ADUList.at(j).strID ==  strADUID)//指定前端
        {
            const ADUUnitInfo &adu = ADUList.at(j);
            for ( int k = 0; k < adu.Channels.size(); k++)
            {
                const ADUChannelInfo &channel = adu.Channels.at(k);
                iChannelNUM++;

                QJsonObject jsonChannel;
                jsonChannel.insert(STR_CHANNEL_INDEX,channel.unID);
                jsonChannel.insert(STR_CHANNEL_NAME,channel.strName);
                jsonChannel.insert(STR_CHANNEL_ABN_NAME,"");
                jsonChannel.insert(STR_CHANNEL_MODEL,"");
                jsonChannel.insert("SN","");
                jsonChannel.insert(STR_CHANNEL_TYPE, channel.etype);

                test::ADUUpdateStateInfo stateInfo;
                bool onLine;
                if(ConfigService::instance().getAduBusState(adu.strID, stateInfo))
                {
                    if(stateInfo.isOnLine())
                    {
                        onLine = true;
                    }
                    else
                    {
                        onLine = false;
                    }
                }
                else
                {
                    onLine = false;
                }

                jsonChannel.insert(STR_ADU_CONNECTION, onLine);

                jsonChannel.insert(STR_CHANNEL_ID, "");
                getChannelPara(channel.etype, channel.stachpara, jsonChannel);
                data.append(jsonChannel);
            }
        }
    }

    return iChannelNUM;
}

/************************************************
 * 函数名:  getDeviceInfo
 * 输入参数:  iPage -- 页码
 *          iCountPerPage -- 每页数量
 * 输出参数:  data -- 数据
 * 返回值:  一次设备的数量
 * 功能: 获取一次设备信息
 ************************************************/
int PDServerData::getDeviceInfo(int iPage, int iCountPerPage, QJsonArray& data)
{
    Q_UNUSED(iPage);
    Q_UNUSED(iCountPerPage);

    int iDeviceNUM = 0;
    ConfigService &configService = ConfigService::instance();
    const StationNode &deviceTree = configService.stationNode();
    for (int i = 0; i < deviceTree.devices.size(); i++)
    {
        iDeviceNUM++;
        const DeviceNode &device = deviceTree.devices.at(i);
        QJsonObject jsonDevice;
        jsonDevice.insert(STR_DEVICE_ID,device.strDeviceGUID);
        jsonDevice.insert(STR_DEVICE_NAME,device.strName);
        jsonDevice.insert(STR_DEVICE_PMS,device.strPMS);
        jsonDevice.insert(STR_POINT_COUNT,device.testPoints.size());
        QJsonArray jsonADUs;
        for (int j = 0; j < device.testPoints.size(); j++)
        {
            QJsonObject jsonadu;
            getPonitInfo(jsonadu, device.testPoints.at(j));
            jsonADUs.append(jsonadu);
        }
        jsonDevice.insert(STR_ADU_ITEMS,jsonADUs);
        data.append(jsonDevice);
    }
    return iDeviceNUM;
}

/************************************************
 * 函数名:  getPonitInfo
 * 输入参数:  stPoint -- 测点数据
 * 输出参数:  jsonPoint -- 测点数据
 * 返回值:  NULL
 * 功能:  获取测点数据
 ************************************************/
void PDServerData::getPonitInfo(QJsonObject &jsonPoint, const TestPointInfo &stPoint)
{
    jsonPoint.insert(STR_POINT_ID, stPoint.strPointGUID);
    jsonPoint.insert(STR_POINT_NAME, stPoint.strName);
    QMap< QString, QList<int> > mapADUInfo;
    for (int i = 0; i < stPoint.ConnectionInfo.size(); i++)
    {
        if (mapADUInfo.contains(stPoint.ConnectionInfo.at(i).strID))
        {
            mapADUInfo[stPoint.ConnectionInfo.at(i).strID].append(stPoint.ConnectionInfo.at(i).unID);
        }
        else
        {
            QList<int> listChannelID;
            listChannelID.append(stPoint.ConnectionInfo.at(i).unID);
            mapADUInfo.insert(stPoint.ConnectionInfo.at(i).strID, listChannelID);
        }
    }

    QJsonArray jsonadus;
    QStringList listADUs = mapADUInfo.keys();
    for (int i = 0; i < listADUs.size(); i++)
    {
        ADUUnitInfo aduInfo;
        if (ConfigService::instance().getADU(listADUs.at(i), aduInfo))
        {
            QJsonObject jsonadu;
            jsonadu.insert(STR_ADU_NAME, aduInfo.strName);
            jsonadu.insert(STR_ADU_ID, aduInfo.strID);

            QJsonArray jsonChannels;
            QList<int> listChannelID = mapADUInfo.value(listADUs.at(i));
            for (int j = 0; j < listChannelID.size(); j++)
            {
                QJsonObject jsonChannel;
                jsonChannel.insert(STR_CHANNEL_NAME, aduInfo.Channels.at(listChannelID.at(j)).strName);
                jsonChannel.insert(STR_CHANNEL_ID, aduInfo.Channels.at(listChannelID.at(j)).unID);
                jsonChannels.append(jsonChannel);
            }

            jsonadu.insert(STR_CHANNEL_COUNT, listChannelID.size());
            jsonadu.insert(STR_CHANNEL_ITEM, jsonChannels);
            jsonadus.append(jsonadu);
        }
    }
    jsonPoint.insert(STR_ADU_COUNT, listADUs.size());
    jsonPoint.insert(STR_ADU_ITEMS, jsonadus);

}

/************************************************
 * 函数名: getVpnKeyName
 * 输入参数: NULL
 * 输出参数: NULL
 * 返回值:  VPN密钥名称
 * 功能:  获取VPN密钥名称
 ************************************************/
QString PDServerData::getVpnKeyName()
{
    QString strSVPKey;
    QDir dir("/etc/openvpn/");  //openvpn的路径
    dir.setFilter(QDir::Dirs | QDir::Files);
    dir.setSorting(QDir::DirsFirst);
    QStringList filter;
    filter<<"*.csr*";
    QFileInfoList fileList = dir.entryInfoList(filter);

    for (int i = 0; i < fileList.size(); i++)
    {
        strSVPKey = fileList.at(i).baseName();
    }

    return strSVPKey;
}


/************************************************
 * 函数名:  getADUInfo
 * 输入参数:  adu -- 前端结构体
 * 输出参数:  jsonadu -- json树
 * 返回值: NULL
 * 功能: 将前端数据转换为json
 ************************************************/
void PDServerData::getADUInfo(QJsonObject &jsonadu, const ADUUnitInfo &adu)
{
    jsonadu.insert(STR_ADU_ID, adu.strID);
    QString strName = ConfigService::instance().getADUTypName(adu.eType);
    jsonadu.insert(STR_ADU_TYPE, strName);
    jsonadu.insert(STR_POINT_COUNT, adu.Channels.size());
    QJsonArray jsonChannels;
    for (int i = 0; i < adu.Channels.size(); i++)
    {
        QJsonObject jsonChannel;
        const ADUChannelInfo &channel = adu.Channels.at(i);
        jsonChannel.insert(STR_CHANNEL_INDEX, channel.unID);
        jsonChannel.insert(STR_CHANNEL_NAME, channel.strName);
        jsonChannel.insert(STR_CHANNEL_TYPE, channel.etype);
        jsonChannels.append(jsonChannel);
    }
    jsonadu.insert(STR_POINT_ITEMS, jsonChannels);
}

void PDServerData::getArchivesInfo(QJsonObject &json, int idType, int level, QString objectId)
{
    QJsonObject jsonInfo;

    const StationNode &stationNode = ConfigService::instance().stationNode();

    if ( idType == 0 )          //查询全部
    {
        QJsonArray jsonArray;
        QJsonObject jsonStation;
        getStationInfo(stationNode, jsonStation);
        jsonArray.append(jsonStation);
        jsonInfo.insert(Str_Station, jsonArray);
    }
    else if ( idType == 1 )     //查询指定站
    {
        if ( objectId != stationNode.strSiteGUID )
        {
            PDS_SYS_ERR_LOG("station id not match %s %s", objectId.toLatin1().data(), stationNode.strSiteGUID.toLatin1().data());
            return;
        }

        QJsonArray jsonArray;
        QJsonObject jsonStation;
        getStationInfo(stationNode, jsonStation, level);
        jsonArray.append(jsonStation);
        jsonInfo.insert(Str_Station, jsonArray);
    }
    else if ( idType == 2 )     //查询指定设备
    {
        for (int i=0; i<stationNode.devices.size(); i++)
        {
            if ( stationNode.devices.at(i).strDeviceGUID == objectId )
            {
                QJsonArray jsonArray;
                QJsonObject jsonObj;
                getDeviceInfo(stationNode.devices.at(i), jsonObj, level);
                jsonArray.append(jsonObj);
                jsonInfo.insert(Str_Device, jsonArray);
                break;
            }
        }
    }
    else if ( idType == 3 )     //查询指定测点
    {
        TestPointInfo tpInfo;
        if ( ConfigService::instance().getTestPoint(objectId, tpInfo) )
        {
            //bool bSub = (level != 1);
            //直接查询测点时，带上关联adu
            bool bSub = true;
            QJsonArray jsonArray;
            QJsonObject jsonObj;
            getTestPointInfo(tpInfo, jsonObj, bSub);
            jsonArray.append(jsonObj);
            jsonInfo.insert(Str_TestPoint, jsonArray);
        }
    }

    json.insert(Str_Archieve, jsonInfo);
}

void PDServerData::setArchivesInfo(QJsonArray &lstRes, const QJsonArray &lstPam, int idType, QString objectId)
{
    if ( idType == 1 )         //站id
    {
        setStationInfo(lstRes, lstPam, objectId);
    }
    else if ( idType == 2 )    //设备id
    {
        setDeviceInfo(lstRes, lstPam, objectId);
    }
    else if ( idType == 3 )    //测点id
    {
        setTestPointInfo(lstRes, lstPam, objectId);
    }
}

void PDServerData::getEquipmentInfo(QJsonObject &json, int idType, int level, QString objectId)
{
    QJsonObject jsonInfo;

    if ( idType == 0 )          //查询全部
    {
        QJsonArray jsonArray;
        QJsonObject jsonMonitor;
        getMonitorInfo(jsonMonitor, 0);
        jsonArray.append(jsonMonitor);
        jsonInfo.insert(Str_Monitor, jsonArray);
    }
    else if ( idType == 1 )     //查询主机
    {
        const SystemSetting &sysSet = ConfigService::instance().systemSettings();
        if ( objectId != sysSet.strMonitorID )
        {
            PDS_SYS_ERR_LOG("monitor id not match: %s %s", objectId.toLatin1().data(), sysSet.strMonitorID.toLatin1().data());
            return;
        }

        QJsonArray jsonArray;
        QJsonObject jsonMonitor;
        getMonitorInfo(jsonMonitor, 0);
        jsonArray.append(jsonMonitor);
        jsonInfo.insert(Str_Monitor, jsonArray);
    }
    else if ( idType == 2 )     //查询指定前端
    {
        ADUUnitInfo aduInfo;
        if ( ConfigService::instance().getADU(objectId, aduInfo) )
        {
            bool bSub = (level != 1);
            QJsonArray jsonArray;
            QJsonObject jsonObj;
            getAduInfo(jsonObj, aduInfo, bSub);
            jsonArray.append(jsonObj);
            jsonInfo.insert(Str_Adu, jsonArray);
        }
    }
    else if ( idType == 3 )     //查询指定channelId
    {
        QStringList lstPart = objectId.split('/');
        if ( lstPart.size() == 2 )
        {
            ADUChannelInfo aduChannel;
            if ( ConfigService::instance().getChannelInfo(lstPart.first(), lstPart.last().toUInt(), aduChannel) )
            {
                QJsonArray jsonArray;
                QJsonObject jsonObj;
                getChannelInfo(jsonObj, aduChannel, lstPart.first());
                jsonArray.append(jsonObj);
                jsonInfo.insert(Str_Channel, jsonArray);
            }
        }
    }

    json.insert(Str_Equipment, jsonInfo);
}

void PDServerData::setEquipmentInfo(QJsonArray &lstRes, const QJsonArray &lstPam, int idType, QString objectId)
{
    if ( idType == 1 )         //主机id
    {
        setMonitorInfo(lstRes, lstPam, objectId);
    }
    else if ( idType == 2 )    //前端id
    {
        setAduInfo(lstRes, lstPam, objectId);
    }
    else if ( idType == 3 )    //channelId
    {
        setChannelInfo(lstRes, lstPam, objectId);
    }
}

void PDServerData::getStationInfo(const StationNode &station, QJsonObject &json, int nLevel)
{
    json.insert(Str_StationId, station.strSiteGUID);
    json.insert(Str_StationPms, station.strPMS);
    json.insert(Str_StationName, station.strName);
    json.insert(Str_StationVol, station.eVoltage);

    if ( nLevel != 1 )
    {
        int nSubLevel = 0;
        if ( nLevel == 2 )
        {
            nSubLevel = 1;
        }

        QJsonArray jsonArray;
        for ( int i=0; i<station.devices.size(); i++ )
        {
            QJsonObject jsonObj;
            getDeviceInfo(station.devices.at(i), jsonObj, nSubLevel);
            jsonArray.append(jsonObj);
        }

        if ( !jsonArray.isEmpty() )
        {
            json.insert(Str_Device, jsonArray);
        }
    }
}

void PDServerData::setStationInfo(QJsonArray &lstRes, const QJsonArray &lstPam, QString objectId)
{
    StationNode sNode = ConfigService::instance().stationNode();
    if ( sNode.strSiteGUID != objectId  )
    {
        for ( int i=0; i<lstPam.size(); i++ )
        {
            QJsonObject jsonRes;
            jsonRes.insert("paramName", lstPam.at(i).toObject().value("paramName"));
            jsonRes.insert("result", 0);
        }
        return;
    }

    QString strKey;
    int nFlag = 0;
    for ( int i=0; i<lstPam.size(); i++ )
    {
        QJsonObject jsonObj = lstPam.at(i).toObject();
        strKey = jsonObj.value("paramName").toString();
        QJsonValue jsValue = jsonObj.value("value");

        QJsonObject jsonRes;
        jsonRes.insert("paramName", strKey);
        jsonRes.insert("result", 1);

        nFlag++;
        if ( strKey == Str_StationId )
        {
            sNode.strSiteGUID = jsValue.toString();
        }
        else if ( strKey == Str_StationPms )
        {
            sNode.strPMS = jsValue.toString();
        }
        else if ( strKey == Str_StationName )
        {
            sNode.strName = jsValue.toString();
        }
        else if ( strKey == Str_StationVol )
        {
            sNode.eVoltage = (VoltageLevel)jsValue.toInt();
        }
        else
        {
            nFlag--;
            jsonRes["result"] = 0;
        }

        lstRes.append(jsonRes);
    }

    if ( nFlag > 0 )
    {
        ConfigService::instance().saveStationInfo(sNode);
    }
}

void PDServerData::getDeviceInfo(const DeviceNode &device, QJsonObject &json, int nLevel)
{
    json.insert(STR_DEVICE_ID, device.strDeviceGUID);
    json.insert(STR_DEVICE_PMS, device.strPMS);
    json.insert(STR_DEVICE_NAME, device.strName);
    json.insert(Str_DeviceVoltage, device.eVoltage);
    json.insert(Str_DeviceType, device.eDeviceType);

    if ( nLevel != 1 )
    {
        bool bSub = (nLevel==0);
        QJsonArray jsonArray;
        for ( int i=0; i<device.testPoints.size(); i++ )
        {
            QJsonObject jsonObj;
            getTestPointInfo(device.testPoints.at(i), jsonObj, bSub);
            jsonArray.append(jsonObj);
        }

        if ( !jsonArray.isEmpty() )
        {
            json.insert(Str_TestPoint, jsonArray);
        }
    }
}

void PDServerData::setDeviceInfo(QJsonArray &lstRes, const QJsonArray &lstPam, QString objectId)
{
    // 判断是否存在
    DeviceNode devNode;
    if ( !ConfigService::instance().getDevice(objectId, devNode) )
    {
        for ( int i=0; i<lstPam.size(); i++ )
        {
            QJsonObject jsonRes;
            jsonRes.insert("paramName", lstPam.at(i).toObject().value("paramName"));
            jsonRes.insert("result", 0);
        }
        return;
    }

    QString strKey;
    int nFlag = 0;
    for ( int i=0; i<lstPam.size(); i++ )
    {
        QJsonObject jsonObj = lstPam.at(i).toObject();
        strKey = jsonObj.value("paramName").toString();
        QJsonValue jsValue = jsonObj.value("value");

        QJsonObject jsonRes;
        jsonRes.insert("paramName", strKey);
        jsonRes.insert("result", 1);

        nFlag++;
        // deviceid不能修改
        if ( strKey == STR_DEVICE_PMS )
        {
            devNode.strPMS = jsValue.toString();
        }
        else if ( strKey == STR_DEVICE_NAME )
        {
            devNode.strName = jsValue.toString();
        }
        else if ( strKey == Str_DeviceVoltage )
        {
            devNode.eVoltage = (VoltageLevel)jsValue.toInt();
        }
        else if ( strKey == Str_DeviceType )
        {
            devNode.eDeviceType = (DeviceType)jsValue.toInt();
        }
        else
        {
            nFlag--;
            jsonRes["result"] = 0;
        }

        lstRes.append(jsonRes);
    }

    if ( nFlag > 0 )
    {
        ConfigService::instance().updateDevice(devNode);
    }
}

int PDServerData::addDeviceInfo(const QJsonObject &json)
{
    // 必要字段检查
    if ( !json.contains(STR_DEVICE_ID) ||
         !json.contains(STR_DEVICE_PMS) ||
         !json.contains(STR_DEVICE_NAME) ||
         !json.contains(Str_DeviceType) )
    {
        return 0;
    }
    // 是否已存在
    DeviceNode devNode;
    devNode.strDeviceGUID = json.value(STR_DEVICE_ID).toString();
    if ( ConfigService::instance().isDeviceIDExisted(devNode.strDeviceGUID) )
    {
        return 0;
    }
    // 解析字段
    QStringList lstKeys = json.keys();
    for ( int i=0; i<lstKeys.size(); i++ )
    {
        if ( lstKeys.at(i) == STR_DEVICE_ID )
        {
        }
        else if ( lstKeys.at(i) == STR_DEVICE_PMS )
        {
            devNode.strPMS = json.value(lstKeys.at(i)).toString();
        }
        else if ( lstKeys.at(i) == STR_DEVICE_NAME )
        {
            devNode.strName = json.value(lstKeys.at(i)).toString();
        }
        else if ( lstKeys.at(i) == Str_DeviceType )
        {
            devNode.eDeviceType = static_cast<DeviceType>(json.value(lstKeys.at(i)).toInt());
        }
        else if ( lstKeys.at(i) == Str_DeviceVoltage )
        {
            devNode.eVoltage = static_cast<VoltageLevel>(json.value(lstKeys.at(i)).toInt());
        }
        else
        {
            PDS_SYS_WARNING_LOG("unknown field: %s", lstKeys.at(i).toLatin1().data());
        }
    }
    // 添加设备
    ConfigService::instance().addDevice(devNode);

    return 1;
}

int PDServerData::delDeviceInfo(const QString &strId)
{
    if ( CONFIG_NO_ERROR == ConfigService::instance().delDevice(strId) )
    {
        return 1;
    }

    return 0;
}

void PDServerData::getTestPointInfo(const TestPointInfo &testPoint, QJsonObject &json, bool bSub)
{
    json.insert(Str_TestPointId, testPoint.strPointGUID);
    json.insert(Str_TestPointName, testPoint.strName);
    if ( bSub )
    {
        const QList<PointConnectionInfo> &lstAdus = testPoint.ConnectionInfo;
        QJsonArray jsonAdus;
        for ( int i=0; i<lstAdus.size(); i++ )
        {
            QJsonObject jsonObj;
            getPointConnectionInfo(lstAdus.at(i), jsonObj);
            jsonAdus.append(jsonObj);
        }

        if ( !jsonAdus.isEmpty() )
        {
            json.insert(Str_TestPointAdu, jsonAdus);
        }
    }
}

void PDServerData::setTestPointInfo(QJsonArray &lstRes, const QJsonArray &lstPam, QString objectId)
{
    // 判断是否存在
    TestPointInfo tpNode;
    if ( !ConfigService::instance().getTestPoint(objectId, tpNode) )
    {
        for ( int i=0; i<lstPam.size(); i++ )
        {
            QJsonObject jsonRes;
            jsonRes.insert("paramName", lstPam.at(i).toObject().value("paramName"));
            jsonRes.insert("result", 0);
        }
        return;
    }

    QString strKey;
    int nFlag = 0;
    for ( int i=0; i<lstPam.size(); i++ )
    {
        QJsonObject jsonObj = lstPam.at(i).toObject();
        strKey = jsonObj.value("paramName").toString();
        QJsonValue jsValue = jsonObj.value("value");

        QJsonObject jsonRes;
        jsonRes.insert("paramName", strKey);
        jsonRes.insert("result", 1);

        nFlag++;
        if ( strKey == Str_TestPointId )
        {
            tpNode.strPointGUID = jsValue.toString();
        }
        else if ( strKey == Str_TestPointName )
        {
            tpNode.strName = jsValue.toString();
        }
        else
        {
            nFlag--;
            jsonRes[STR_RESULT] = 0;
        }

        lstRes.append(jsonRes);
    }

    if ( nFlag > 0 )
    {
        ConfigService::instance().updateTestPointByTpId(objectId, tpNode);
    }
}

//int PDServerData::addTestPointInfo(const QJsonObject &json)
//{
//    int nRes = 0;
//    QString strDevId = json.value(STR_DEVICE_ID).toString();
//    if ( !ConfigService::instance().isDeviceIDExisted(strDevId) )
//    {
//        return nRes;
//    }

//    QJsonArray jsArray = json.value(Str_TestPoint).toArray();
//    for ( int i=0; i<jsArray.size(); i++ )
//    {
//        TestPointInfo tpInfo;
//        // 解析字段
//        const QJsonObject &oneTestPoint = jsArray.at(i).toObject();
//        tpInfo.strGUID = oneTestPoint.value(Str_TestPointId).toString();
//        tpInfo.strName =

//        json.insert(Str_TestPointId, testPoint.strGUID);
//        json.insert(Str_TestPointName, testPoint.strName);

//        ConfigService::instance().addTestPoint(strDevId, tpInfo);
//    }

//}

int PDServerData::delTestPointInfo(const QString &strId)
{
    if ( CONFIG_NO_ERROR == ConfigService::instance().delTestPoint(strId) )
    {
        return 1;
    }

    return 0;
}

/************************************************
 * 输入参数: opType -- 操作类型；
 *          lstAdu -- 前端列表
 * 输出参数: NULL
 * 返回值: 结果状态
 * 功能: 操作传感器
 ************************************************/
bool PDServerData::operAduList(int opType, int aduType, const QList<QString> lstAdu)
{
    bool bRes = false;

    // todo: 使用枚举
    if ( opType == 1 )
    {
        if ( lstAdu.isEmpty() )
        {
            QList<ADUUnitInfo> lstAdu = ConfigService::instance().ADUList((ADUType)aduType);
            if ( !lstAdu.isEmpty() )
            {
                QStringList strListAdu;
                MonitorService::instance().sampleData(strListAdu, (ADUType)aduType);
                bRes = true;
            }

            PDS_SYS_INFO_LOG("sample all sensor, total: %d", lstAdu.size());
        }
        else
        {
            PDS_SYS_INFO_LOG("sample some sensor: %d", lstAdu.size());
            for ( int i=0; i<lstAdu.size(); ++i )
            {
                if ( !ConfigService::instance().isADUIDExisted(lstAdu.at(i)) )
                {
                    PDS_SYS_ERR_LOG("adu not exist: %s", lstAdu[i].toLatin1().data());
                    return bRes;
                }
            }

            bRes = true;
            for ( int i=0; i<lstAdu.size(); ++i )
            {
                QStringList strListAdu;
                strListAdu.append(lstAdu.at(i));
                MonitorService::instance().sampleData(strListAdu, (ADUType)aduType);
            }
        }
    }

    return bRes;
}

int PDServerData::addTestPointInfo(QString &testPointId, const QString &deviceId, const QJsonObject &json)
{
    int nRes = 0;

    TestPointInfo tpInfo;
    tpInfo.strPointGUID = json.value(Str_TestPointId).toString();
    tpInfo.strName = json.value(Str_TestPointName).toString();
    tpInfo.strOutName = tpInfo.strName;
    if ( tpInfo.strName.isEmpty() )
    {
        return nRes;
    }
    if ( tpInfo.strPointGUID.isEmpty() )
    {
        tpInfo.strPointGUID = ConfigService::instance().getGUID();
    }
    testPointId = tpInfo.strPointGUID;

    QList<PointConnectionInfo> lstConnection;
    QJsonArray jsConnectInfo = json.value(Str_TestPointAdu).toArray();
    for ( int i=0; i<jsConnectInfo.size(); i++ )
    {
        const QJsonObject &jsObj = jsConnectInfo.at(i).toObject();
        PointConnectionInfo pcInfo;
        pcInfo.strID = jsObj.value(STR_ADU_ID).toString();
        pcInfo.etype = static_cast<ADUChannelType>(jsObj.value(STR_CHANNEL_TYPE).toInt());
        pcInfo.unID = static_cast<quint8>(jsObj.value(STR_CHANNEL_INDEX).toInt());
        lstConnection.append(pcInfo);
    }

    if ( CONFIG_NO_ERROR == ConfigService::instance().addTestPoint(deviceId, tpInfo) )
    {
        //保存关联信息
        if ( CONFIG_NO_ERROR == ConfigService::instance().saveRelationInfo(tpInfo.strPointGUID, tpInfo.strOutName, lstConnection) )
        {
            nRes = 1;
        }
    }

    return nRes;
}

/************************************************
 * 输入参数: adu -- 关联通道信息
 * 输出参数: jsonadu -- json树
 * 返回值: NULL
 * 功能: 将关联通道数据转换为json
 ************************************************/
void PDServerData::getPointConnectionInfo(const PointConnectionInfo &adu, QJsonObject &jsonadu)
{
    jsonadu.insert(STR_ADU_ID, adu.strID);
    jsonadu.insert(STR_CHANNEL_TYPE, adu.etype);
    jsonadu.insert(STR_CHANNEL_INDEX, adu.unID);
}

//void PDServerData::setPointConnectionInfo(QJsonArray &lstRes, const QJsonArray &lstPam, QString objectId)
//{
//    PointConnectionInfo pointAdu;
//    ConfigService::instance().getPointConnectedADUID();
//}

/************************************************
 * 函数名: getChannelInfo(QJsonObject &json, const ADUChannelInfo &channel)
 * 输入参数: channel -- 通道信息
 *          aduID -- 前端ID
 * 输出参数: json -- json树
 * 返回值: NULL
 * 功能: 将通道数据转换为json
 ************************************************/
void PDServerData::getChannelInfo(QJsonObject &json, const ADUChannelInfo &channel, const QString &aduId)
{
    json.insert(STR_CHANNEL_ID, QString("%1/%2").arg(aduId).arg(channel.unID));
    json.insert(STR_CHANNEL_INDEX, channel.unID);
    json.insert(STR_CHANNEL_NAME, channel.strName);
    json.insert(STR_CHANNEL_TYPE, channel.etype);
    //通道参数
    QJsonObject jsonParam;
    getChannelPara(channel.etype, channel.stachpara, jsonParam);
    json.insert(STR_CHANNEL_PARAM, jsonParam);
}

void PDServerData::setChannelInfo(QJsonArray &lstRes, const QJsonArray &lstPam, QString objectId)
{
    QStringList lstPart = objectId.split('/');
    if ( lstPart.size() != 2 )
    {
        PDS_SYS_ERR_LOG("setChannelInfo error: %s", objectId.toLatin1().data());;
        setInfoErrorResp(lstRes, lstPam);
        return;
    }

    ADUChannelInfo channel;
    if ( !ConfigService::instance().getChannelInfo(lstPart.first(), static_cast<quint8>(lstPart.last().toInt()), channel) )
    {
        PDS_SYS_ERR_LOG("fail to getChannelInfo: %s", lstPart.first().toLatin1().data());
        setInfoErrorResp(lstRes, lstPam);
        return;
    }

    QString strKey;
    int nFlag = 0;
    for ( int i=0; i<lstPam.size(); i++ )
    {
        QJsonObject jsonObj = lstPam.at(i).toObject();
        strKey = jsonObj.value(Str_ParamName).toString();
        QJsonValue jsValue = jsonObj.value(STR_VALUE);

        PDS_SYS_INFO_LOG("setChannelInfo %s %s", strKey.toLatin1().data(), jsValue.toString().toLatin1().data());

        QJsonObject jsonRes;
        jsonRes.insert(Str_ParamName, strKey);
        jsonRes.insert(STR_RESULT, 1);

        nFlag++;
        if ( strKey == STR_CHANNEL_INDEX )
        {
            channel.unID = static_cast<quint8>(jsValue.toInt());
        }
        else if ( strKey == STR_CHANNEL_NAME )
        {
            channel.strName = jsValue.toString();
        }
        else if ( strKey == STR_CHANNEL_TYPE )
        {
            channel.etype = static_cast<ADUChannelType>(jsValue.toInt());
        }
        else
        {
            if ( !parseChannelParamFromJson(channel.etype, channel.stachpara, strKey, jsValue) )
            {
                nFlag--;
                jsonRes[STR_RESULT] = 0;
            }
        }

        lstRes.append(jsonRes);
    }

    if ( nFlag > 0 )
    {
        PDS_SYS_INFO_LOG("setChannelInfo %d %s", nFlag, lstPart.first().toLatin1().data());
        ConfigService::instance().saveChannelInfo(lstPart.first(), channel);
    }
}

void PDServerData::getAduInfo(QJsonObject &json, const ADUUnitInfo &adu, bool bSub)
{
    json.insert(STR_ADU_ID, adu.strID);
    json.insert(STR_ADU_NAME, adu.strName);
    json.insert(STR_ADU_TYPE, adu.eType);
    json.insert(STR_ADU_MODEL, "");         // 无型号信息
    json.insert(Str_AduWorkMode, adu.stADUParam.ucAutoUpdate);
    json.insert(Str_AduMode, adu.stADUParam.eADUWorkModel);
    json.insert(Str_AduLinkType, adu.stADUParam.eLinkGroup);
    json.insert(Str_AduVer, adu.strVersion);
    json.insert(Str_AduFreq, adu.stADUParam.ucFrequency);
    //json.insert(Str_HardwearVer, "");
    //json.insert("SN", "");
    json.insert(STR_ADU_GROUP_ID, adu.stADUParam.ucWorkGroup);
    json.insert(STR_ADU_GROUP_NUM, adu.stADUParam.usNumInGroup);
    json.insert(STR_ADU_COMMUNICATION_CHANNEL, adu.stADUParam.ucConnectionLoad);
    json.insert(STR_ADU_COMMUNICATION_RATE, adu.stADUParam.ucConnectionSpeed);
    json.insert(STR_ADU_SLEEP_INTERVAL, static_cast<int>(adu.stADUParam.uiSleepTime));
    json.insert(STR_ADU_SAMPEL_INTERVAL, static_cast<int>(adu.stADUParam.uiSampleSpace));
    json.insert(STR_ADU_SAMPEL_START_TIMR, adu.stADUParam.usStartSampleTime);

    if ( bSub )
    {
        QJsonArray jsonArray;
        for ( int i=0; i<adu.Channels.size(); i++ )
        {
            QJsonObject jsonObj;
            getChannelInfo(jsonObj, adu.Channels.at(i), adu.strID);
            jsonArray.append(jsonObj);
        }
        json.insert(Str_Channel, jsonArray);
    }
}

void PDServerData::setAduInfo(QJsonArray &lstRes, const QJsonArray &lstPam, QString objectId)
{
    ADUUnitInfo aduInfo;
    if ( !ConfigService::instance().findADU(objectId, aduInfo) )
    {
        setInfoErrorResp(lstRes, lstPam);
        return;
    }

    QString strKey;
    int nFlag = 0;
    for ( int i=0; i<lstPam.size(); i++ )
    {
        QJsonObject jsonObj = lstPam.at(i).toObject();
        strKey = jsonObj.value(Str_ParamName).toString();
        QJsonValue jsValue = jsonObj.value(STR_VALUE);

        QJsonObject jsonRes;
        jsonRes.insert(Str_ParamName, strKey);
        jsonRes.insert(STR_RESULT, 1);

        nFlag++;
        if ( strKey == STR_ADU_ID )
        {
            aduInfo.strID = jsValue.toString();
        }
        else if ( strKey == STR_ADU_NAME )
        {
            aduInfo.strName = jsValue.toString();
        }
        else if ( strKey == STR_ADU_TYPE )
        {
            aduInfo.eType = (ADUType)jsValue.toInt();
        }
        else if ( strKey == Str_AduWorkMode )
        {
            aduInfo.stADUParam.ucAutoUpdate = (quint8)jsValue.toInt();
        }
        else if ( strKey == Str_AduMode )
        {
            aduInfo.stADUParam.eADUWorkModel = (Monitor::ADUWorkMode)jsValue.toInt();
        }
        else if ( strKey == Str_AduLinkType )
        {
            aduInfo.stADUParam.eLinkGroup = (Monitor::LinkGroup)jsValue.toInt();
        }
        else if ( strKey == Str_AduFreq )
        {
            aduInfo.stADUParam.ucFrequency = (quint8)jsValue.toInt();
        }
        else if ( strKey == STR_ADU_GROUP_ID )
        {
            aduInfo.stADUParam.ucWorkGroup = (quint8)jsValue.toInt();
        }
        else if ( strKey == STR_ADU_GROUP_NUM )
        {
            aduInfo.stADUParam.usNumInGroup = (quint16)jsValue.toInt();
        }
        else if ( strKey == STR_ADU_SLEEP_INTERVAL )
        {
            aduInfo.stADUParam.uiSleepTime = (quint32)jsValue.toInt();
        }
        else if ( strKey == STR_ADU_SAMPEL_INTERVAL )
        {
            aduInfo.stADUParam.uiSampleSpace = (quint32)jsValue.toInt();
        }
        else if ( strKey == STR_ADU_SAMPEL_START_TIMR )
        {
            aduInfo.stADUParam.usStartSampleTime = (quint16)jsValue.toInt();
        }
        else
        {
            nFlag--;
            jsonRes["result"] = 0;
        }

        lstRes.append(jsonRes);
    }

    if ( nFlag > 0 )
    {
        ConfigService::instance().saveADUInfo(objectId, aduInfo);
    }
}

int PDServerData::addAdu(const QJsonObject &json)
{
    // 必备信息检查
    if ( !json.contains(STR_ADU_ID) ||
         !json.contains(STR_ADU_NAME) ||
         !json.contains(STR_ADU_TYPE) ||
         !json.contains(Str_AduLinkType) )
    {
        return  0;
    }

    QString strValue = json.value(STR_ADU_ID).toString();
    if ( ConfigService::instance().isADUIDExisted(strValue) )
    {
        PDS_SYS_ERR_LOG("addAdu already exist: %s", strValue.toLatin1().data());
        return 0;
    }

    ADUUnitInfo aduInfo;
    aduInfo.strID = strValue;
    aduInfo.strName = json.value(STR_ADU_NAME).toString();
    aduInfo.eType = static_cast<ADUType>(json.value(STR_ADU_TYPE).toInt());
    aduInfo.eLinkGroup = static_cast<Monitor::LinkGroup>(json.value(Str_AduLinkType).toInt());
    aduInfo.stADUParam.eLinkGroup = aduInfo.eLinkGroup;
    if ( json.contains(Str_AduWorkMode) )
    {
        aduInfo.stADUParam.ucAutoUpdate = static_cast<quint8>(json.value(Str_AduWorkMode).toInt());
    }
    else
    {
        aduInfo.stADUParam.ucAutoUpdate = 0;
    }
    if ( json.contains(STR_ADU_GROUP_ID) )
    {
        aduInfo.stADUParam.ucWorkGroup = static_cast<quint8>(json.value(STR_ADU_GROUP_ID).toInt());
    }
    else
    {
        aduInfo.stADUParam.ucWorkGroup = 0;
    }
    if ( json.contains(Str_AduFreq) )
    {
        aduInfo.stADUParam.ucFrequency = static_cast<quint8>(json.value(Str_AduFreq).toInt());
    }
    else
    {
        aduInfo.stADUParam.ucFrequency = 50;
    }
    if ( json.contains(STR_SAMPLE_INTERVAL) )
    {
        aduInfo.stADUParam.uiSampleSpace = static_cast<quint32>(json.value(STR_SAMPLE_INTERVAL).toInt());
    }
    else
    {
        aduInfo.stADUParam.uiSampleSpace = 1440;
    }
    if ( json.contains(STR_ADU_SAMPEL_START_TIMR) )
    {
        aduInfo.stADUParam.usStartSampleTime = static_cast<quint16>(json.value(STR_ADU_SAMPEL_START_TIMR).toInt());
    }
    else
    {
        aduInfo.stADUParam.usStartSampleTime = 0;
    }
    if ( json.contains(Str_AduMode) )
    {
        aduInfo.stADUParam.eADUWorkModel = static_cast<Monitor::ADUWorkMode>(json.value(Str_AduMode).toInt());
    }
    else
    {
        aduInfo.stADUParam.eADUWorkModel = Monitor::WORKER_MODEL_MAINTAIN;
    }

    if ( Monitor::WORKER_MODEL_LOWPOWER == aduInfo.stADUParam.eADUWorkModel )
    {
        aduInfo.stADUParam.ucStartArtificialTime = 8;
        aduInfo.stADUParam.ucEndArtificialTime = 20;
        aduInfo.stADUParam.usArtificialWakeUpInterval = 3;
        aduInfo.stADUParam.usNotArtificialWalkUpInterval = 30;
        aduInfo.stADUParam.bAutoChangeMode = 1;
    }

    ConfigService::instance().addADUInfo(aduInfo);

    return 1;
}

int PDServerData::delAdu(const QString &strAduId)
{
    PDS_SYS_INFO_LOG("delAdu: %s", strAduId.toLatin1().data());

    if ( CONFIG_NO_ERROR == ConfigService::instance().deleteADU(strAduId) )
    {
        return 1;
    }

    PDS_SYS_ERR_LOG("fail to delAdu: %s", strAduId.toLatin1().data());
    return 0;
}

void PDServerData::getMonitorInfo(QJsonObject &json, int nLevel)
{
    const MonitorInfo &monitorInfo = getMonitorInfo();
    const SystemSetting &sysSetting = ConfigService::instance().systemSettings();
    const WebSetting &webSetting = ConfigService::instance().webSetting();
    const ModbusSetting &modbusSetting = ConfigService::instance().modbusSetting();

    json.insert(Str_MonitorId, monitorInfo.strMonitorID);
    json.insert(Str_MonitorType, 0);    // S1010
    json.insert(Str_MonitorName, sysSetting.strMonitorName);
    json.insert(Str_MonitorGroup, sysSetting.ucMonitorConnectionGroup);
    json.insert(STR_MONITOR_SLEEP_SPACE, static_cast<int>(sysSetting.uiMonitorSleepSpace));
    json.insert(STR_MONITOR_AWAKE_TIME, static_cast<int>(sysSetting.uiMonitorAwakeTime));
    //todo: 无采样宽度
    json.insert(Str_MonitorPdSampleInterval, (int)sysSetting.uiPdSampleInterval);
    json.insert(Str_MonitorMechSampleInterval, (int)sysSetting.uiMechSyncDataInterval);
    //todo: 无主机采样启动时间
    json.insert(Str_MonitorNetType, webSetting.eWebType);
    json.insert(Str_SN, monitorInfo.strMonitorID);
    //todo: 无出厂日期、硬件版本
    json.insert("workMode", MonitorService::instance().getMonitorWorkMode());

    json.insert("softwearVer", monitorInfo.strSoftwearVer);
    json.insert("webViewVer", monitorInfo.strWebViewVerr);
    json.insert("diagramVer", monitorInfo.iDiagramVer);
    json.insert("isIncludeSubDev", monitorInfo.bIncludeSubDev);
    json.insert(STR_FREQUNCY, monitorInfo.iFrequency);
    //todo: 采集间隔??
    json.insert(STR_SYS_UPLOAD_INTERVAL, monitorInfo.iUploadInterval);
    json.insert(Str_GprsSignal, ConfigService::instance().getGPRSSignal());
    //todo: 主机电量暂时无法获取
    json.insert(Str_VpnIp, ConfigService::instance().getGPRSIP());
    json.insert(Str_VpnKeyName, getVpnKeyName());
    json.insert(Str_RemoteIp, webSetting.strServerAddress);
    json.insert(Str_RemotePort, webSetting.iServerPort);
    json.insert("APNType", webSetting.eWebType);
    json.insert("APNUser", webSetting.strNetworkUserName);
    json.insert("APNPwd", webSetting.strNetworkPassword);
    json.insert("localIP", webSetting.strIP);
    json.insert("localGateway", webSetting.strGateWay);
    json.insert("localMask", webSetting.strSubnetMask);
    int nValue = (int)webSetting.bStrategy4G;
    json.insert(Str_NetAutoReset, nValue);
    //json.insert(STR_ADU_PORT_MODBUS, server.stModbusSetting);
    json.insert(Str_MdbusAddr, modbusSetting.ucModbusAddress);
    json.insert(Str_MdbusDataBit, modbusSetting.iDatabit);
    json.insert(Str_MdbusParity, modbusSetting.ucParity);
    json.insert(Str_MdbusRTS, modbusSetting.iRts);
    json.insert(Str_MdbusStopBit, modbusSetting.iStopbit);
    json.insert(Str_MdbusBaud, modbusSetting.iBaud);

    if ( nLevel != 1 )
    {
        bool bSub = (nLevel==0);

        QJsonArray jsonArray;
        QList<ADUUnitInfo> lstAdu = ConfigService::instance().ADUList();
        for ( int i=0; i<lstAdu.size(); i++ )
        {
            QJsonObject jsonObj;
            getAduInfo(jsonObj, lstAdu.at(i), bSub);
            jsonArray.append(jsonObj);
        }

        json.insert("adu", jsonArray);
    }
}

void PDServerData::setMonitorInfo(QJsonArray &lstRes, const QJsonArray &lstPam, QString objectId)
{
    SystemSetting sysSet = ConfigService::instance().systemSettings();
    WebSetting webSet = ConfigService::instance().webSetting();
    ModbusSetting mdbSet = ConfigService::instance().modbusSetting();

    if ( sysSet.strMonitorID != objectId )
    {
        setInfoErrorResp(lstRes, lstPam);
        return;
    }

    bool bSysSet=false, bNetSet=false, bMdbSet=false;

    QString strKey;
    //int nFlag = 0;
    for ( int i=0; i<lstPam.size(); i++ )
    {
        QJsonObject jsonObj = lstPam.at(i).toObject();
        strKey = jsonObj.value("paramName").toString();
        QJsonValue jsValue = jsonObj.value("value");

        QJsonObject jsonRes;
        jsonRes.insert("paramName", strKey);
        jsonRes.insert("result", 1);

        //----SystemSetting----
        if ( strKey == Str_MonitorId )
        {
            bSysSet = true;
            sysSet.strMonitorID = jsValue.toString();
        }
        else if ( strKey == Str_MonitorName )
        {
            bSysSet = true;
            sysSet.strMonitorName = jsValue.toString();
        }
        else if ( strKey == Str_MonitorGroup )
        {
            bSysSet = true;
            sysSet.ucMonitorConnectionGroup = (quint8)jsValue.toInt();
        }
        else if ( strKey == STR_MONITOR_SLEEP_SPACE )
        {
            bSysSet = true;
            sysSet.uiMonitorSleepSpace = (quint32)jsValue.toInt();
        }
        else if ( strKey == STR_MONITOR_AWAKE_TIME )
        {
            bSysSet = true;
            sysSet.uiMonitorAwakeTime = (quint32)jsValue.toInt();
        }
        else if ( strKey == Str_MonitorPdSampleInterval )
        {
            bSysSet = true;
            sysSet.uiPdSampleInterval = (quint32)jsValue.toInt();
        }
        else if ( strKey == Str_MonitorMechSampleInterval )
        {
            bSysSet = true;
            sysSet.uiMechSyncDataInterval = (quint32)jsValue.toInt();
        }
        else if ( strKey == STR_FREQUNCY )
        {
            bSysSet = true;
            sysSet.iFreq = jsValue.toInt();
        }
        else if ( strKey == STR_SYS_UPLOAD_INTERVAL )
        {
            bSysSet = true;
            sysSet.iUploadInterval = jsValue.toInt();
        }
        else if ( strKey == STR_MONITOR_PING_SPACE )
        {
            bSysSet = true;
            int nValue = jsValue.toInt();
            if ( nValue <= 0 )
            {
                nValue = 5;
            }
            sysSet.uiPingInterval = (quint16)nValue;
        }
        //----WebSetting----
        else if ( strKey == Str_MonitorNetType )
        {
            bNetSet = true;
            webSet.eWebType = (WebType)jsValue.toInt();
        }
        else if ( strKey == Str_RemoteIp )
        {
            bNetSet = true;
            webSet.strServerAddress = jsValue.toString();
        }
        else if ( strKey == Str_RemotePort )
        {
            bNetSet = true;
            webSet.iServerPort = jsValue.toInt();
        }
        else if ( strKey == Str_NetAutoReset )
        {
            bNetSet = true;
            webSet.bStrategy4G = (jsValue.toInt()==1);
        }
        //----ModbusSetting----
        else if ( strKey == Str_MdbusAddr )
        {
            bMdbSet = true;
            mdbSet.ucModbusAddress = (quint8)jsValue.toInt();
        }
        else if ( strKey == Str_MdbusDataBit )
        {
            bMdbSet = true;
            mdbSet.iDatabit = jsValue.toInt();
        }
        else if ( strKey == Str_MdbusParity )
        {
            bMdbSet = true;
            mdbSet.ucParity = (char)jsValue.toInt();
        }
        else if ( strKey == Str_MdbusRTS )
        {
            bMdbSet = true;
            mdbSet.iRts = jsValue.toInt();
        }
        else if ( strKey == Str_MdbusStopBit )
        {
            bMdbSet = true;
            mdbSet.iStopbit = jsValue.toInt();
        }
        else if ( strKey == Str_MdbusBaud )
        {
            bMdbSet = true;
            mdbSet.iBaud = jsValue.toInt();
        }
        //----
        else
        {
            jsonRes["result"] = 0;
        }

        lstRes.append(jsonRes);
    }

    if ( bSysSet )
    {
        ConfigService::instance().savesystemSetting(sysSet);
    }
    if ( bNetSet )
    {
        ConfigService::instance().saveWebSetting(webSet);
    }
    if ( bMdbSet )
    {
        ConfigService::instance().saveModbusSetting(mdbSet);
    }
}

void PDServerData::setInfoErrorResp(QJsonArray &lstRes, const QJsonArray &lstPam)
{
    for ( int i=0; i<lstPam.size(); i++ )
    {
        QJsonObject jsonRes;
        jsonRes.insert(Str_ParamName, lstPam.at(i).toObject().value(Str_ParamName));
        jsonRes.insert(Str_Result, 0);
        lstRes.append(jsonRes);
    }
}

/************************************************
 * 函数名:  getChannelAlarmData
 * 输入参数:  strChannelName -- 通道名
        startTime -- 开始时间
        endTime -- 结束时间
        iPage -- 页码
        iCountPerPage -- 每页数量
 * 输出参数:  data -- 数据
 * 返回值:  NULL
 * 功能: 获取某时间段内的通道报警信息
 ************************************************/
void PDServerData::getChannelAlarmData(const QString &strChannelCode, const QDateTime &startTime, const QDateTime &endTime, int iPage, int iCountPerPage, QJsonArray& data)
{
    Q_UNUSED(strChannelCode);
    Q_UNUSED(startTime);
    Q_UNUSED(endTime);
    Q_UNUSED(iPage);
    Q_UNUSED(iCountPerPage);
    Q_UNUSED(data);

}

/************************************************
 * 函数名:  getChannelPara
 * 输入参数:  eChannelType -- 通道类型枚举
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取通道参数
 ************************************************/
void PDServerData::getChannelPara(ADUChannelType eChannelType, const Channelpara & stachpara, QJsonObject &jsonData)
{
    switch (eChannelType)
    {
    case CHANNEL_AE://AE参数
    {
        jsonData.insert(STR_PARA_GAIN,stachpara.staAEPara.cGain);//增益
        jsonData.insert(STR_PARA_GAIN_MODEL,stachpara.staAEPara.eChannelGainType);//增益模式
        jsonData.insert(STR_PARA_SAMPLE_PERIOD,stachpara.staAEPara.ucSampleCycles);//采样周期数
        jsonData.insert(STR_PARA_SAMPLE_POINTS,stachpara.staAEPara.usSampelCount);//周期采样数
    }
        break;
    case CHANNEL_TEV://TEV参数
    {
        jsonData.insert(STR_PARA_BACK_GROUND_DATA, stachpara.staTEVPara.cBackGroundNum);
    }
        break;
    case CHANNEL_UHF:
    {
        jsonData.insert(STR_PARA_GAIN,stachpara.staUHFPara.cGain);//增益
        jsonData.insert(STR_PARA_BAND_WIDTH,stachpara.staUHFPara.cBandWidth);//带宽
        jsonData.insert(STR_PARA_GAIN_MODEL,stachpara.staUHFPara.eChannelGainType);//增益模式
        jsonData.insert(STR_PARA_SAMPLE_PERIOD,stachpara.staUHFPara.ucSampleCycles);//采样周期数
        jsonData.insert(STR_PARA_SAMPLE_POINTS,stachpara.staUHFPara.usSampelCount);//周期采样数
    }
        break;
    case CHANNEL_HFCT:
    {
        jsonData.insert(STR_PARA_GAIN,stachpara.staHFCTPara.cGain);//增益
        jsonData.insert(STR_PARA_GAIN_MODEL,stachpara.staHFCTPara.eChannelGainType);//增益模式
        jsonData.insert(STR_PARA_SAMPLE_PERIOD,stachpara.staHFCTPara.ucSampleCycles);//采样周期数
        jsonData.insert(STR_PARA_SAMPLE_POINTS,stachpara.staHFCTPara.usSampelCount);//周期采样数
    }
        break;
    case CHANNEL_MECH:
    {
        //        jsonData.insert(STR_PARA_CLOSE_TIME,stachpara.staMechPara.fCloseTime);//合闸时间
        //        jsonData.insert(STR_PARA_OPEN_TIME,stachpara.staMechPara.fOpenTime);//分闸时间
        //        jsonData.insert(STR_PARA_CLOSE_SYNC,stachpara.staMechPara.fCloseSync);//合闸同期性
        //        jsonData.insert(STR_PARA_OPEN_SYNC,stachpara.staMechPara.fOpenSync);//分闸同期性
        //        jsonData.insert(STR_PARA_CIRCUIT_CURRETN,stachpara.staMechPara.fCircuitCurrent);//线圈额定电流
        //        jsonData.insert(STR_PARA_MOTOR_CURRETN,stachpara.staMechPara.fMotorCurrent);//电机额定电流
        //        jsonData.insert(STR_PARA_ENGERGY_STORE_TIME,stachpara.staMechPara.fEnergyStoreTime);//电机储能时间

        // 2019.4.9 修改
        jsonData.insert(STR_PARA_LOOP_CURRENT_THRED, stachpara.staMechPara.usLoopCurrentThred);
        jsonData.insert(STR_PARA_MOTOR_CURRENT_THRED, stachpara.staMechPara.usMotorCurrentThred);
        int nValue = stachpara.staMechPara.bSwitchState ? 1 : 0;
        jsonData.insert(STR_PARA_SWITCH_STATE, nValue);
        nValue = stachpara.staMechPara.bBreakerType ? 1 : 0;
        jsonData.insert(STR_PARA_BREAK_TYPE, nValue);
        nValue = stachpara.staMechPara.bMotorFunctionType ? 1 : 0;
        jsonData.insert(STR_PARA_MOTOR_FUNCTION_TYPE, nValue);
    }
        break;
    case CHANNEL_SAW:
    {
        jsonData.insert(STR_PARA_ALARM,stachpara.staSAWPara.cAlarm);//报警阈值
        jsonData.insert(STR_PARA_TEMPERATURE_DIFFERENCE,stachpara.staSAWPara.cTemperatureDifference);//温升差值
        jsonData.insert(STR_PARA_THREE_TRIP_DIFFERENCE,stachpara.staSAWPara.cThreeTripDifference);//三项差值
    }
        break;
    case CHANNEL_AX8:
    {
        jsonData.insert(STR_PARA_TEMPERATURE,stachpara.staAX8Para.cTemperature);//温度
        jsonData.insert(STR_PARA_DISTANCE_UNIT,stachpara.staAX8Para.cDistanceUnit);//距离单位
        jsonData.insert(STR_PARA_FTP,stachpara.staAX8Para.cFTP);//文件传输协议
        jsonData.insert(STR_PARA_FOLDER,stachpara.staAX8Para.cFolder);//文件夹
    }
        break;

    default:
        break;
    }

}

/************************************************
 * 输入参数:  eChannelType -- 通道类型枚举；
 *           json -- json数据
 * 输出参数:  chpara -- 通道参数
 * 返回值:  NULL
 * 功能:  解析通道参数
 ************************************************/
void PDServerData::parseChannelParaFromJson(ADUChannelType eChannelType, Channelpara &chpara, const QJsonObject &json)
{
    PDS_SYS_INFO_LOG("parseChannelParaFromJson: %d", eChannelType);
    switch (eChannelType)
    {
    case CHANNEL_AE:
        if ( json.contains(STR_PARA_GAIN) )
        {
            chpara.staAEPara.cGain = (qint8)json.value(STR_PARA_GAIN).toInt();
        }
        if ( json.contains(STR_PARA_GAIN_MODEL) )
        {
            chpara.staAEPara.eChannelGainType = (ChannelGainType)json.value(STR_PARA_GAIN_MODEL).toInt();
        }
        if ( json.contains(STR_PARA_SAMPLE_PERIOD) )
        {
            chpara.staAEPara.ucSampleCycles = (quint8)json.value(STR_PARA_SAMPLE_PERIOD).toInt();
        }
        if ( json.contains(STR_PARA_SAMPLE_POINTS) )
        {
            chpara.staAEPara.usSampelCount = (quint16)json.value(STR_PARA_SAMPLE_POINTS).toInt();
        }
        break;
    case CHANNEL_TEV:
        if ( json.contains(STR_PARA_BACK_GROUND_DATA) )
        {
            chpara.staTEVPara.cBackGroundNum = (qint8)json.value(STR_PARA_BACK_GROUND_DATA).toInt();
        }
        break;
    case CHANNEL_UHF:
        if ( json.contains(STR_PARA_GAIN) )
        {
            chpara.staUHFPara.cGain = (quint8)json.value(STR_PARA_GAIN).toInt();
        }
        if ( json.contains(STR_PARA_BAND_WIDTH) )
        {
            chpara.staUHFPara.cBandWidth = (UHFDataFilter)json.value(STR_PARA_BAND_WIDTH).toInt();
        }
        if ( json.contains(STR_PARA_GAIN_MODEL) )
        {
            chpara.staUHFPara.eChannelGainType = (ChannelGainType)json.value(STR_PARA_GAIN_MODEL).toInt();
        }
        if ( json.contains(STR_PARA_SAMPLE_PERIOD) )
        {
            chpara.staUHFPara.ucSampleCycles = (quint8)json.value(STR_PARA_SAMPLE_PERIOD).toInt();
        }
        if ( json.contains(STR_PARA_SAMPLE_POINTS) )
        {
            chpara.staUHFPara.usSampelCount = (quint16)json.value(STR_PARA_SAMPLE_POINTS).toInt();
        }
        break;
    case CHANNEL_HFCT:
        if ( json.contains(STR_PARA_GAIN) )
        {
            chpara.staHFCTPara.cGain = (qint8)json.value(STR_PARA_GAIN).toInt();
        }
        if ( json.contains(STR_PARA_GAIN_MODEL) )
        {
            chpara.staHFCTPara.eChannelGainType = (ChannelGainType)json.value(STR_PARA_GAIN_MODEL).toInt();
        }
        if ( json.contains(STR_PARA_SAMPLE_PERIOD) )
        {
            chpara.staHFCTPara.ucSampleCycles = (quint8)json.value(STR_PARA_SAMPLE_PERIOD).toInt();
        }
        if ( json.contains(STR_PARA_SAMPLE_POINTS) )
        {
            chpara.staHFCTPara.usSampelCount = (quint16)json.value(STR_PARA_SAMPLE_POINTS).toInt();
        }
        break;
    case CHANNEL_MECH:
        if ( json.contains(STR_PARA_LOOP_CURRENT_THRED) )
        {
            chpara.staMechPara.usLoopCurrentThred = (quint16)json.value(STR_PARA_LOOP_CURRENT_THRED).toInt();
        }
        if ( json.contains(STR_PARA_MOTOR_CURRENT_THRED) )
        {
            chpara.staMechPara.usMotorCurrentThred = (quint16)json.value(STR_PARA_MOTOR_CURRENT_THRED).toInt();
        }
        if ( json.contains(STR_PARA_SWITCH_STATE) )
        {
            chpara.staMechPara.bSwitchState = (json.value(STR_PARA_SWITCH_STATE).toInt()==1);
        }
        if ( json.contains(STR_PARA_BREAK_TYPE) )
        {
            chpara.staMechPara.bBreakerType = (json.value(STR_PARA_BREAK_TYPE).toInt()==1);
        }
        if ( json.contains(STR_PARA_MOTOR_FUNCTION_TYPE) )
        {
            chpara.staMechPara.bMotorFunctionType = (json.value(STR_PARA_MOTOR_FUNCTION_TYPE).toInt()==1);
        }
        break;
    default:
        break;
    }
}

bool PDServerData::parseChannelParamFromJson(ADUChannelType eChannelType, Channelpara &chpara, const QString &strKey, const QJsonValue &jsValue)
{
    PDS_SYS_INFO_LOG("parseChannelParaFromJson %d %s %s", eChannelType, strKey.toLatin1().data(), jsValue.toString().toLatin1().data());

    bool bRes = true;
    switch (eChannelType)
    {
    case CHANNEL_AE:
        if ( strKey == STR_PARA_GAIN )
        {
            chpara.staAEPara.cGain = (qint8)jsValue.toInt();
        }
        else if ( strKey == STR_PARA_GAIN_MODEL )
        {
            chpara.staAEPara.eChannelGainType = (ChannelGainType)jsValue.toInt();
        }
        else if ( strKey == STR_PARA_SAMPLE_PERIOD )
        {
            chpara.staAEPara.ucSampleCycles = (quint8)jsValue.toInt();
        }
        else if ( strKey == STR_PARA_SAMPLE_POINTS )
        {
            chpara.staAEPara.usSampelCount = (quint16)jsValue.toInt();
        }
        else
        {
            bRes = false;
        }
        break;
    case CHANNEL_TEV:
        if ( strKey == STR_PARA_BACK_GROUND_DATA )
        {
            chpara.staTEVPara.cBackGroundNum = (qint8)jsValue.toInt();
        }
        else
        {
            bRes = false;
        }
        break;
    case CHANNEL_UHF:
        if ( strKey == STR_PARA_GAIN )
        {
            chpara.staUHFPara.cGain = (quint8)jsValue.toInt();
        }
        else if ( strKey == STR_PARA_BAND_WIDTH )
        {
            chpara.staUHFPara.cBandWidth = (UHFDataFilter)jsValue.toInt();
        }
        else if ( strKey == STR_PARA_GAIN_MODEL )
        {
            chpara.staUHFPara.eChannelGainType = (ChannelGainType)jsValue.toInt();
        }
        else if ( strKey == STR_PARA_SAMPLE_PERIOD )
        {
            chpara.staUHFPara.ucSampleCycles = (quint8)jsValue.toInt();
        }
        else if ( strKey == STR_PARA_SAMPLE_POINTS )
        {
            chpara.staUHFPara.usSampelCount = (quint16)jsValue.toInt();
        }
        else
        {
            bRes = false;
        }
        break;
    case CHANNEL_HFCT:
        if ( strKey == STR_PARA_GAIN )
        {
            chpara.staHFCTPara.cGain = (qint8)jsValue.toInt();
        }
        else if ( strKey == STR_PARA_GAIN_MODEL )
        {
            chpara.staHFCTPara.eChannelGainType = (ChannelGainType)jsValue.toInt();
        }
        else if ( strKey == STR_PARA_SAMPLE_PERIOD )
        {
            chpara.staHFCTPara.ucSampleCycles = (quint8)jsValue.toInt();
        }
        else if ( strKey == STR_PARA_SAMPLE_POINTS )
        {
            chpara.staHFCTPara.usSampelCount = (quint16)jsValue.toInt();
        }
        else
        {
            bRes = false;
        }
        break;
    case CHANNEL_MECH:
        if ( strKey == STR_PARA_LOOP_CURRENT_THRED )
        {
            chpara.staMechPara.usLoopCurrentThred = (quint16)jsValue.toInt();
        }
        else if ( strKey == STR_PARA_MOTOR_CURRENT_THRED )
        {
            chpara.staMechPara.usMotorCurrentThred = (quint16)jsValue.toInt();
        }
        else if ( strKey == STR_PARA_SWITCH_STATE )
        {
            chpara.staMechPara.bSwitchState = (jsValue.toInt()==1);
        }
        else if ( strKey == STR_PARA_BREAK_TYPE )
        {
            chpara.staMechPara.bBreakerType = (jsValue.toInt()==1);
        }
        else if ( strKey == STR_PARA_MOTOR_FUNCTION_TYPE )
        {
            chpara.staMechPara.bMotorFunctionType = (jsValue.toInt()==1);
        }
        else
        {
            bRes = false;
        }
        break;
    default:
        bRes = false;
        break;
    }

    return bRes;
}

/************************************************
 * 函数名:  setStationInfo
 * 输入参数:  iUploadInterval -- 上传间隔
 *          iFreq -- 电网频率
 *          iSyncType -- 同步类型
 *          strSyncFlag -- 同步标识
 * 输出参数:  NULL
 * 返回值:  操作结果
 * 功能: 设置检测设备信息（设置）
 ************************************************/
bool PDServerData::setMonitorSetting(int iUploadInterval)
{
    ConfigService &configService = ConfigService::instance();
    SystemSetting staSystemSetting = configService.systemSettings();
    staSystemSetting.iUploadInterval = iUploadInterval;
    configService.savesystemSetting(staSystemSetting);
    return true;
}

/************************************************
 * 函数名:  setChannelInfo
 * 输入参数: staChannelPara -- 传感器信息
 * 输出参数:  NULL
 * 返回值:  操作结果
 * 功能: 设置通道信息
 ************************************************/
void PDServerData::setChannelInfo(QJsonObject &jsonChannelPara )
{
    ConfigService &configService = ConfigService::instance();
    Channelpara staChannelPara;
    ConfigData data;
    bool result = data.getChannelParaFromJson(jsonChannelPara,staChannelPara);
    if (result)
    {
        result = configService.saveChannelPara(staChannelPara, jsonChannelPara.value(STR_ADU_ID).toString(),jsonChannelPara.value(STR_CHANNEL_INDEX).toInt());
    }
    jsonChannelPara.insert(STR_RESULT,result);
}

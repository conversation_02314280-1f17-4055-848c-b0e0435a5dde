#include "aephasechartdatagenerator.h"
#include "chartjsonconstants.h"
#include "configservice.h"
#include "log.h"

AePhaseChartDataGenerator::AePhaseChartDataGenerator(const AERecord& aeRecord)
    : m_aeRecord(aeRecord)
{
}

QJsonObject AePhaseChartDataGenerator::generateChartData()
{
    QJsonObject chartJson;

    chartJson.insert(ChartJsonConstants::kTitleKey, "");

    chartJson.insert(ChartJsonConstants::kTrigger<PERSON>ey, generateTriggerInfo());
    chartJson.insert(ChartJsonConstants::kAxisInfoKey, generateAxisInfo());
    chartJson.insert(ChartJsonConstants::kSeriesKey, generateSeriesData());

    return chartJson;
}

QJsonObject AePhaseChartDataGenerator::generateAxisInfo()
{
    QJsonObject axisInfo;

    axisInfo.insert(ChartJsonConstants::kX<PERSON><PERSON><PERSON><PERSON><PERSON>, ChartJsonConstants::kAePhaseDesc);

    axisInfo.insert(ChartJsonConstants::kXRangeMinKey, 0);
    axisInfo.insert(ChartJsonConstants::kXRangeMaxKey, 360);
    axisInfo.insert(ChartJsonConstants::kXUnitKey, ChartJsonConstants::kAePhaseUnit);

    axisInfo.insert(ChartJsonConstants::kYDescKey, ChartJsonConstants::kAeAmplitudeDesc);
    axisInfo.insert(ChartJsonConstants::kYRangeMinKey, m_aeRecord.fAmpLowerLimit);
    axisInfo.insert(ChartJsonConstants::kYRangeMaxKey, m_aeRecord.fAmpUpperLimit);
    axisInfo.insert(ChartJsonConstants::kYUnitKey, ConfigService::instance().getDataUnitString(m_aeRecord.eAmpUnit));

    return axisInfo;
}

QJsonObject AePhaseChartDataGenerator::generateTriggerInfo()
{
    QJsonObject trigger;

    trigger.insert(ChartJsonConstants::kDescKey, ChartJsonConstants::kAeTriggerDesc);
    trigger.insert(ChartJsonConstants::kValueKey, m_aeRecord.triggerAmplitude);
    trigger.insert(ChartJsonConstants::kColorKey, "");

    return trigger;
}

QJsonArray AePhaseChartDataGenerator::generateSeriesData()  
{
    QJsonArray seriesArray;
    QJsonObject seriesObject;
    QJsonArray dataList;

    const int dataSize = m_aeRecord.phaseArrayData.size();
    if (dataSize % 2 != 0)
    {
        logError(QString("generateAePhaseSeriesData: dataSize error: %1").arg(dataSize));
        return seriesArray;
    }

    for (int i = 0; i < dataSize; i += 2)
    {
        float phase = m_aeRecord.phaseArrayData[i];
        float amplitude = m_aeRecord.phaseArrayData[i + 1];

        QJsonArray pointArray;
        pointArray.append(phase);
        pointArray.append(amplitude);

        // logTrace(QString("generateAePhaseSeriesData: pointArray: %1, %2").arg(phase).arg(amplitude));
        
        dataList.append(pointArray);
    }

    seriesObject.insert(ChartJsonConstants::kDataListKey, dataList);
    seriesArray.append(seriesObject);

    return seriesArray;
}

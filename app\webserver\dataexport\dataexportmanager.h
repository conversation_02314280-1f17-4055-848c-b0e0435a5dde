/*
* Copyright (c) 2016.12，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：dataexportmanager.h
*
* 初始版本：1.0
* 作者：xw
* 创建日期：2021年2月2日
* 摘要：备份数据导出管理类，仅linux平台下功能可用
*
*/

#ifndef DATAEXPORTMANAGER_H
#define DATAEXPORTMANAGER_H

#include <atomic>

#include <QObject>
#include <QSharedPointer>
#include <QMutex>

class ExportSize;

class DataExportManager : public QObject
{
    Q_OBJECT
public:
    explicit DataExportManager(QObject *parent = 0);

    ~DataExportManager();

    /*************************************************
    功能: 取消数据导出
    *************************************************************/
    void cancelExport();

signals:
    /*************************************************
    功能: 发送ws信息
    *************************************************************/
    void sigSendData(const QString & message);

    /*************************************************
    功能: 发送压缩结束
    *************************************************************/
    void sigCompressFinish();

public slots:
    /*************************************************
    * 输入参数：listDir 文件夹、文件列表
    功能: 数据导出槽函数
    *************************************************************/
    void onDataExport(const QList<QString> &listDir);

private:
    /*************************************************
    * 输入参数：listDir 文件夹、文件列表
    功能: 非法文件夹、文件过滤
    *************************************************************/
    void filterDir(const QList<QString> &listDir);

    /*************************************************
    功能: 发送数据大小ws信息
    *************************************************************/
    void sendDataSize(const int size);

    /*************************************************
    功能: 发送开始压缩ws信息
    *************************************************************/
    void sendBeginCompress();

    /*************************************************
    功能: 发送压缩结束ws信息
    *************************************************************/
    void sendEndCompress();

private:
    QThread *m_pThread; // 线程
    QList<QString> m_listDataDir; // 文件夹、文件列表
    QSharedPointer<ExportSize> m_spExportSize; // 数据大小计算对象
    std::atomic<bool> m_bCancel; // 导出操作是否被取消
};

#endif // DATAEXPORTMANAGER_H

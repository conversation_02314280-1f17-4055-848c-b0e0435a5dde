/*
* Copyright (c) 2016.12，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：dataexportmanager.h
*
* 初始版本：1.0
* 作者：xw
* 创建日期：2021年2月2日
* 摘要：备份数据导出管理类，仅linux平台下功能可用
*
*/

#ifndef DATAEXPORTMANAGER_H
#define DATAEXPORTMANAGER_H

#include <atomic>

#include <QObject>
#include <QSharedPointer>
#include <QMutex>

class ExportSize;

class DataExportManager : public QObject
{
    Q_OBJECT
public:
    explicit DataExportManager(QObject *parent = 0);

    ~DataExportManager();

    void cancelExport();

signals:
    void sigSendData(const QString & message);

public slots:
    void onDataExport(const QList<QString> &listDir);

private:
    void filterDir(const QList<QString> &listDir);

    void sendDataSize(const int size);

    void sendBeginCompress();

    void sendEndCompress();

    void clear();

private:
    QThread *m_pThread;
    QList<QString> m_listDataDir;
    QSharedPointer<ExportSize> m_spExportSize;
    std::atomic<bool> m_bCancel;
};

#endif // DATAEXPORTMANAGER_H
